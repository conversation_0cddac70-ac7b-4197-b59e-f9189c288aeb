{"name": "atotal-vagyon-root", "version": "0.0.0", "private": true, "type": "module", "scripts": {"format": "git ls-files | egrep '\\.(json|js|jsx|css|ts|tsx|vue|mjs|cjs)$' | grep --invert-match package.json | xargs pnpm exec prettier --write", "format-website": "prettier --write \"apps/website/src/**/*.{js,jsx,ts,tsx,css,scss,md,html,json,yml}\"", "remove-unused-imports": "eslint --fix \"apps/**/*.{ts,tsx}\" --rule 'unused-imports: [error, double]'", "lint": "eslint --fix \"apps/**/*.{ts,tsx}\"", "prepare": "husky install", "precommit": "lint-staged", "e2e-test": "playwright test", "build": "turbo build --cache-dir=.turbo", "reset": "sudo git clean -Xdf && docker compose up npm_installer && pnpm run build"}, "devDependencies": {"@playwright/test": "^1.39.0", "eslint": "^8.52.0", "eslint-config-atotal": "workspace:*", "eslint-import-resolver-typescript": "^4.4.1", "husky": "^9.1.7", "lint-staged": "^15.5.1", "playwright": "^1.39.0", "prettier": "^2.8.8"}, "engines": {"npm": ">=7.0.0", "node": ">=14.0.0"}, "packageManager": "pnpm@9.6.0", "dependencies": {"turbo": "^2.5.1"}, "husky": {"hooks": {"pre-commit": "npm run lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix"]}, "pnpm": {"overrides": {"rollup": "npm:@rollup/wasm-node@4.38.0"}, "supportedArchitectures": {"os": ["linux"], "cpu": ["x64"], "libc": ["musl"]}}}
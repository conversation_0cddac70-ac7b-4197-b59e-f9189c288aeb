import { compiled } from "vite-plugin-compiled-react";
import react from "@vitejs/plugin-react";
import vike from "vike/plugin";
import { defineConfig } from "vite";
import { checker } from "vite-plugin-checker";
import { telefunc } from "telefunc/vite";
import Inspect from 'vite-plugin-inspect'


export default defineConfig({
  server: {
    port: Number(process.env.PORT),
    host: true,
    allowedHosts: true,
  },
  resolve: {
    alias: {
      "#root": __dirname.replaceAll("\\", "/"),
    },
  },
  ssr: { noExternal: ["react-icons"] },
  plugins: [
    react(),
    compiled({ extract: true }),
    vike(),
    telefunc(),
    checker({ typescript: true }),
    Inspect()
  ],
  optimizeDeps: {
    exclude: ["@node-rs/argon2"],
  },
  assetsInclude: ["**/*.txt"],
});

{"name": "website", "version": "0.0.3", "private": true, "type": "module", "scripts": {"dev": "npx prisma db push --force-reset && vike dev", "build": "rimraf dist && vike build", "start": "npx prisma migrate deploy && cross-env NODE_ENV=production node dist/server/index.js", "dev-start": "npx prisma db push --force-reset && cross-env NODE_ENV=development node dist/server/index.js", "prepare": "prisma generate"}, "dependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@aws-sdk/client-ses": "^3.687.0", "@fontsource-variable/inter": "^5.1.0", "@fontsource/source-sans-pro": "^5.0.8", "@hookform/resolvers": "^3.3.4", "@mantine/core": "7.17.0", "@mantine/dates": "7.17.0", "@mantine/hooks": "7.17.0", "@mantine/modals": "7.17.0", "@mantine/notifications": "7.17.0", "@mantine/tiptap": "7.17.0", "@myfunc/prisma-transactional": "^0.3.0", "@node-rs/argon2": "^2.0.2", "@node-rs/argon2-darwin-arm64": "^2.0.2", "@node-rs/argon2-win32-x64-msvc": "^2.0.2", "@prisma/client": "^5.16.0", "@tanstack/react-query": "^5.60.6", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-subscript": "^2.11.5", "@tiptap/extension-superscript": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@uiw/react-json-view": "2.0.0-alpha.30", "ag-grid-community": "^32.3.3", "ag-grid-react": "^32.3.3", "cookie-parser": "^1.4.6", "cookie-session": "^2.1.0", "cron": "^4.3.0", "cron-validator": "^1.3.1", "dayjs": "^1.11.10", "express": "^4.18.2", "express-ws": "5.0.2", "http-proxy-middleware": "^3.0.3", "i18next": "^23.8.1", "immer": "^10.0.3", "ky": "^1.4.0", "lightweight-charts": "^4.2.0", "lodash-es": "4.17.21", "mantine-datatable": "7.14.5", "nodemailer": "^6.9.16", "openapi-sampler": "^1.5.1", "openapi-types": "^12.1.3", "piscina": "^5.0.0", "plotly.js": "^2.33.0", "prismjs": "^1.29.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.1", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-plotly.js": "^2.6.0", "sanitize-html": "^2.14.0", "telefunc": "0.2.5", "temp-dir": "^3.0.0", "uuid": "^11.1.0", "vike": "0.4.228", "vike-react": "0.6.1", "vike-react-query": "0.1.3", "vike-react-zustand": "0.0.2", "vike-server": "1.0.15", "ws": "^8.18.0", "xlsx": "^0.18.5", "xml-js": "^1.6.11", "zod": "^3.22.4", "zustand": "5.0.3", "zustand-querystring": "0.2.0"}, "devDependencies": {"@compiled/react": "0.18.4", "@hookform/devtools": "^4.3.3", "@tanstack/react-query-devtools": "5.76.1", "@types/cookie-parser": "^1.4.6", "@types/cookie-session": "^2.0.49", "@types/express": "4.17.17", "@types/express-serve-static-core": "4.19.5", "@types/express-ws": "^3.0.5", "@types/lodash-es": "4.17.12", "@types/node": "^20.4.10", "@types/plotly.js": "^2.29.5", "@types/prismjs": "^1.26.5", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-plotly.js": "^2.6.3", "@types/sanitize-html": "^2.13.0", "@types/ws": "^8.5.14", "@vitejs/plugin-react": "^4.4.1", "cross-env": "^7.0.3", "devalue": "^4.3.2", "eslint": "^8.53.0", "eslint-config-atotal": "workspace:*", "postcss-preset-mantine": "^1.12.3", "prisma": "^5.16.0", "rimraf": "^5.0.5", "tsconfig-atotal": "workspace:*", "type-fest": "^4.20.1", "typescript": "^5.8.3", "vite": "^6.3.2", "vite-plugin-checker": "^0.9.1", "vite-plugin-compiled-react": "1.3.1", "vite-plugin-inspect": "^11.0.1"}, "optionalDependencies": {"bufferutil": "^4.0.9"}}
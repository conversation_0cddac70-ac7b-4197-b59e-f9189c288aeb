/**
 * Date Range Filter Component
 *
 * Component for selecting date ranges.
 */

import { useState } from "react";
import { Box, Button, Divider, Group, Paper, Stack } from "@mantine/core";
import { DatePicker } from "@mantine/dates";
import dayjs from "dayjs";
import { formatDate, formatDateRange } from "../utils";
import type { DatePreset } from "../types";

interface DateRangeFilterProps {
  /** Current start date */
  startDate: Date | null;
  /** Current end date */
  endDate: Date | null;
  /** Whether this is a single date filter */
  isSingleDate?: boolean;
  /** Callback when dates are applied */
  onApply: (startDate: Date | null, endDate: Date | null) => void;
  /** Callback when dates are cleared */
  onClear: () => void;
  /** Callback when the component is closed */
  onClose: () => void;
  /** Callback when a preset is applied */
  onApplyPreset: (preset: DatePreset) => void;
}

/**
 * Date Range Filter Component
 */
export function DateRangeFilter({
  startDate,
  endDate,
  isSingleDate = false,
  onApply,
  onClear,
  onClose,
  onApplyPreset,
}: DateRangeFilterProps) {
  // Local state for the date picker
  const [localStartDate, setLocalStartDate] = useState<Date | null>(startDate);
  const [localEndDate, setLocalEndDate] = useState<Date | null>(endDate);

  // Handle applying the date range
  const handleApply = () => {
    onApply(localStartDate, localEndDate);
    onClose();
  };

  // Handle clearing the date range
  const handleClear = () => {
    onClear();
    onClose();
  };

  // Handle applying a preset
  const handlePreset = (preset: DatePreset) => {
    onApplyPreset(preset);
    onClose();
  };

  // Handle single date selection
  const handleSingleDateChange = (date: Date | null) => {
    if (date) {
      setLocalStartDate(date);
      setLocalEndDate(dayjs(date).add(1, "day").toDate());
    } else {
      setLocalStartDate(null);
      setLocalEndDate(null);
    }
  };

  return (
    <Paper
      shadow="md"
      withBorder
      style={{
        zIndex: 100,
        display: "flex",
      }}
    >
      {!isSingleDate && (
        <Stack
          p="md"
          css={{
            width: 150,
          }}
        >
          <Button
            variant={
              localStartDate &&
              dayjs(localStartDate).isSame(dayjs().startOf("day"))
                ? "filled"
                : "outline"
            }
            onClick={() => handlePreset("today")}
          >
            Today
          </Button>
          <Button variant="outline" onClick={() => handlePreset("yesterday")}>
            Yesterday
          </Button>
          <Button variant="outline" onClick={() => handlePreset("last_7_days")}>
            Last 7 days
          </Button>
          <Button
            variant="outline"
            onClick={() => handlePreset("last_30_days")}
          >
            Last 30 days
          </Button>
          <Button variant="outline" onClick={() => handlePreset("this_month")}>
            This month
          </Button>
          <Button variant="outline" onClick={() => handlePreset("last_month")}>
            Last month
          </Button>
        </Stack>
      )}

      <Divider orientation="vertical" />

      <Box style={{ minWidth: 300 }}>
        {isSingleDate ? (
          <DatePicker
            p="md"
            value={localStartDate}
            onChange={handleSingleDateChange}
          />
        ) : (
          <DatePicker
            p="md"
            type="range"
            value={[localStartDate, localEndDate]}
            onChange={([start, end]) => {
              setLocalStartDate(start);
              setLocalEndDate(end);
            }}
            allowSingleDateInRange
          />
        )}

        <Divider />

        <Group align="center" p="xs">
          <Button variant="outline" onClick={handleClear}>
            Clear
          </Button>
          <Button onClick={handleApply}>Apply</Button>
        </Group>
      </Box>
    </Paper>
  );
}

/**
 * Format a date range for display
 */
export function formatDateRangeForDisplay(
  startDate: Date | null,
  endDate: Date | null,
  isSingleDate: boolean
): string {
  if (!startDate) return "Select date";

  if (isSingleDate) {
    return formatDate(startDate);
  }

  return formatDateRange(startDate, endDate);
}

/**
 * Filter Bar Component
 *
 * Main component for managing filters for an operation.
 */

import { useState } from "react";
import { Button, Group, Popover, Text, Tooltip } from "@mantine/core";
import { IoCloseSharp } from "react-icons/io5";
import { DateRangeFilter, formatDateRangeForDisplay } from "./DateRangeFilter";
import { FilterPanel } from "./FilterPanel";
import type { GroupProps } from "@mantine/core";
import { useOperationFilters } from "../store";
import { DownloadSheetComboBox } from "#root/src/components/DownloadSheetComboBox";
import {
  constructUrlForCsv,
  constructUrlForXlsx,
  useOperation,
} from "#root/src/openapi/openapi.query";
import { useAuth } from "#root/src/api/auth/auth.query";
import { modals } from "@mantine/modals";
import { navigate } from "vike/client/router";
import { CreateEmbeddingModal } from "#root/src/components/EmbeddingModals";

interface ActionBarProps extends GroupProps {
  /** Operation ID */
  operationId: string;
  title?: string;
}

export function ActionBar({ operationId, title, ...rest }: ActionBarProps) {
  // Get filter state and actions
  const {
    regularFilters,
    startDate,
    endDate,
    isSingleDate,
    regularFilterDefinitions,
    setDateRange,
    applyDatePreset,
    clearRegularFilters,
    clearDateFilters,
    params,
  } = useOperationFilters(operationId);
  const operation = useOperation({ operationId });
  const { profile } = useAuth();

  const showAuthModal = () => {
    modals.openConfirmModal({
      title: "Authentication Required",
      centered: true,
      children: <Text size="sm">Sign up to access these functionalities</Text>,
      labels: { confirm: "Sign up", cancel: "Cancel" },
      onConfirm: () => {
        navigate("/signup");
      },
    });
  };

  const handleEmbedClick = () => {
    if (!profile) {
      showAuthModal();
      return;
    }

    const id = modals.open({
      centered: true,
      title: "Create Embedding",
      size: "xl",
      children: (
        <CreateEmbeddingModal
          onClose={() => modals.close(id)}
          dataSetOperationId={operationId}
          apiParams={params.filter ? params.filter.join() : ""}
        />
      ),
    });
  };

  const handleDownloadClick = (val, combobox) => {
    if (!profile) {
      showAuthModal();
      return;
    }

    let tabUrl = constructUrlForCsv(operation, params);
    if (val === "xlsx") {
      tabUrl = constructUrlForXlsx(operation, params);
    }
    const anchor = document.createElement("a");
    anchor.href = tabUrl;
    anchor.download = `results.${val}`;
    anchor.click();
    anchor.remove();
    combobox.closeDropdown();
  };
  // State for popovers
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  const [filterPanelOpen, setFilterPanelOpen] = useState(false);

  // Count of regular filters
  const regularFilterCount = regularFilters.length;

  return (
    <Group gap="md" {...rest}>
      <Text size="lg" fw={500}>
        {title}
      </Text>
      <Tooltip label={"Create a new embedding from the chart / table above"}>
        <Button ml="auto" onClick={handleEmbedClick} variant="light">
          {"Embed"}
        </Button>
      </Tooltip>
      <DownloadSheetComboBox onClick={handleDownloadClick} />

      {/* Date filter */}
      <Popover
        opened={datePickerOpen}
        onChange={setDatePickerOpen}
        position="bottom"
        shadow="md"
      >
        <Popover.Target>
          <Button
            // variant="outline"
            onClick={() => setDatePickerOpen(true)}
            rightSection={
              startDate ? (
                <IoCloseSharp
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    clearDateFilters();
                  }}
                />
              ) : undefined
            }
          >
            {startDate ? (
              <Text>
                {formatDateRangeForDisplay(startDate, endDate, isSingleDate)}
              </Text>
            ) : (
              <Text>Date range</Text>
            )}
          </Button>
        </Popover.Target>

        <Popover.Dropdown p={0}>
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            isSingleDate={isSingleDate}
            onApply={setDateRange}
            onClear={() => clearDateFilters()}
            onClose={() => setDatePickerOpen(false)}
            onApplyPreset={applyDatePreset}
          />
        </Popover.Dropdown>
      </Popover>

      {/* Regular filters */}
      <Popover
        opened={filterPanelOpen}
        onChange={setFilterPanelOpen}
        position="bottom"
        shadow="md"
      >
        <Popover.Target>
          <Button
            variant="filled"
            onClick={() => setFilterPanelOpen(true)}
            rightSection={
              regularFilterCount > 0 ? (
                <IoCloseSharp
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    // Clear only regular filters
                    clearRegularFilters();
                  }}
                  color="white"
                />
              ) : undefined
            }
          >
            {regularFilterCount > 0 ? (
              <Text>Filters ({regularFilterCount})</Text>
            ) : (
              <Text>Add filter</Text>
            )}
          </Button>
        </Popover.Target>

        <Popover.Dropdown p={0}>
          <FilterPanel
            operationId={operationId}
            filterDefinitions={regularFilterDefinitions}
            onClose={() => setFilterPanelOpen(false)}
          />
        </Popover.Dropdown>
      </Popover>
    </Group>
  );
}

/**
 * Filter Store
 *
 * Zustand store for managing filter state.
 */

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { devtools } from "zustand/middleware";

import type {
  FilterStore,
  Filter,
  FilterOperator,
  DatePreset,
  FilterState,
} from "./types";
import { EMPTY_FILTER_STATE } from "./types";
import {
  serializeFilter,
  getDateRangeFromPreset,
  createDateFilters,
} from "./utils";
import { useOperation } from "../openapi/openapi.query";
import { useEffect, useMemo } from "react";
import { DATETIME_FIELD_NAME } from "../constants";
import {
  getDefaultDateRange,
  getDefaultFilters,
  isOperationSingleDate,
} from "./operationSpecifics";
import { assert } from "../shared/assert";
import type { Operation } from "../openapi/openapi";

/**
 * Helper function to update filters and serialize them
 */
const updateFiltersState = (
  filters: Filter[],
  state: { filtersByOperation: Record<string, FilterState> },
  operationId: string
) => {
  const serialized = filters
    .map((filter) => serializeFilter(filter, operationId))
    .filter(Boolean) as string[];
  state.filtersByOperation[operationId] = { filters, serialized };
};

/**
 * Create the filter store
 */
export const useFilterStore = create<FilterStore>()(
  devtools(
    immer((set, get) => ({
      // Filter states by operation ID
      filtersByOperation: {},

      // Pending filters for the filter panel
      pendingFilters: {},

      initializeFilters(operation) {
        const state = get();
        assert(operation.operationId);
        if (state.filtersByOperation[operation.operationId]) {
          return;
        }
        const dateField = getDateField(operation);
        const [startDate, endDate] = getDefaultDateRange(operation.operationId);
        const nonDateFilters = getDefaultFilters(operation.operationId);
        const dateFilters = createDateFilters(dateField, startDate, endDate);
        const newFilters = [...nonDateFilters, ...dateFilters];

        set((state) => {
          assert(operation.operationId);
          updateFiltersState(newFilters, state, operation.operationId);
        });
      },

      /**
       * Initialize pending filters for an operation
       */
      initPendingFilters: (operationId: string) => {
        const currentState =
          get().filtersByOperation[operationId] || EMPTY_FILTER_STATE;
        const regularFilters = currentState.filters.filter(
          (f) => !f.isDateFilter
        );

        set((state) => {
          state.pendingFilters[operationId] = [
            ...regularFilters.map((filter, index) => ({
              field: filter.field,
              operator: filter.operator,
              value: String(filter.value),
              id: Date.now() + index,
            })),
            {
              field: null,
              operator: null,
              value: "",
              id: Date.now() + regularFilters.length,
            },
          ];
        });
      },

      /**
       * Update a pending filter
       */
      updatePendingFilter: (
        operationId: string,
        index: number,
        field: string | null,
        operator: FilterOperator | null,
        value: string
      ) => {
        set((state) => {
          // Initialize if needed
          if (!state.pendingFilters[operationId]) {
            state.pendingFilters[operationId] = [
              { field: null, operator: null, value: "", id: Date.now() },
            ];
          }

          const pendingFilters = [...state.pendingFilters[operationId]];
          pendingFilters[index] = {
            ...pendingFilters[index],
            field,
            operator,
            value,
          };

          // Add a new empty filter if the last one is being filled
          if (
            index === pendingFilters.length - 1 &&
            (field || operator || value)
          ) {
            pendingFilters.push({
              field: null,
              operator: null,
              value: "",
              id: Date.now(),
            });
          }

          // Remove empty filters except the last one
          if (
            !field &&
            !operator &&
            !value &&
            index !== pendingFilters.length - 1
          ) {
            state.pendingFilters[operationId] = get().removePendingFilter(
              operationId,
              index
            );
            return;
          }

          state.pendingFilters[operationId] = pendingFilters;
        });
      },

      /**
       * Remove a pending filter
       */
      removePendingFilter: (operationId: string, index: number) => {
        const pendingFilters = get().pendingFilters[operationId] || [];
        const newPendingFilters = pendingFilters.filter((_, i) => i !== index);

        // Ensure we always have at least one empty filter at the end
        if (
          newPendingFilters.length === 0 ||
          newPendingFilters[newPendingFilters.length - 1].field !== null
        ) {
          newPendingFilters.push({
            field: null,
            operator: null,
            value: "",
            id: Date.now(),
          });
        }

        set((state) => {
          state.pendingFilters[operationId] = newPendingFilters;
        });

        return newPendingFilters;
      },

      /**
       * Apply pending filters
       */
      applyPendingFilters: (operationId: string) => {
        const pendingFilters = get().pendingFilters[operationId] || [];
        const currentState =
          get().filtersByOperation[operationId] || EMPTY_FILTER_STATE;

        // Preserve date filters, apply regular filters from pending state
        const dateFilters = currentState.filters.filter((f) => f.isDateFilter);
        const validPendingFilters = pendingFilters
          .filter((f) => f.field && f.operator && f.value)
          .map((f) => ({
            field: f.field!,
            operator: f.operator!,
            value: f.value,
            isDateFilter: false,
          }));

        const newFilters = [...dateFilters, ...validPendingFilters];

        set((state) => {
          updateFiltersState(newFilters, state, operationId);
        });
      },

      /**
       * Reset pending filters to match current filters
       */
      resetPendingFilters: (operationId) => {
        get().initPendingFilters(operationId);
      },

      clearDateFilters: (operation) => {
        const state = get();
        assert(operation.operationId);
        const dateField = getDateField(operation);
        const [startDate, endDate] = getDefaultDateRange(operation.operationId);
        const nonDateFilters = state.filtersByOperation[
          operation.operationId
        ].filters.filter((f) => !f.isDateFilter);
        const dateFilters = createDateFilters(dateField, startDate, endDate);
        const newFilters = [...nonDateFilters, ...dateFilters];

        set((state) => {
          assert(operation.operationId);
          updateFiltersState(newFilters, state, operation.operationId);
        });
      },

      clearRegularFilters: (operation) => {
        const state = get();
        assert(operation.operationId);
        const nonDateFilters = getDefaultFilters(operation.operationId);
        const dateFilters = state.filtersByOperation[
          operation.operationId
        ].filters.filter((f) => f.isDateFilter);
        const newFilters = [...nonDateFilters, ...dateFilters];

        set((state) => {
          assert(operation.operationId);
          updateFiltersState(newFilters, state, operation.operationId);
        });
      },

      /**
       * Set a date range
       */
      setDateRange: (
        operationId: string,
        field: string,
        startDate: Date | null,
        endDate: Date | null
      ) => {
        set((state) => {
          const currentState =
            state.filtersByOperation[operationId] || EMPTY_FILTER_STATE;

          // Keep non-date filters and add new date filters
          const nonDateFilters = currentState.filters.filter(
            (f) => !(f.isDateFilter && f.field === field)
          );
          const dateFilters = createDateFilters(field, startDate, endDate);
          const newFilters = [...nonDateFilters, ...dateFilters];

          updateFiltersState(newFilters, state, operationId);
        });
      },

      /**
       * Apply a date preset
       */
      applyDatePreset: (
        operationId: string,
        field: string,
        preset: DatePreset
      ) => {
        const [startDate, endDate] = getDateRangeFromPreset(preset);
        get().setDateRange(operationId, field, startDate, endDate);
      },
    }))
  )
);

function getDateField(operation: Operation) {
  return operation?.plotConfig?.x || DATETIME_FIELD_NAME;
}

export function useOperationFields(operationId: string) {
  const operation = useOperation({ operationId });
  const dateField = getDateField(operation);
  // Get all filter definitions
  const filterDefinitions = useMemo(() => {
    if (!operation?.filters) return [];
    return operation.filters.map((filter) => ({
      name: filter.name,
      title: filter.title,
      operator: filter.op,
      field: filter.column,
    }));
  }, [operation]);

  // Get date filter definitions
  const dateFilterDefinitions = useMemo(() => {
    return filterDefinitions.filter((filter) => filter.field === dateField);
  }, [filterDefinitions, dateField]);

  // Get regular filter definitions
  const regularFilterDefinitions = useMemo(() => {
    return filterDefinitions.filter((filter) => filter.field !== dateField);
  }, [filterDefinitions, dateField]);

  return {
    filterDefinitions,
    dateFilterDefinitions,
    regularFilterDefinitions,
    dateField,
  };
}

/**
 * Hook for using filters for a specific operation
 */
export function useOperationFilters(operationId: string) {
  const store = useFilterStore();
  const operation = useOperation({ operationId });
  useMemo(() => {
    // This can fragment the state
    store.initializeFilters(operation);
  }, [operation, store]);

  const {
    filterDefinitions,
    dateFilterDefinitions,
    regularFilterDefinitions,
    dateField,
  } = useOperationFields(operationId);

  const filterState =
    store.filtersByOperation[operationId] || EMPTY_FILTER_STATE;

  const pendingFilters = store.pendingFilters[operationId] || [];

  // Check if this operation uses a single date
  const isSingleDate = isOperationSingleDate(operationId);

  // Get date filters
  const dateFilters = useMemo(
    () => filterState.filters.filter((f) => f.isDateFilter),
    [filterState.filters]
  );

  // Get regular filters
  const regularFilters = useMemo(
    () => filterState.filters.filter((f) => !f.isDateFilter),
    [filterState.filters]
  );

  // Get start and end dates
  const [startDate, endDate] = useMemo(() => {
    const startFilter = dateFilters.find(
      (f) => f.field === dateField && f.operator === "gte"
    );

    const endFilter = dateFilters.find(
      (f) => f.field === dateField && f.operator === "lt"
    );

    return [
      startFilter?.value instanceof Date ? startFilter.value : null,
      endFilter?.value instanceof Date ? endFilter.value : null,
    ];
  }, [dateFilters, dateField]);

  const setDateRange = (startDate: Date | null, endDate: Date | null) =>
    store.setDateRange(operationId, dateField, startDate, endDate);

  const applyDatePreset = (preset: DatePreset) =>
    store.applyDatePreset(operationId, dateField, preset);

  return {
    // State
    filters: filterState.filters,
    serialized: filterState.serialized,
    hasFilters: filterState.filters.length > 0,
    dateFilters,
    regularFilters,
    pendingFilters,

    params: useMemo(() => {
      return {
        ...(filterState.filters.length > 0 && {
          filter: filterState.serialized,
        }),
      };
    }, [filterState.filters, filterState.serialized]),

    // Date filter state
    dateField,
    startDate,
    endDate,
    isSingleDate,

    // Filter definitions
    filterDefinitions,
    dateFilterDefinitions,
    regularFilterDefinitions,

    // Actions bound to this operation
    clearDateFilters: () => store.clearDateFilters(operation),
    clearRegularFilters: () => store.clearRegularFilters(operation),
    setDateRange,
    applyDatePreset,

    // Pending filters UI state
    initPendingFilters: () => store.initPendingFilters(operationId),
    updatePendingFilter: (
      index: number,
      field: string | null,
      operator: FilterOperator | null,
      value: string
    ) => store.updatePendingFilter(operationId, index, field, operator, value),
    removePendingFilter: (index: number) =>
      store.removePendingFilter(operationId, index),
    applyPendingFilters: () => store.applyPendingFilters(operationId),
  };
}

/**
 * Operation-Specific Logic
 *
 * This file contains all operation-specific logic and special cases.
 * Centralizing this logic makes it easier to find and modify special cases.
 *
 * When adding new operations or special cases:
 * 1. Add them to the appropriate function in this file
 * 2. Keep all operation-specific logic in this file, not scattered across the codebase
 * 3. Use clear, descriptive comments to explain the purpose of each special case
 */

import dayjs from "dayjs";
import type { Filter, FilterOperator } from "./types";
import { getPublicGlobalConfig } from "../shared/globalConfig";

/**
 * Get default filters for an operation
 */
export function getDefaultFilters(operationId: string): Filter[] {
  switch (operationId) {
    case "DAM_Aggregated_curve_v1":
      return [
        {
          field: "Hour",
          operator: "eq",
          value: "1",
        },
      ];

    case "IDA_Aggregated_curve_v1":
      return [
        {
          field: "QuarterHour",
          operator: "eq",
          value: "1",
        },
        {
          field: "AuctionID",
          operator: "eq",
          value: "1",
        },
      ];

    default:
      return [];
  }
}

/**
 * Format a date for a specific operation, field, and operator
 *
 * This function handles special cases for date formatting.
 * Add new special cases by adding conditions to this function.
 */
export function formatDateForOperation(
  date: Date,
  field: string,
  operator: FilterOperator,
  operationId?: string
): string {
  // Special case for IDC_v1 operation and ExecutionTime field
  if (operationId === "IDC_v1" && field === "ExecutionTime") {
    if (operator === "gte") {
      // Subtract 9 hours
      // IDC_v1 date filter is a single day filter
      // Trade opens at 15:00 every day, for the next day
      return dayjs(date).subtract(9, "hour").format("YYYY-MM-DDTHH:mm:ss");
    }
  }

  // Default case - just format as YYYY-MM-DD
  return dayjs(date).format("YYYY-MM-DD");
}

/**
 * Check if an operation uses a single date filter
 */
export function isOperationSingleDate(operationId: string): boolean {
  return [
    "DAM_Aggregated_curve_v1",
    "IDA_Aggregated_curve_v1",
    "IDC_v1",
  ].includes(operationId);
}

/**
 * Get default date range for an operation
 *
 * @param operationId The operation ID
 * @param now Optional dayjs instance to use (useful for development environment adjustments)
 */
export function getDefaultDateRange(operationId: string): [Date, Date] {
  const currentTime = getNow();

  switch (operationId) {
    case "DAM_Aggregated_curve_v1":
    case "IDA_Aggregated_curve_v1":
    case "IDC_v1":
    case "DAM_Aggregated_Trading_Data_v1":
      return [
        currentTime.startOf("day").toDate(),
        currentTime.add(1, "day").toDate(),
      ];

    case "IDA_QuarterHourly_v1":
    case "IDC_Hourly_v1":
    case "IDC_QuarterHourly_v1":
    case "DAM_IC_Flows_v1":
    case "IIP_v1":
    case "Imbalance_settlement_inputs_v1":
    case "IDA_MC_Flows_v1":
      return [
        currentTime.subtract(6, "days").toDate(),
        currentTime.add(1, "day").toDate(),
      ];

    default:
      return [currentTime.subtract(7, "days").toDate(), currentTime.toDate()];
  }
}

function getNow() {
  const { NODE_ENV } = getPublicGlobalConfig();
  const now = dayjs();
  if (NODE_ENV === "development") {
    return now.subtract(7, "days");
  }
  return now;
}

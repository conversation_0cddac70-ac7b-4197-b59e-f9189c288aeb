import {
  useCreateEmbedding,
  useEmbedding,
  useUpdateEmbedding,
} from "#root/src/api/embeddings/embeddings.query";
import {
  ActionIcon,
  Box,
  Button,
  Checkbox,
  CopyButton,
  Divider,
  Group,
  Stack,
  Text,
  TextInput,
  Tooltip,
} from "@mantine/core";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { FiCheck, FiCopy, FiTrash } from "react-icons/fi";
import { useAuth } from "../api/auth/auth.query";
import { assert } from "../shared/assert";
import { iframeForEmbedding } from "../utils/embedding";
import { DateFilterToggles } from "./DateFilterToggles";

type CreateEmbeddingModalProps = {
  onClose: () => void;
  dataSetOperationId: string;
  currentDateRange?: string;
  name?: string;
  includeTable?: boolean;
  includeChart?: boolean;
  apiParams?: string;
  allowedOrigins?: string[];
};

type FormValues = {
  name: string;
  allowedOrigins: string[];
  includeTable: boolean;
  includeChart: boolean;
  apiParams: string;
};

type UpdateEmbeddingModalProps = {
  onClose: () => void;
  embeddingId: string;
};
export const UpdateEmbeddingModal = ({
  onClose,
  embeddingId,
}: UpdateEmbeddingModalProps) => {
  const { data, error, isLoading } = useEmbedding(embeddingId);
  assert(data);
  const { control, handleSubmit, setValue, getValues, watch } =
    useForm<FormValues>({
      defaultValues: {
        allowedOrigins: data.EmbeddingsAllowedOrigin
          ? data.EmbeddingsAllowedOrigin.map((ao) => ao.origin)
          : ["*"],
        apiParams: data.apiParams || "",
        includeChart: data.includeChart,
        includeTable: data.includeTable,
        name: data.name,
      },
    });

  const updateEmbedding = useUpdateEmbedding();

  const onSubmit = async (data: FormValues) => {
    await updateEmbedding.mutateAsync({
      ...data,
      allowedOrigins: data.allowedOrigins.map((ao) => {
        return {
          origin: ao,
        };
      }),
      id: embeddingId,
    });
    onClose();
  };

  const addNewWhitelist = () => {
    const currentList = getValues("allowedOrigins");
    setValue("allowedOrigins", [...currentList, ""]);
  };

  const removeWhitelist = (index: number) => {
    const currentList = getValues("allowedOrigins");
    setValue(
      "allowedOrigins",
      currentList.filter((_, i) => i !== index)
    );
  };

  if (isLoading || error) {
    return null;
  }
  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack gap="md">
          <Controller
            name="name"
            control={control}
            rules={{ required: "Name is required" }}
            render={({ field, fieldState }) => (
              <TextInput
                label="Embedding Name"
                placeholder="My Embedding"
                error={fieldState.error?.message}
                required
                {...field}
              />
            )}
          />

          <Divider label="Display Options" labelPosition="center" />

          <Group grow>
            <Controller
              name="includeTable"
              control={control}
              render={({ field }) => (
                <Checkbox
                  label="Include Table"
                  checked={field.value}
                  onChange={(e) => field.onChange(e.currentTarget.checked)}
                />
              )}
            />
            <Controller
              name="includeChart"
              control={control}
              render={({ field }) => (
                <Checkbox
                  label="Include Chart"
                  checked={field.value}
                  onChange={(e) => field.onChange(e.currentTarget.checked)}
                />
              )}
            />
          </Group>

          <Controller
            name="apiParams"
            control={control}
            render={({ field }) => (
              <TextInput
                label="Filter String"
                placeholder="Filter parameters"
                {...field}
              />
            )}
          />

          <Box mt="xs">
            <DateFilterToggles
              apiParams={watch("apiParams")}
              onChange={(newApiParams) => setValue("apiParams", newApiParams)}
            />
          </Box>

          <Divider label="Allowed Origins" labelPosition="center" />

          <Text size="sm" c="dimmed">
            Specify which domains can embed this view. Use * for wildcards
            (e.g., *.example.com)
          </Text>

          <Controller
            name="allowedOrigins"
            control={control}
            render={({ field }) => (
              <Stack gap="xs">
                {field.value.map((origin, index) => (
                  <Group key={index}>
                    <TextInput
                      placeholder="*.example.com"
                      value={origin}
                      onChange={(e) => {
                        const newList = [...field.value];
                        newList[index] = e.currentTarget.value;
                        field.onChange(newList);
                      }}
                      style={{ flexGrow: 1 }}
                    />
                    {field.value.length > 1 && (
                      <ActionIcon
                        color="red"
                        onClick={() => removeWhitelist(index)}
                      >
                        <FiTrash size={16} />
                      </ActionIcon>
                    )}
                  </Group>
                ))}
                <Button
                  onClick={addNewWhitelist}
                  variant="outline"
                  leftSection={<FiCopy size={16} />}
                >
                  Add Another Domain
                </Button>
              </Stack>
            )}
          />

          <Group mt="md">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" loading={updateEmbedding.isPending}>
              Update Embedding
            </Button>
          </Group>
        </Stack>
      </form>
    </>
  );
};

export const CreateEmbeddingModal = ({
  onClose,
  dataSetOperationId,
  apiParams,
}: CreateEmbeddingModalProps) => {
  const [embedCode, setEmbedCode] = useState<string | null>(null);
  const { profile } = useAuth();
  const { control, handleSubmit, setValue, getValues, watch } =
    useForm<FormValues>({
      defaultValues: {
        apiParams: apiParams,
        includeChart: true,
        includeTable: true,
        allowedOrigins: ["*"],
        name: "",
      },
    });

  const createEmbedding = useCreateEmbedding();
  const addNewWhitelist = () => {
    const currentList = getValues("allowedOrigins");
    setValue("allowedOrigins", [...currentList, ""]);
  };

  // eslint-disable-next-line sonarjs/no-identical-functions
  const removeWhitelist = (index: number) => {
    const currentList = getValues("allowedOrigins");
    setValue(
      "allowedOrigins",
      currentList.filter((_, i) => i !== index)
    );
  };

  const onSubmit = async (data: FormValues) => {
    if (!profile) {
      // shouldn't really be possible to get here I think
      console.log("can't create embedding without valid login");
      return;
    }

    try {
      const result = await createEmbedding.mutateAsync({
        allowedOrigins: data.allowedOrigins,
        apiParams: data.apiParams,
        includeChart: data.includeChart,
        includeTable: data.includeTable,
        userId: profile.id,
        dataSetOperationId,
        name: data.name,
      });

      // Generate embed code
      if (result?.id) {
        const code = iframeForEmbedding(result.id);
        setEmbedCode(code);
      }
    } catch (error) {
      console.error("Error creating embedding:", error);
    }
  };

  return (
    <>
      {!embedCode ? (
        <form onSubmit={handleSubmit(onSubmit)}>
          <Stack gap="md">
            <Controller
              name="name"
              control={control}
              rules={{ required: "Name is required" }}
              render={({ field, fieldState }) => (
                <TextInput
                  label="Embedding Name"
                  placeholder="My Embedding"
                  error={fieldState.error?.message}
                  required
                  {...field}
                />
              )}
            />

            <Divider label="Display Options" labelPosition="center" />

            <Group grow>
              <Controller
                name="includeTable"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    label="Include Table"
                    checked={field.value}
                    onChange={(e) => field.onChange(e.currentTarget.checked)}
                  />
                )}
              />
              <Controller
                name="includeChart"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    label="Include Chart"
                    checked={field.value}
                    onChange={(e) => field.onChange(e.currentTarget.checked)}
                  />
                )}
              />
            </Group>

            <Controller
              name="apiParams"
              control={control}
              render={({ field }) => (
                <TextInput
                  label="Filter String"
                  placeholder="Filter parameters"
                  {...field}
                />
              )}
            />

            <Box mt="xs">
              <DateFilterToggles
                apiParams={watch("apiParams")}
                onChange={(newApiParams) => setValue("apiParams", newApiParams)}
              />
            </Box>

            <Divider label="Allowed Origins" labelPosition="center" />

            <Text size="sm" c="dimmed">
              Specify which domains can embed this view. Use * for wildcards
              (e.g., *.example.com)
            </Text>

            <Controller
              name="allowedOrigins"
              control={control}
              render={({ field }) => (
                <Stack gap="xs">
                  {field.value?.map((origin, index) => (
                    <Group key={index}>
                      <TextInput
                        placeholder="*.example.com"
                        value={origin}
                        onChange={(e) => {
                          const newList = [...field.value];
                          newList[index] = e.currentTarget.value;
                          field.onChange(newList);
                        }}
                        style={{ flexGrow: 1 }}
                      />
                      {field.value.length > 1 && (
                        <ActionIcon
                          color="red"
                          onClick={() => removeWhitelist(index)}
                        >
                          <FiTrash size={16} />
                        </ActionIcon>
                      )}
                    </Group>
                  ))}
                  <Button
                    onClick={addNewWhitelist}
                    variant="outline"
                    leftSection={<FiCopy size={16} />}
                  >
                    Add Another Domain
                  </Button>
                </Stack>
              )}
            />

            <Group mt="md">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" loading={createEmbedding.isPending}>
                Create Embedding
              </Button>
            </Group>
          </Stack>
        </form>
      ) : (
        <Stack gap="md">
          <Text fw={500}>Embedding created successfully!</Text>

          <Box
            p="md"
            style={(theme) => ({
              backgroundColor: theme.colors.gray[0],
              borderRadius: theme.radius.sm,
              fontFamily: "monospace",
              position: "relative",
              overflow: "auto",
            })}
          >
            <pre
              style={{
                margin: 0,
                whiteSpace: "pre-wrap",
                wordBreak: "break-all",
              }}
            >
              {embedCode}
            </pre>
            <CopyButton value={embedCode} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip
                  label={copied ? "Copied" : "Copy"}
                  withArrow
                  position="right"
                >
                  <ActionIcon
                    color={copied ? "teal" : "gray"}
                    style={{ position: "absolute", top: 5, right: 5 }}
                    onClick={copy}
                  >
                    {copied ? <FiCheck size={16} /> : <FiCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Box>

          <Group>
            <Button onClick={onClose}>Close</Button>
          </Group>
        </Stack>
      )}
    </>
  );
};

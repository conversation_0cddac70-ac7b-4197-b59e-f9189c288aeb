import type dayjs from "dayjs";
import { groupBy, pick, maxBy } from "lodash-es";
import { DataTable } from "mantine-datatable";
import { useApiData } from "../openapi/openapi.query";
import { useNow } from "#root/src/hooks/useNow";

function getTimeSlots(now: dayjs.Dayjs): string[] {
  const currentQH = now
    .tz("Europe/Budapest")
    .minute(Math.floor(now.minute() / 15) * 15)
    .second(0)
    .millisecond(0);

  return Array.from({ length: 10 }, (_, i) => {
    const start = currentQH.add(i * 15, "minute");
    const end = start.add(15, "minute");
    return `${start.format("YYYYMMDD HH:mm")}-${end.format("YYYYMMDD HH:mm")}`;
  });
}

const LatestTradesIDMComponent = () => {
  const now = useNow();
  const start = now
    .tz("Europe/Budapest")
    .subtract(12, "hours")
    .startOf("minute")
    .format("YYYY-MM-DDTHH:mm:ss")
    .toString();
  const end = now
    .tz("Europe/Budapest")
    .add(1, "hours")
    .startOf("minute")
    .format("YYYY-MM-DDTHH:mm:ss")
    .toString();
  const {
    data: { data },
  } = useApiData({
    operationId: "IDC_v1",
    params: {
      filter: `ExecutionTime__gte__${start}Z,ExecutionTime__lte__${end}Z`,
    },
  });

  const timeSlots = getTimeSlots(now);
  const grouped = groupBy(data, "Name");
  const filtered = pick(grouped, timeSlots);
  const rows = Object.entries(filtered).map(([name, group]) => ({
    Name: name,
    Price: maxBy(group, "ExecutionTime")?.Price || "No trades",
    id: name,
  }));

  return (
    <>
      <DataTable
        h="100%"
        columns={[
          {
            accessor: "Name",
            title: "Name",
          },
          {
            accessor: "Price",
            title: "Last Traded Price",
          },
        ]}
        records={rows}
      />
    </>
  );
};

export default LatestTradesIDMComponent;

import {
  ActionIcon,
  Button,
  Combobox,
  Group,
  MultiSelect,
  Popover,
  Stack,
  Text,
  TextInput,
} from "@mantine/core";
import { FiDownload } from "react-icons/fi";
import { useAppStore } from "#root/src/pages/store";
import { debounce } from "lodash-es";
import { useEffect, useMemo, useState } from "react";
import {
  PartnerStatus,
  partnerStatusOptions,
} from "#root/src/shared/constants.ts";

export const PartnerFilter = () => {
  const options = ["created", "approved"].map((item) => (
    <Combobox.Option value={item} key={item}>
      <Group>
        <ActionIcon>{<FiDownload />}</ActionIcon>
        {item}
      </Group>
    </Combobox.Option>
  ));

  const { name, setName, status, setStatus } = useAppStore(
    (state) => state.partnerFilter
  );

  const [localName, setLocalName] = useState<string>(name);

  const debouncedStateUpdate = useMemo(
    () => debounce((value: string) => setName(value), 500),
    [setName]
  );

  useEffect(() => {
    debouncedStateUpdate(localName);
    return () => {
      debouncedStateUpdate.cancel();
    };
  }, [localName, debouncedStateUpdate, name]);

  return (
    <Popover>
      <Popover.Target>
        <Button ml="auto" component="a" variant="filled">
          <Text>Filters</Text>
        </Button>
      </Popover.Target>
      <Popover.Dropdown>
        <Stack>
          <TextInput
            label="name"
            value={localName}
            onChange={(event) => setLocalName(event.currentTarget.value)}
          />
          <MultiSelect
            label="status"
            data={partnerStatusOptions}
            onChange={(value) => setStatus(value as PartnerStatus[])}
            value={status}
          ></MultiSelect>
        </Stack>
      </Popover.Dropdown>
    </Popover>
  );
};

export default PartnerFilter;

import { Text } from "@mantine/core";
import { useRef } from "react";
import ApiDataPlot from "./ApiDataPlotClientOnly";
import { ApiDataTable } from "./ApiDataTable";
import { isDisabledPlot } from "./plots/constants";
import type { ChartComponentProps } from "./plots/types";

export default function ApiDataPlotWithTableClientOnly({
  operationId,
  onReady,
  params,
  ChartComponentProps,
  includeChart = true,
  includeTable = true,
}: {
  operationId: string;
  onReady?: () => void;
  params?: {
    [key: string]: string | number | string[] | number[] | Date[];
  };
  ChartComponentProps?: Partial<ChartComponentProps>;
  includeTable?: boolean;
  includeChart?: boolean;
  includeEmbedding?: boolean;
}) {
  const disabledPlot = isDisabledPlot(operationId);
  const ready = useRef({ plot: !!disabledPlot, table: false });
  const shouldShowPlot = !disabledPlot && includeChart;
  const shouldShowTable = includeTable;

  // Wait for both components to fully render before showing them to prevent flickering
  const allReady =
    (!shouldShowPlot || ready.current.plot) &&
    (!shouldShowTable || ready.current.table);

  return (
    <div
      css={
        {
          // opacity: allReady ? 1 : 0,
        }
      }
    >
      {shouldShowPlot && (
        <>
          <div
            css={{
              height: 400,
              position: "relative",
            }}
          >
            <ApiDataPlot
              operationId={operationId}
              onReady={() => {
                ready.current.plot = true;
                if (
                  ready.current.plot &&
                  (ready.current.table || !shouldShowTable)
                ) {
                  onReady?.();
                }
              }}
              params={params}
              {...ChartComponentProps}
            />
          </div>
        </>
      )}

      {shouldShowTable && (
        <>
          <Text size="lg" fw={500} my="md">
            Table View
          </Text>
          <div
            css={{
              height: 400,
              position: "relative",
            }}
          >
            <ApiDataTable
              operationId={operationId}
              onReady={() => {
                ready.current.table = true;
                if (
                  ready.current.table &&
                  (ready.current.plot || !shouldShowPlot)
                ) {
                  onReady?.();
                }
              }}
              params={params}
            />
          </div>
        </>
      )}
    </div>
  );
}

import { useNow } from "#root/src/hooks/useNow";
import { Paper, Text } from "@mantine/core";
import dayjs from "dayjs";
import { useApiData } from "../openapi/openapi.query";

export const DAMBaseLoadCard = () => {
  const now = useNow();
  // Timezone is important here, because the DAM data is in this timezone
  const today = now.tz("Europe/Budapest").startOf("day");
  const {
    data: { data },
  } = useApiData({
    operationId: "DAM_Aggregated_Trading_Data_v1",
    params: {
      filter: `DeliveryDay__gte__${today.format("YYYY-MM-DDTHH:mm:ss")}Z`,
    },
  });

  // +1 is important, because the data is from 1-24
  const currentH = now.hour() + 1;
  const last = data.find(
    (d) => dayjs(d.DeliveryDay).isSame(today, "day") && d.ProductH === currentH
  )?.BaseloadPrice;

  return (
    <StatCard
      title="Daily BL price"
      data={last ? `${last.toFixed(2)} €/MWh` : "-"}
    />
  );
};

const StatCard = ({ title, data }) => {
  return (
    <Paper p="16px" h="83px">
      <Text c="gray.7" size="14px" fw={500}>
        {title}
      </Text>
      <Text fw={600} size="lg" mt="12px">
        {data}
      </Text>
    </Paper>
  );
};

export const DAMCard = () => {
  const now = useNow();
  // Timezone is important here, because the DAM data is in this timezone
  const today = now.tz("Europe/Budapest").startOf("day");
  const {
    data: { data },
  } = useApiData({
    operationId: "DAM_Aggregated_Trading_Data_v1",
    params: {
      filter: `DeliveryDay__gte__${today.format("YYYY-MM-DDTHH:mm:ss")}Z`,
    },
  });

  // +1 is important, because the data is from 1-24
  const currentH = now.hour() + 1;
  const last = data.find(
    (d) => dayjs(d.DeliveryDay).isSame(today, "day") && d.ProductH === currentH
  )?.Price;

  return (
    <StatCard
      title="DAM Hourly price"
      data={last ? `${last.toFixed(2)} €/MWh` : "-"}
    />
  );
};

export const IDACard = ({ n }) => {
  const now = useNow();
  const today = now.startOf("day");

  const {
    data: { data },
  } = useApiData({
    operationId: "IDA_QuarterHourly_v1",
    params: {
      filter: `DeliveryDay__gte__${today.format("YYYY-MM-DDTHH:mm:ss")}Z`,
    },
  });

  // calculate quarter hour
  // +1 is important, because the data is from 1-96
  const currentQuarterH = Math.floor((now.hour() * 60 + now.minute()) / 15) + 1;
  const last = data.find(
    (d) =>
      dayjs(d.DeliveryDay).isSame(today, "day") &&
      d.ProductQH === currentQuarterH
  )?.[`PriceIDA${n}`];

  return (
    <StatCard
      title={`IDA${n} QH price`}
      data={last ? `${last.toFixed(2)} €/MWh` : "-"}
    />
  );
};

export const ID3Card = () => {
  const now = useNow();
  const now_ = now.startOf("minute");
  const {
    data: { data },
  } = useApiData({
    operationId: "IDC_QuarterHourly_v1",
    params: {
      filter: `DeliveryDate__gte__${now_
        .subtract(25, "minutes")
        .format("YYYY-MM-DDTHH:mm:ss")}Z,DeliveryDate__lte__${now_.format(
        "YYYY-MM-DDTHH:mm:ss"
      )}Z`,
    },
  });
  // We need the first element since the data is sorted in DESC by DeliveryDate
  const last = data.at(0)?.ID3Index;

  return (
    <StatCard
      title="ID3 index"
      data={last ? `${last.toFixed(2)} €/MWh` : "-"}
    />
  );
};

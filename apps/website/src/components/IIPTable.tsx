import { <PERSON><PERSON>, <PERSON>, But<PERSON>, Text } from "@mantine/core";
import { DataTable } from "mantine-datatable";
import { useApiData } from "../openapi/openapi.query";
import { useNow } from "#root/src/hooks/useNow";

const IIPTable = () => {
  const now = useNow();
  const minuteNow = now.startOf("minute");
  const {
    data: { data },
  } = useApiData({
    operationId: "IIP_v1",
    params: {
      // TODO: server/browser timezone
      filter: `IntervalStart__lte__${minuteNow
        .format("YYYY-MM-DDTHH:mm:ss")
        .toString()}Z,IntervalStop__gte__${minuteNow
        .format("YYYY-MM-DDTHH:mm:ss")
        .toString()}Z,EventStatus__eq__Active`,
    },
  });

  const last10 = data.slice(0, 10).map((d, id) => ({
    ...d,
    id,
  }));

  return (
    <Stack>
      {last10.length > 0 ? (
        <DataTable
          columns={[
            {
              accessor: "CreatedDate",
              title: "Created Date",
            },
            {
              accessor: "MarketParticipant",
              title: "Market Participant",
            },
            {
              accessor: "AffectedAssetUnit",
              title: "Affected Asset Unit",
            },
            {
              accessor: "UnavailabilityType",
              title: "Unavailability Type",
            },
            {
              accessor: "UnavailableCapacity",
              title: "Unavailable Capacity",
            },
          ]}
          records={last10}
        />
      ) : (
        "No outages reported at this time."
      )}
    </Stack>
  );
};

export default IIPTable;

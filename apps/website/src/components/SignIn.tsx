import { useAuth, usePasswordReset } from "#root/src/api/auth/auth.query";
import { BlockTitle } from "#root/src/components/BlockTitle";
import { TextInput } from "#root/src/components/formfields/TextInput";
import { navigate } from "vike/client/router";
import { passwordRegex } from "#root/src/shared/constants";
import type { PaperProps } from "@mantine/core";
import { Alert, Anchor, Box, Button, Stack, Text } from "@mantine/core";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { FiAlertTriangle } from "react-icons/fi";
import { usePageContext } from "vike-react/usePageContext";
import { useActivateUser } from "../api/users/query";

export type SigninProps = {
  onSignIn?: () => void;
  variant?: "homepage" | "default";
} & PaperProps;
export const Signin = (props: SigninProps) => {
  const { t } = useTranslation();
  const ctx = usePageContext();
  const token = ctx.urlParsed.search["token"];
  const emailResult = usePasswordReset(token);
  const activateUser = useActivateUser();
  const { profile, login } = useAuth();
  const passwordResetEmail = emailResult.data?.valid && emailResult.data.email;
  let redirect = ctx.urlParsed.search.redirect;

  const form = useForm<{
    email: string;
    password: string;
    newPassword: string;
  }>({
    mode: "onChange",
    defaultValues: {
      email: passwordResetEmail || "",
    },
  });

  useEffect(() => {
    if (token && !emailResult.data.valid) {
      form.setError("root", {
        message: `${t("invalidLink")}.`,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (profile) {
    if (redirect && !redirect.startsWith("/")) {
      redirect = "/" + redirect;
    }
    navigate(redirect || "/");
    return;
  }

  const variant = props.variant ?? "default";
  const isPasswordResetInitialNavigation = !!token;
  const shouldShowPasswordResetFields = isPasswordResetInitialNavigation;

  const onSignIn = async ({
    email,
    password,
    newPassword,
  }: {
    email: string;
    password: string;
    newPassword?: string;
  }) => {
    const res = await login.mutateAsync({
      email,
      password,
      newPassword,
      token,
    });

    switch (res.status) {
      case "WRONG_CREDENTIALS_ERROR":
        form.setError("root", {
          message: `${t("wrongCredentials")}.`,
        });
        break;
      case "INVALID_PASSWORD_ERROR":
        form.setError("root", {
          message: `${t("invalidPassword")}.`,
        });
        break;
      case "SAME_PASSWORD_ERROR":
        form.setError("root", {
          message: `${t("samePasswordError")}.`,
        });
        break;
      case "PASSWORD_UPDATE_REQUIRED":
        form.setError("root", {
          message: `${t("passwordUpdateRequired")}.`,
          type: "PASSWORD_UPDATE_REQUIRED",
        });
        break;
      case "USER_INACTIVE_ERROR":
        form.setError("root", {
          message: `${t("userInactiveError")}.`,
        });
        break;
      case "OK":
        props.onSignIn?.();
        await activateUser.mutateAsync(res.userId);
        break;
      default:
        break;
    }
  };

  const needsPasswordUpdate =
    form.formState.errors.root?.type === "PASSWORD_UPDATE_REQUIRED";

  return (
    <Box p="md" maw="550" mx="auto">
      <FormProvider {...form}>
        <BlockTitle label={t("Welcome back!")} fz="30" mb="xs" />

        {!needsPasswordUpdate && (
          <Text fz="14" mb="xl">
            {"Don't have an account yet? "}
            <Anchor fz="14" href="/signup" underline="always">
              Sign up now
            </Anchor>
          </Text>
        )}
        <form onSubmit={form.handleSubmit(onSignIn)}>
          <Stack>
            <TextInput
              withAsterisk
              readOnly={shouldShowPasswordResetFields}
              name="email"
              label="E-mail"
              rules={{
                required: true,
              }}
            />
            {!shouldShowPasswordResetFields && (
              <TextInput
                withAsterisk
                name="password"
                label={t("Password")}
                type="password"
                rules={{
                  required: true,
                }}
              />
            )}

            {(needsPasswordUpdate || shouldShowPasswordResetFields) && (
              <>
                <Alert
                  color="yellow"
                  icon={<FiAlertTriangle />}
                  mt="sm"
                  title="Attention"
                >
                  {
                    "If you don't have a password yet, please set one that you will use!"
                  }
                  <br />
                  <br />
                  {
                    "If you have requested a password reset, please enter your new password!"
                  }
                </Alert>
                <TextInput
                  mt="xs"
                  withAsterisk
                  name="newPassword"
                  label="New Password"
                  type="password"
                  description="Password must contain at least 8 characters, one lowercase letter, one uppercase letter, and one number."
                  rules={{
                    required: true,
                    validate: (value) =>
                      passwordRegex.test(value) ||
                      "Password does not meet requirements",
                  }}
                />
                <TextInput
                  withAsterisk
                  name="newPasswordConfirm"
                  label="Confirm New Password"
                  type="password"
                  rules={{
                    required: true,
                    validate: (value) =>
                      value === form.getValues().newPassword ||
                      "Passwords do not match",
                  }}
                />
              </>
            )}
          </Stack>
          <Stack mt="md" gap="sm" align="stretch">
            <Button
              // color="#52808e"
              mt="md"
              fullWidth={true}
              className="signin-button"
              type="submit"
              loading={form.formState.isSubmitting}
            >
              Sign in
            </Button>
            {!shouldShowPasswordResetFields && (
              <Button
                component="a"
                href="/auth/link"
                color="#5E5E5E"
                variant="outline"
              >
                <img
                  src="/ms-symbollockup_mssymbol_19.svg"
                  alt=""
                  css={{ marginRight: "6px" }}
                />
                Sign in with Microsoft
              </Button>
            )}
          </Stack>

          {!shouldShowPasswordResetFields && (
            <Button
              ml={0}
              p={0}
              style={{
                textDecoration: "underline",
              }}
              fw="normal"
              component="a"
              href="/password-reset"
              variant="transparent"
              {...(variant === "homepage" && {
                color: "cyan",
              })}
            >
              Forgot password?
            </Button>
          )}

          {form.formState.errors.root && !needsPasswordUpdate && (
            <Alert
              color="red"
              icon={<FiAlertTriangle />}
              mt="sm"
              title={
                form.formState.errors.root.type !== "PASSWORD_UPDATE_REQUIRED"
                  ? t("error")
                  : t("warning")
              }
              css={{
                color: "red",
              }}
            >
              {form.formState.errors.root.message}
            </Alert>
          )}
        </form>
      </FormProvider>
    </Box>
  );
};

import { <PERSON>chor, Button, Group, Menu, useMantineTheme } from "@mantine/core";
import { usePageContext } from "vike-react/usePageContext";
import { useAuth } from "../api/auth/auth.query";
import { useOpenApi } from "../openapi/openapi.query";
import { AnnouncementIcon } from "#root/src/components/AnnouncementIcon";

const HEADER_HEIGHT = 70;
export const WARNING_BANNER_HEIGHT = 40;
export const FULL_HEADER_HEIGHT = HEADER_HEIGHT + WARNING_BANNER_HEIGHT;

const NavButton = ({ label, href }: { label: string; href: string }) => {
  const ctx = usePageContext();
  // Get base route from href (e.g., /view, /guide, /settings)
  const baseRoute = href === "/" ? "/" : `/${href.split("/")[1]}`;
  const currentPath = ctx.urlPathname;
  // Get current base route from path
  const currentBaseRoute =
    currentPath === "/" ? "/" : `/${currentPath.split("/")[1]}`;

  // Check if current path matches the base route
  const isActive = baseRoute === currentBaseRoute;
  const theme = useMantineTheme();
  return (
    <a
      href={href}
      css={{
        height: "100%",
        // marginBottom: 10,
        textDecoration: "none",
        color: isActive
          ? theme.colors[theme.primaryColor][8]
          : theme.colors.gray[9],
        position: "relative",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        //   color: isActive ? "red" : "black",
        // borderBottomLeftRadius: 5,
        // borderBottomRightRadius: 5,
        padding: theme.spacing.md,
        bottom: 0,
        // paddingTop: 10,
        // overflow: "hidden",
        // backgroundColor: "none",
      }}
    >
      {label}
      <div
        css={{
          position: "absolute",
          bottom: 0,
          height: 4,
          width: "100%",
          backgroundColor: isActive
            ? theme.colors[theme.primaryColor][8]
            : "none",
        }}
      ></div>
    </a>
  );
};

const UserMenu = () => {
  const { profile, logout } = useAuth();
  const theme = useMantineTheme();
  return (
    <Group gap={1} align={"center"} justify={"flex-start"} ml={"auto"}>
      <Menu position="bottom-start">
        <Menu.Target>
          <Button size="md" h="40" ml="auto" variant="transparent">
            {profile?.email}
          </Button>
        </Menu.Target>
        <Menu.Dropdown w={200}>
          <Menu.Item component="a" href="/settings/general">
            Settings
          </Menu.Item>
          <Menu.Item
            onClick={() => {
              logout.mutate();
            }}
          >
            Log out
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
      <AnnouncementIcon />
    </Group>
  );
};

export const Header = () => {
  const { data } = useOpenApi();
  const theme = useMantineTheme();
  const { profile } = useAuth();
  const firstOperation = data.groupedOperations[0].operations[0];
  return (
    <>
      <div
        css={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 101,
          backgroundColor: "#d9a7a7",
          color: "#4d4d4d",
          textAlign: "center",
          padding: "10px 0",
          fontSize: "14px",
          fontWeight: 500,
          borderBottom: "1px solid #c9c9c9",
          height: WARNING_BANNER_HEIGHT,
        }}
      >
        This website is currently in the testing phase. Some features may not
        work as expected.
        <Anchor
          href="/about_us/pricing"
          target="_blank"
          size="sm"
          c="gray"
          underline="always"
          ml="xs"
        >
          Terms and Conditions
        </Anchor>
      </div>

      <div
        css={{
          position: "fixed",
          top: WARNING_BANNER_HEIGHT, // Adjusted to account for the banner height
          left: 0,
          right: 0,
          zIndex: 100,
          width: 250,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          height: HEADER_HEIGHT,
          backgroundColor: "#fff7",
          backdropFilter: "blur(10px)",
          borderBottom: `1px solid ${theme.colors.gray[4]}`,
        }}
      >
        <a href="/">
          <img
            src="/HUPX_labs_logo.svg"
            alt=""
            width={140}
            css={{ display: "block" }}
          />
        </a>
      </div>

      <div
        css={{
          position: "fixed",
          top: WARNING_BANNER_HEIGHT, // Adjusted to account for the banner height
          left: 250,
          right: 0,
          zIndex: 100,
          display: "flex",
          alignItems: "center",
          height: HEADER_HEIGHT,
          padding: "0 20px",
          backgroundColor: "#fff7",
          backdropFilter: "blur(10px)",
          borderBottom: `1px solid ${theme.colors.gray[4]}`,
        }}
      >
        <NavButton label="Home" href="/" />
        <NavButton
          label="Data View"
          href={`/view/${firstOperation.operationId}`}
        />
        {profile && (
          <NavButton
            label="Developer Guide"
            href={`/guide/${firstOperation.operationId}`}
          />
        )}
        <NavButton label="Changelog" href="/changelog" />
        {profile && <NavButton label="Settings" href="/settings/general" />}
        <NavButton label="About" href="/about_us/pricing" />
        {profile && <UserMenu />}
        {!profile && (
          <Group m={0} p={0} ml="auto">
            <Button component="a" href="/signup" variant={"outline"}>
              Sign up
            </Button>
            <Button component="a" href="/signin">
              Sign in
            </Button>
          </Group>
        )}
      </div>
    </>
  );
};

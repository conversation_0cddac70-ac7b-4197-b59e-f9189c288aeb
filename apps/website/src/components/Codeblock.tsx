import Prism from "prismjs";
import { useEffect } from "react";
import "prismjs/components/prism-markup-templating";
import "prismjs/components/prism-java.js";
import "prismjs/components/prism-bash.js";
import "prismjs/components/prism-php.js";
import "prismjs/components/prism-clike.js";
import "prismjs/components/prism-csharp.js";
import "prismjs/components/prism-python.js";

import "./prism-theme.css";
import "prismjs/plugins/line-numbers/prism-line-numbers.css";
import "prismjs/plugins/line-numbers/prism-line-numbers.js";

export const CodeBlock = ({
  code,
  language = "javascript",
}: {
  code: string;
  language?: string;
  lineNumbering?: boolean;
}) => {
  useEffect(() => {
    Prism.highlightAll();
  }, [code, language]);

  return (
    <div
      style={{ position: "relative", display: "flex", flexGrow: 1, width: 0 }}
    >
      <pre
        css={{
          maxHeight: 300,
          width: "100%",
          overflow: "auto",
        }}
        data-start="0"
        className={`line-numbers language-${language}`}
        key={language + code}
      >
        <code data-start="0">{code}</code>
      </pre>
    </div>
  );
};

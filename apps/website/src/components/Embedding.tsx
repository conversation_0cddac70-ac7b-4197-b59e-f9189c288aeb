import { LoadingOverlay } from "@mantine/core";
import { useState, useMemo } from "react";
import { clientOnly } from "vike-react/clientOnly";
import { useEmbedding } from "../api/embeddings/embeddings.query";
import { assert } from "../shared/assert";
import { processDateTemplates } from "../utils/dateTemplating";

const ApiDataPlotWithTable = clientOnly(
  () => import("#root/src/components/ApiDataPlotWithTableClientOnly")
);

function Loader({
  operationId,
  params,
  includeTable = true,
  includeChart = true,
}: {
  operationId: string;
  params?: {
    [key: string]: string | number | string[] | number[] | Date[];
  };
  includeTable?: boolean;
  includeChart?: boolean;
}) {
  const [ready, setReady] = useState(false);

  return (
    <div
      id={"LOADING_WRAP"}
      css={{
        position: "relative",
        height: "100%",
      }}
    >
      <LoadingOverlay
        visible={!ready}
        zIndex={100}
        overlayProps={{ radius: "sm", blur: 2 }}
        loaderProps={{
          style: {
            alignSelf: "start",
            marginTop: 70,
            height: "100%",
          },
        }}
      />
      <ApiDataPlotWithTable
        operationId={operationId}
        onReady={() => {
          setReady(true);
        }}
        params={params}
        ChartComponentProps={{
          showControls: true,
        }}
        includeChart={includeChart}
        includeTable={includeTable}
        includeEmbedding={false}
      />
    </div>
  );
}

export const Embedding = ({ id }: { id: string }) => {
  const { data } = useEmbedding(id);
  // Already checked in +guard.server.ts
  assert(data);
  const { includeChart, includeTable, apiParams, dataSetOperationId } = data;

  // Process any date templates in the apiParams
  const processedApiParams = useMemo(() => {
    return processDateTemplates(apiParams || "");
  }, [apiParams]);

  return (
    <>
      <Loader
        operationId={dataSetOperationId}
        params={{
          ...(processedApiParams && {
            filter: processedApiParams,
          }),
        }}
        includeChart={includeChart}
        includeTable={includeTable}
      />
    </>
  );
};

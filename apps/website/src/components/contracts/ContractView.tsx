import { useContract } from "#root/src/api/contract/contract.query";
import { BlockTitle } from "#root/src/components/BlockTitle";
import {
  Box,
  Divider,
  Group,
  Paper,
  Stack,
  Text,
  Title,
  Badge,
  Grid,
} from "@mantine/core";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";
import { formatAddress } from "#root/src/utils/address";

export type ContractViewProps = {
  contractId: string;
};

const FieldDisplay = ({
  label,
  value,
}: {
  label: string;
  value?: string | number | null;
}) => (
  <Box mb="xs">
    <Text size="sm" fw={500} c="dimmed">
      {label}
    </Text>
    <Text>{value || "-"}</Text>
  </Box>
);

export const ContractView = ({ contractId }: ContractViewProps) => {
  const { data: contract } = useContract(contractId);
  const { t } = useTranslation();

  // Format status for display
  const getStatusBadge = (status: string) => {
    let color: string;

    switch (status.toUpperCase()) {
      case "PENDING":
        color = "yellow";
        break;
      case "ACCEPTED":
        color = "green";
        break;
      case "REJECTED":
        color = "red";
        break;
      default:
        color = "blue";
    }

    return <Badge color={color}>{status}</Badge>;
  };

  if (!contract) {
    return (
      <Title order={2}>{"No contract data is found or you unauthorized"}</Title>
    );
  }

  return (
    <Stack gap="lg">
      {/* Header with basic information */}
      <Paper shadow="xs" p="lg" radius="md">
        <Group justify="space-between" mb="md">
          <Title order={3}>{contract.companyName}</Title>
          {getStatusBadge(contract.status)}
        </Group>

        <Group grow>
          <FieldDisplay label={t("Contract ID")} value={contract.id} />
          <FieldDisplay label={t("Type")} value={contract.type} />
          <FieldDisplay
            label={t("Created At")}
            value={dayjs(contract.createdAt).format("DD-MM-YYYY HH:mm")}
          />
        </Group>
      </Paper>

      {/* Company Information */}
      <Paper shadow="xs" p="lg" radius="md">
        <BlockTitle label={t("Company Information")} mb="md" />

        <Group grow mb="md">
          <FieldDisplay
            label={t("Company Name")}
            value={contract.companyName}
          />
          <FieldDisplay
            label={t("Tax Number")}
            value={contract.companyTaxNumber}
          />
        </Group>

        <Group grow mb="md">
          <FieldDisplay
            label={t("Address")}
            value={formatAddress(
              contract.companyStreetName || "",
              contract.companyStreetType || "",
              contract.companyHouseNumber || "",
              contract.companyFloorDoor || ""
            )}
          />
          <FieldDisplay label={t("City")} value={contract.companyCity} />
        </Group>

        <Group grow mb="md">
          <FieldDisplay label={t("ZIP Code")} value={contract.companyZipCode} />
          <FieldDisplay label={t("Country")} value={contract.companyCountry} />
        </Group>

        <Group grow mb="md">
          <FieldDisplay
            label={t("Authorized Representative")}
            value={contract.companyAuthorizedRepName}
          />
          <FieldDisplay
            label={t("Additional Representative")}
            value={contract.companyAdditionalRepName}
          />
        </Group>

        <Group grow>
          <FieldDisplay
            label={t("Commencement Date")}
            value={contract.companyCommencementDate}
          />
          <FieldDisplay
            label={t("Invoice Email")}
            value={contract.companyInvoiceEmail}
          />
        </Group>
      </Paper>

      {/* Billing Information */}
      <Paper shadow="xs" p="lg" radius="md">
        <BlockTitle label={t("Billing Information")} mb="md" />

        {contract.billingIsSameAsCompanyAddress ? (
          <Text style={{ fontStyle: "italic" }}>
            {t("Same as company address")}
          </Text>
        ) : (
          <>
            <Group grow mb="md">
              <FieldDisplay
                label={t("Billing Name")}
                value={contract.billingName}
              />
              <FieldDisplay
                label={t("Billing Country")}
                value={contract.billingCountry}
              />
            </Group>

            <Group grow>
              <FieldDisplay
                label={t("Billing Address")}
                value={formatAddress(
                  contract.billingStreetName || "",
                  contract.billingStreetType || "",
                  contract.billingHouseNumber || "",
                  contract.billingFloorDoor || ""
                )}
              />
              <FieldDisplay
                label={t("Billing City")}
                value={contract.billingCity}
              />
              <FieldDisplay
                label={t("Billing ZIP Code")}
                value={contract.billingZipCode}
              />
            </Group>
          </>
        )}
      </Paper>

      {/* Main Contact */}
      <Paper shadow="xs" p="lg" radius="md">
        <BlockTitle label={t("Main Contact")} mb="md" />

        <Group grow mb="md">
          <FieldDisplay
            label={t("First Name")}
            value={contract.mainContactFirstName}
          />
          <FieldDisplay
            label={t("Last Name")}
            value={contract.mainContactLastName}
          />
        </Group>

        <Group grow>
          <FieldDisplay
            label={t("Position")}
            value={contract.mainContactPosition}
          />
          <FieldDisplay label={t("Phone")} value={contract.mainContactPhone} />
          <FieldDisplay label={t("Email")} value={contract.mainContactEmail} />
        </Group>
      </Paper>

      {/* Secondary Contacts */}
      {contract.secondaryContacts.length > 0 && (
        <Paper shadow="xs" p="lg" radius="md">
          <BlockTitle label={t("Secondary Contacts")} mb="md" />

          {contract.secondaryContacts.map((contact, index) => (
            <Box
              key={contact.id}
              mb={index < contract.secondaryContacts.length - 1 ? "md" : 0}
            >
              {index > 0 && <Divider my="md" />}

              <Group grow mb="md">
                <FieldDisplay
                  label={t("First Name")}
                  value={contact.firstName}
                />
                <FieldDisplay label={t("Last Name")} value={contact.lastName} />
              </Group>

              <Group grow>
                <FieldDisplay label={t("Position")} value={contact.position} />
                <FieldDisplay label={t("Phone")} value={contact.phone} />
                <FieldDisplay label={t("Email")} value={contact.email} />
              </Group>
            </Box>
          ))}
        </Paper>
      )}

      {/* Packages */}
      {contract.packages.length > 0 && (
        <Paper shadow="xs" p="lg" radius="md">
          <BlockTitle label={t("Ordered Packages")} mb="md" />

          {contract.packages.map((pkg, index) => (
            <Box
              key={pkg.id}
              mb={index < contract.packages.length - 1 ? "md" : 0}
            >
              {index > 0 && <Divider my="md" />}

              <Grid grow mb="md">
                <Grid.Col span={4}>
                  <FieldDisplay
                    label={t("Package Name")}
                    value={pkg.package.name}
                  />
                </Grid.Col>
                <Grid.Col span={4}>
                  <FieldDisplay label={t("Price")} value={`${pkg.price} EUR`} />
                </Grid.Col>
                {pkg.quantity !== 1 && (
                  <Grid.Col span={4}>
                    <FieldDisplay label={t("Quantity")} value={pkg.quantity} />
                  </Grid.Col>
                )}
                {pkg.historicalData.length !== 0 ? (
                  <Grid.Col span={4}>
                    <FieldDisplay
                      label={t("Historical Data")}
                      value={pkg.historicalData.join(", ")}
                    />
                  </Grid.Col>
                ) : (
                  <Grid.Col span={4}></Grid.Col>
                )}
              </Grid>
            </Box>
          ))}
        </Paper>
      )}

      {/* Affiliated Companies */}
      {contract.affiliates.length > 0 && (
        <Paper shadow="xs" p="lg" radius="md">
          <BlockTitle label={t("Affiliated Companies")} mb="md" />

          {contract.affiliates.map((affiliate, index) => (
            <Box
              key={affiliate.id}
              mb={index < contract.affiliates.length - 1 ? "md" : 0}
            >
              {index > 0 && <Divider my="md" />}

              <Group grow mb="md">
                <FieldDisplay
                  label={t("Company Name")}
                  value={affiliate.name}
                />
                <FieldDisplay
                  label={t("Tax Number")}
                  value={affiliate.taxNumber}
                />
                <FieldDisplay
                  label={t("Registration")}
                  value={affiliate.companyRegistration}
                />
              </Group>

              <Group grow mb="md">
                <FieldDisplay label={t("Address")} value={affiliate.address} />
                <FieldDisplay label={t("City")} value={affiliate.city} />
              </Group>

              <Group grow>
                <FieldDisplay label={t("ZIP Code")} value={affiliate.zipCode} />
                <FieldDisplay label={t("Country")} value={affiliate.country} />
              </Group>
            </Box>
          ))}
        </Paper>
      )}
    </Stack>
  );
};

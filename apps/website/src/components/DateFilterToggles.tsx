import { Checkbox, Stack, Text, Group, Badge } from "@mantine/core";
import { useMemo, useState, useEffect } from "react";
import {
  parseFilterString,
  isDateFilter,
  isRelativeDateFilter,
  convertFilterToTemplate,
  convertTemplateToAbsolute,
  getDateFilterDescription,
  getFieldFromFilter,
} from "../utils/dateTemplating";

interface DateFilterTogglesProps {
  apiParams: string;
  onChange: (newApiParams: string) => void;
}

export function DateFilterToggles({
  apiParams,
  onChange,
}: DateFilterTogglesProps) {
  // Store original filter values to restore them when toggling off relative mode
  const [originalFilters, setOriginalFilters] = useState<Record<string, string>>({});

  // Parse filters and extract date filters
  const { filters, dateFilters, dateFiltersByField, relativeEnabled } = useMemo(() => {
    const parsedFilters = parseFilterString(apiParams);
    const dateFiltersArray = parsedFilters.filter(
      (filter) => isDateFilter(filter) || isRelativeDateFilter(filter)
    );

    // Group date filters by field
    const filtersByField: Record<string, string[]> = {};
    dateFiltersArray.forEach((filter) => {
      const field = getFieldFromFilter(filter);
      if (!filtersByField[field]) {
        filtersByField[field] = [];
      }
      filtersByField[field].push(filter);
    });

    // Initialize relative enabled state
    const initialRelativeState: Record<string, boolean> = {};
    dateFiltersArray.forEach((filter) => {
      initialRelativeState[filter] = isRelativeDateFilter(filter);
    });

    return {
      filters: parsedFilters,
      dateFilters: dateFiltersArray,
      dateFiltersByField: filtersByField,
      relativeEnabled: initialRelativeState
    };
  }, [apiParams]);

  // Update originalFilters when apiParams changes
  useEffect(() => {
    // Only store original values for non-relative filters
    const newOriginalFilters: Record<string, string> = {};

    filters.forEach(filter => {
      if (isDateFilter(filter) && !isRelativeDateFilter(filter)) {
        // Store the original absolute date filter
        newOriginalFilters[getFieldFromFilter(filter) + '__' + filter.split('__')[1]] = filter;
      }
    });

    setOriginalFilters(prev => ({...prev, ...newOriginalFilters}));
  }, [filters]);

  // Handle toggle change
  const handleToggleChange = (filter: string, enabled: boolean) => {
    // Update the filter
    const updatedFilters = [...filters];
    const filterIndex = updatedFilters.indexOf(filter);

    if (filterIndex !== -1) {
      if (enabled) {
        // When enabling relative mode, convert to template and store original
        const field = getFieldFromFilter(filter);
        const operator = filter.split('__')[1];
        const key = `${field}__${operator}`;

        // Store the original filter if it's not already stored
        if (!originalFilters[key] && isDateFilter(filter)) {
          setOriginalFilters(prev => ({
            ...prev,
            [key]: filter
          }));
        }

        // Convert to template format
        updatedFilters[filterIndex] = convertFilterToTemplate(filter);
      } else {
        // When disabling relative mode, restore the original filter if available
        const field = getFieldFromFilter(filter);
        const operator = filter.split('__')[1];
        const key = `${field}__${operator}`;

        if (originalFilters[key]) {
          // Restore the original filter
          updatedFilters[filterIndex] = originalFilters[key];
        } else {
          // Fall back to converting the template to absolute if original not found
          updatedFilters[filterIndex] = convertTemplateToAbsolute(filter);
        }
      }

      // Update the parent component
      onChange(updatedFilters.join(","));
    }
  };

  if (dateFilters.length === 0) {
    return (
      <Text size="sm" c="dimmed">
        No date filters found in the current filter string.
      </Text>
    );
  }

  return (
    <Stack gap="xs">
      <Text fw={500}>Date Filters</Text>
      {Object.entries(dateFiltersByField).map(([field, fieldFilters]) => (
        <Stack key={field} gap="xs">
          <Text size="sm" fw={500}>
            {field}
          </Text>
          {fieldFilters.map((filter) => {
            const isRelative = isRelativeDateFilter(filter);
            const description = getDateFilterDescription(filter);

            return (
              <Group key={filter} gap="xs">
                <Checkbox
                  label="Relative to current date"
                  checked={isRelative}
                  onChange={(e) =>
                    handleToggleChange(filter, e.currentTarget.checked)
                  }
                />
                <Badge color={isRelative ? "blue" : "gray"}>
                  {description}
                </Badge>
              </Group>
            );
          })}
        </Stack>
      ))}
    </Stack>
  );
}

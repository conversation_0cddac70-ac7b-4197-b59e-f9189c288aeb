import type { ChartComponentProps } from "./types";
import { useEffect, useRef, useState } from "react";
import type {
  LineSeriesPartialOptions,
  UTCTimestamp,
} from "lightweight-charts";
import { ColorType, createChart } from "lightweight-charts";
import { useApiData } from "#root/src/openapi/openapi.query";
import dayjs from "dayjs";
import { Chip, Stack, useMantineTheme } from "@mantine/core";

interface IsiMarketData {
  DAMPrice: number;
  DeliveryDay: string;
  IDA1Price: number;
  IDA1Volume: number;
  IDA2Price: number;
  IDA2Volume: number;
  IDA3Price: number;
  IDA3Volume: number;
  IDC3HPrice: number;
  IDC3HVolume: number;
  IDC3QHPrice: number;
  IDC3QHVolume: number;
  IDCTHLiquidity: number;
  IDCTHPrice: number;
  IDCTQHLiquidity: number;
  IDCTQHPrice: number;
  QuarterHour: number;
  idx: number;
}

export const ISI_v1: React.FC<ChartComponentProps> = (props) => {
  const theme = useMantineTheme();
  const variants = [
    {
      value: "DAMPrice",
      label: "DAM Price",
      color: theme.colors[theme.primaryColor][6],
    },
    { value: "IDA1Price", label: "IDA1 Price", color: "#70C8E5" },
    { value: "IDA2Price", label: "IDA2 Price", color: "#33B7C2" },
    { value: "IDA3Price", label: "IDA3 Price", color: "#577590" },
    { value: "IDC3HPrice", label: "IDC3H Price", color: "#FFA600" },
    { value: "IDC3QHPrice", label: "IDC3QH Price", color: "#F7D560" },
    { value: "IDCTHPrice", label: "IDCTH Price", color: "#E9C52B" },
    { value: "IDCTQHPrice", label: "IDCTQH Price", color: "#F68B44" },
  ];
  const { operationId, onReady, params, showControls } = props;
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [selectedVariants, setSelectedVariants] =
    useState<typeof variants>(variants);
  const {
    data: { data },
  } = useApiData<{ data: IsiMarketData[] }>({
    operationId,
    params,
  });

  useEffect(() => {
    if (!chartContainerRef.current || !data) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      layout: {
        background: { type: ColorType.Solid, color: "white" },
        textColor: "black",
      },
      grid: {
        vertLines: { color: "#e0e0e0" },
        horzLines: { color: "#e0e0e0" },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: "#D1D4DC",
        fixLeftEdge: true,
        fixRightEdge: true,
      },
      rightPriceScale: {
        scaleMargins: { top: 0.1, bottom: 0.3 },
        borderColor: "#D1D4DC",
      },
      leftPriceScale: {
        visible: true,
        scaleMargins: { top: 0.7, bottom: 0 },
        borderColor: "#D1D4DC",
      },
      crosshair: {
        mode: 1,
        vertLine: { width: 1, color: "#758696", style: 3 },
        horzLine: { visible: true, labelVisible: true },
      },
    });

    // Process and sort data
    const sortedData = data
      .map((item) => ({
        ...item,
        time: (dayjs(item.DeliveryDay)
          .add(item.QuarterHour * 15, "minute")
          .toDate()
          .getTime() / 1000) as UTCTimestamp,
      }))
      .sort((a, b) => a.time - b.time);

    // Create line series for each selected price
    const lineSeriesOptions: LineSeriesPartialOptions = {
      lineWidth: 2,
      priceScaleId: "right",
      // pointMarkersVisible: true,
      lastValueVisible: false,
      priceLineVisible: false,
    };

    selectedVariants.forEach((variant, index) => {
      let currentSeries = chart.addLineSeries({
        ...lineSeriesOptions,
        color: variant.color,
      });
      let currentData: any[] = [];

      // Process data for price series
      sortedData.forEach((item, dataIndex) => {
        const prevItem = sortedData[dataIndex - 1];

        currentData.push(
          item[variant.value as keyof IsiMarketData]
            ? {
                time: item.time,
                value: item[variant.value as keyof IsiMarketData] as number,
                color: variant.color,
              }
            : { time: item.time }
        );

        if (
          prevItem &&
          !item[variant.value as keyof IsiMarketData] &&
          prevItem[variant.value as keyof IsiMarketData]
        ) {
          currentSeries.setData(currentData);
          currentData = [];
          currentSeries = chart.addLineSeries({
            ...lineSeriesOptions,
            color: variant.color,
          });
        }
      });

      currentSeries.setData(currentData);
    });

    chart.timeScale().fitContent();

    const handleResize = () => {
      if (chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: chartContainerRef.current.clientHeight,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    onReady?.();

    return () => {
      window.removeEventListener("resize", handleResize);
      chart.remove();
    };
  }, [data, selectedVariants, theme.colors, theme.primaryColor, onReady]);
  return (
    <div style={{ height: "100%", display: "flex" }}>
      <div
        style={{
          position: "relative",
          height: "100%",
          flexGrow: 1,
        }}
      >
        <div
          ref={chartContainerRef}
          style={{
            position: "absolute",
            inset: 0,
          }}
        />
      </div>
      {showControls && (
        <Stack gap="2" w="107">
          {variants.map((variant, index) => (
            <Chip
              css={{
                // flexGrow: 1
                label: {
                  width: "100%",
                },
              }}
              w="100%"
              radius="xs"
              size="xs"
              color={variant.color}
              key={variant.value}
              checked={selectedVariants.some(
                (selectedVariant) => selectedVariant.value === variant.value
              )}
              onChange={(checked) => {
                if (checked) {
                  setSelectedVariants([...selectedVariants, variant]);
                } else {
                  setSelectedVariants(
                    selectedVariants.filter(
                      (selectedVariant) =>
                        selectedVariant.value !== variant.value
                    )
                  );
                }
              }}
            >
              {variant.label}
            </Chip>
          ))}
        </Stack>
      )}
    </div>
  );
};

import { useEffect, useState } from "react";
import Plot from "react-plotly.js";
import { useApiData } from "../../openapi/openapi.query";
import { useMantineTheme } from "@mantine/core";
import type { ChartComponentProps } from "./types";

export const DAMChart: React.FC<ChartComponentProps> = (props) => {
  const { operationId, onReady, height, params } = props;
  const theme = useMantineTheme();
  const {
    data: { data },
  } = useApiData<{
    data: {
      BuyVolume: number;
      DeliveryDay: string;
      Hour: number;
      Price: number;
      Region: string;
      SellVolume: number;
    }[];
  }>({ operationId, params });

  const [chartData, setChartData] = useState<{
    buyVolume: number[];
    sellVolume: number[];
    price: number[];
  }>({
    buyVolume: [],
    sellVolume: [],
    price: [],
  });

  useEffect(() => {
    if (!data) return;

    const buyVolume: number[] = [];
    const sellVolume: number[] = [];
    const price: number[] = [];

    data.forEach((item) => {
      // if (item.Hour !== 1) {
      //   return;
      // }
      buyVolume.push(item.BuyVolume);
      sellVolume.push(item.SellVolume);
      price.push(item.Price);
    });

    setChartData({
      buyVolume,
      sellVolume,
      price,
    });
  }, [data]);

  //   const [filter, setFilter] = useState({
  //     deliveryDay:
  //   });

  return (
    <>
      <Plot
        style={{
          width: "100%",
          height: "100%",
        }}
        data={[
          {
            x: chartData.buyVolume,
            y: chartData.price,
            type: "scatter",
            mode: "lines+markers",
            name: "Buy Volume",
            line: {
              color: theme.colors.gray[6],
              width: 2,
            },
            marker: {
              size: 5,
            },
          },
          {
            x: chartData.sellVolume,
            y: chartData.price,
            type: "scatter",
            mode: "lines+markers",
            name: "Sell Volume",
            line: {
              color: theme.colors[theme.primaryColor][6],
              width: 2,
            },
            marker: {
              size: 5,
            },
          },
        ]}
        layout={{
          // width: '100%',
          // height: 600,
          // height: "100%",
          margin: {
            t: 20,
            r: 0,
            l: 50,
            b: 40,
          },
          xaxis: {
            title: "Volume",
            // make grid more dense
            nticks: 12,
          },
          yaxis: {
            title: "Price",
            // gridwidth: 21,
          },
          legend: {
            //   xanchor: "right",
          },
        }}
        config={{
          responsive: true,
        }}
        onAfterPlot={onReady}
      />
    </>
  );
};

import { useApiData } from "#root/src/openapi/openapi.query";
import { Chip, Stack, useMantineTheme } from "@mantine/core";
import dayjs from "dayjs";
import type {
  LineSeriesPartialOptions,
  UTCTimestamp,
} from "lightweight-charts";
import { ColorType, createChart } from "lightweight-charts";
import { useEffect, useRef, useState } from "react";
import type { ChartComponentProps } from "./types";

interface MarketData {
  DeliveryDay: string;
  PriceIDA1: number;
  PriceIDA2: number;
  PriceIDA3: number;
  ProductQH: number;
  VolumeIDA1: number;
  VolumeIDA2: number;
  VolumeIDA3: number;
  idx: number;
}

interface TimeSeriesPoint {
  time: UTCTimestamp;
  value?: number;
  color?: string;
}
const variants = ["IDA1", "IDA2", "IDA3"];
export const IDA_QuarterHourly_v1: React.FC<ChartComponentProps> = (props) => {
  const { operationId, onReady, height = 400, params, showControls } = props;
  const {
    data: { data },
  } = useApiData<{ data: MarketData[] }>({
    operationId,
    params,
  });
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const theme = useMantineTheme();
  const [selectedIDA, setSelectedIDA] = useState(variants[0]);

  useEffect(() => {
    if (!chartContainerRef.current || !data) return;

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      layout: {
        background: { type: ColorType.Solid, color: "white" },
        textColor: "black",
      },
      grid: {
        vertLines: { color: "#e0e0e0" },
        horzLines: { color: "#e0e0e0" },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: "#D1D4DC",
        fixLeftEdge: true,
        fixRightEdge: true,
      },
      rightPriceScale: {
        scaleMargins: { top: 0.1, bottom: 0.3 },
        borderColor: "#D1D4DC",
      },
      leftPriceScale: {
        visible: true,
        scaleMargins: { top: 0.7, bottom: 0 },
        borderColor: "#D1D4DC",
      },
      crosshair: {
        mode: 1,
        vertLine: { width: 1, color: "#758696", style: 3 },
        horzLine: { visible: true, labelVisible: true },
      },
    });

    // Format and sort data
    const sortedData = data
      .map((item) => ({
        ...item,
        time: (dayjs(item.DeliveryDay)
          .add((item.ProductQH - 1) * 15, "minute")
          .toDate()
          .getTime() / 1000) as UTCTimestamp,
      }))
      .sort((a, b) => a.time - b.time);

    // Create line series for price data
    const lineSeriesOptions: LineSeriesPartialOptions = {
      color: "#2962FF",
      lineWidth: 2,
      priceScaleId: "right",
      pointMarkersVisible: true,
      lastValueVisible: false,
      priceLineVisible: false,
    };

    let currentSeries = chart.addLineSeries(lineSeriesOptions);
    let currentData: TimeSeriesPoint[] = [];

    // Process data for price series
    sortedData.forEach((item, index) => {
      const prevItem = sortedData[index - 1];

      currentData.push(
        item[`Price${selectedIDA}`]
          ? {
              time: item.time,
              value: item[`Price${selectedIDA}`],
              color: theme.colors[theme.primaryColor][6],
            }
          : { time: item.time }
      );

      if (
        prevItem &&
        !item[`Price${selectedIDA}`] &&
        prevItem[`Price${selectedIDA}`]
      ) {
        currentSeries.setData(currentData);
        currentData = [];
        currentSeries = chart.addLineSeries(lineSeriesOptions);
      }
    });
    currentSeries.setData(currentData);

    // Create volume series
    const volumeSeries = chart.addHistogramSeries({
      color: "#26a69a",
      priceScaleId: "left",
      title: "Volume",
      priceFormat: { type: "volume" },
    });

    const volumeData = sortedData.map((item) =>
      item[`Volume${selectedIDA}`]
        ? {
            value: item[`Volume${selectedIDA}`],
            color: theme.colors.gray[6],
            time: item.time,
          }
        : { time: item.time }
    );
    volumeSeries.setData(volumeData);

    // Fit content and handle resize
    chart.timeScale().fitContent();
    const handleResize = () => {
      if (chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: chartContainerRef.current.clientHeight,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    onReady?.();

    return () => {
      window.removeEventListener("resize", handleResize);
      chart.remove();
    };
  }, [data, height, selectedIDA, theme.colors, onReady]);

  return (
    <div style={{ height: "100%", display: "flex" }}>
      <div
        style={{
          position: "relative",
          height: "100%",
          flexGrow: 1,
        }}
      >
        <div
          ref={chartContainerRef}
          style={{
            position: "absolute",
            inset: 0,
          }}
        />
      </div>
      {showControls && (
        <Stack gap="2" w="107">
          {variants.map((variant, index) => (
            <Chip
              css={{
                // flexGrow: 1
                label: {
                  width: "100%",
                },
              }}
              w="100%"
              radius="xs"
              size="xs"
              key={variant}
              checked={selectedIDA === variant}
              onChange={(checked) => {
                if (checked) {
                  setSelectedIDA(variant);
                }
              }}
            >
              {variant}
            </Chip>
          ))}
        </Stack>
      )}
    </div>
  );
};

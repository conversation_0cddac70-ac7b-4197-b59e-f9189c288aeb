import { useApiData } from "#root/src/openapi/openapi.query";
import type { IChartApi } from "lightweight-charts";
import { createChart, ColorType } from "lightweight-charts";
import { useRef, useState, useEffect } from "react";
import { useMantineTheme } from "@mantine/core";
import dayjs from "dayjs";
import type { ChartComponentProps } from "./types";

interface DamData {
  BaseloadPrice: number;
  // "2019-08-24T14:15:22Z"
  DeliveryDay: string;
  Price: number;
  ProductH: number;
  Volume: number;
  idx: number;
}

interface ChartSeriesData {
  priceData: {
    time: string; // Changed to string format
    value: number;
  };
  volumeData: {
    time: string; // Changed to string format
    value: number;
    color: string;
  };
}

export const DAMAggregatedChart: React.FC<ChartComponentProps> = (props) => {
  const { operationId, onReady, height = 400, params } = props;
  const {
    data: { data },
  } = useApiData<{ data: DamData[] }>({
    operationId,
    params,
  });
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartApi, setChartApi] = useState<IChartApi | null>(null);
  const [series, setSeries] = useState<{
    priceSeries: ReturnType<IChartApi["addLineSeries"]>;
    volumeSeries: ReturnType<IChartApi["addHistogramSeries"]>;
  } | null>(null);
  const theme = useMantineTheme();

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      // height,
      layout: {
        background: { type: ColorType.Solid, color: "white" },
        textColor: "black",
      },
      grid: {
        vertLines: { color: "#e0e0e0" },
        horzLines: { color: "#e0e0e0" },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: "#D1D4DC",
        fixLeftEdge: true,
        fixRightEdge: true,
      },
      rightPriceScale: {
        scaleMargins: { top: 0.1, bottom: 0.3 },
        borderColor: "#D1D4DC",
      },
      leftPriceScale: {
        visible: true,
        scaleMargins: { top: 0.7, bottom: 0 },
        borderColor: "#D1D4DC",
      },
      crosshair: {
        mode: 1,
        vertLine: {
          width: 1,
          color: "#758696",
          style: 3,
        },
        horzLine: {
          visible: true,
          labelVisible: true,
        },
      },
    });

    setChartApi(chart);

    const priceSeries = chart.addLineSeries({
      color: "#2962FF",
      lineWidth: 2,
      priceScaleId: "right",
      title: "Price",
      pointMarkersVisible: true,
    });

    const volumeSeries = chart.addHistogramSeries({
      color: "#26a69a",
      priceScaleId: "left",
      title: "Volume",
      priceFormat: {
        type: "volume",
      },
    });

    setSeries({
      priceSeries,
      volumeSeries,
    });
    if (onReady) {
      onReady();
    }
    return () => {
      chart.remove();
    };
  }, [height]);

  // Update data
  useEffect(() => {
    if (!data || !series) return;
    const { priceSeries, volumeSeries } = series;

    // Format data
    const mappedData = data.map((item) => {
      return {
        ...item,
        time: dayjs(item.DeliveryDay)
          .add((item.ProductH - 1) * 1, "hours")
          .toDate(),
      };
    });

    // Sort data by delivery time
    const sortedData = [...mappedData].sort((a, b) => {
      return a.time.getTime() - b.time.getTime();
    });

    const chartData: ChartSeriesData[] = sortedData.map((item) => {
      const time = (item.time.getTime() / 1000) as any;

      return {
        priceData: {
          time,
          value: item.Price,
          color: theme.colors[theme.primaryColor][6],
        },
        volumeData: {
          time,
          value: item.Volume,
          color: theme.colors.gray[6],
        },
      };
    });

    priceSeries.setData(chartData.map((d) => d.priceData));
    volumeSeries.setData(chartData.map((d) => d.volumeData));
    chartApi!.timeScale().fitContent();
  }, [data, series, onReady]);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (chartApi && chartContainerRef.current) {
        chartApi.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [chartApi]);

  return (
    <div style={{ position: "relative", height: "100%" }}>
      <div
        ref={chartContainerRef}
        style={{
          position: "absolute",
          inset: 0,
        }}
      />
    </div>
  );
};

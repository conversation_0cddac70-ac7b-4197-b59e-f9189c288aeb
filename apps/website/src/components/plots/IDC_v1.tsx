import { useApiData } from "#root/src/openapi/openapi.query";
import type { IChartApi, UTCTimestamp } from "lightweight-charts";
import { createChart, ColorType } from "lightweight-charts";
import { useRef, useState, useEffect, useMemo } from "react";
import type { ChartComponentProps } from "./types";
import { Chip, Stack, useMantineTheme } from "@mantine/core";
import dayjs from "dayjs";
import { groupBy } from "lodash-es";

interface DamData {
  BuyDeliveryAreaID: string;
  BuyOrderID: number;
  BuyRemoteOrdrId: number;
  Currency: string;
  DeliveryEndDay: string;
  DeliveryEndTime: string;
  DeliveryStartDay: string;
  DeliveryStartTime: string;
  Duration: number;
  ExecutionTime: string;
  HourOfDelivery: number;
  HourOfExecution: number;
  IsPredefined: boolean;
  Local: string;
  Name: string;
  Price: number;
  ProductName: string;
  Quantity: number;
  QuantityUnit: string;
  RemoteTradeId: number;
  SellDeliveryAreaID: string;
  SellOrderID: number;
  SellRemoteOrdrId: number;
  TradeID: number;
  Volume: number;
  idx: number;
}

interface CandleData {
  time: UTCTimestamp;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

const productTypes = ["Hour", "Quarter hour"] as const;
type ProductType = (typeof productTypes)[number];

const parseTimestamp = (name: string): UTCTimestamp => {
  const [open, close] = name.split("-");
  const result = dayjs(open, { utc: true }).unix() as UTCTimestamp;
  return result;
};

export const IDC_v1: React.FC<ChartComponentProps> = (props) => {
  const { operationId, onReady, height = 400, params, showControls } = props;
  const {
    data: { data },
  } = useApiData<{ data: DamData[] }>({
    operationId,
    params,
  });
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const [chartApi, setChartApi] = useState<IChartApi | null>(null);
  const [selectedProductType, setSelectedProductType] =
    useState<ProductType>("Hour");
  const theme = useMantineTheme();

  const [series, setSeries] = useState<{
    candleSeries: ReturnType<IChartApi["addCandlestickSeries"]>;
    volumeSeries: ReturnType<IChartApi["addHistogramSeries"]>;
  } | null>(null);
  const processedData = useMemo(() => {
    if (!data?.length) return { candles: [], volumes: [] };

    // Filter data based on selected product type
    const filteredData = data.filter((item) =>
      selectedProductType === "Hour"
        ? item.ProductName === "XBID_Hour_Power"
        : item.ProductName === "XBID_Quarter_Hour_Power"
    );

    const nameGroups: string[] = [];
    const number = selectedProductType === "Hour" ? 24 : 96;
    const increment = selectedProductType === "Hour" ? 60 : 15;
    const ExecutionTimeEnd = params?.filter
      ?.find((item) => item.startsWith("ExecutionTime__lt__"))
      ?.split("ExecutionTime__lt__")?.[1];
    const startDay =
      dayjs(ExecutionTimeEnd)
        .subtract(1, "day")
        // .subtract(1, "minute")
        // .startOf("day")
        .format("YYYYMMDD") + " 00:00";

    for (let i = 0; i < number; i++) {
      const start = i * increment;
      nameGroups.push(
        dayjs(startDay).add(start, "minute").format("YYYYMMDD HH:mm")
      );
    }

    // Group by Name
    const groups = groupBy(filteredData, (item) => {
      const [open, close] = item.Name.split("-");

      const found = nameGroups.find((name) => {
        return open === name;
      });
      return found;
    });
    delete groups["undefined"];

    // Process each group into candle data
    const candleData = Object.entries(groups)
      .map(([name, group]): CandleData => {
        const prices = group.map((item) => item.Price);
        const volumes = group.map((item) => item.Volume);
        const mean = prices.reduce((a, b) => a + b, 0) / prices.length;
        const std = Math.sqrt(
          prices.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / prices.length
        );

        // Use first part of the name for timestamp
        const timestamp = parseTimestamp(name);

        return {
          time: timestamp,
          open: mean + std + 0.001,
          high: Math.max(...prices),
          low: Math.min(...prices),
          close: mean - std - 0.001,
          volume: volumes.reduce((a, b) => a + b, 0),
        };
      })

      // Sort by timestamp
      .sort((a, b) => a.time - b.time);

    return {
      candles: candleData,
      volumes: candleData.map((d) => ({
        time: d.time,
        value: d.volume,
        // color: d.open > d.close ? theme.colors.red[6] : theme.colors.green[6],
      })),
    };
  }, [data, selectedProductType, theme.colors]);

  // Initialize chart
  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      // height,
      // layout: {
      //   background: { type: ColorType.Solid, color: "white" },
      //   textColor: "black",
      // },
      // grid: {
      //   vertLines: { color: "#e0e0e0" },
      //   horzLines: { color: "#e0e0e0" },
      // },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        fixLeftEdge: true,
        fixRightEdge: true,
      },
      rightPriceScale: {
        scaleMargins: { top: 0.1, bottom: 0.3 },
      },
      leftPriceScale: {
        visible: true,
        scaleMargins: { top: 0.7, bottom: 0 },
      },
    });

    const candleSeries = chart.addCandlestickSeries({
      upColor: theme.colors[theme.primaryColor][6],
      downColor: theme.colors[theme.primaryColor][6],
      borderVisible: false,
      wickUpColor: theme.colors[theme.primaryColor][6],
      wickDownColor: theme.colors[theme.primaryColor][6],
    });

    const volumeSeries = chart.addHistogramSeries({
      color: theme.colors.gray[6],
      priceScaleId: "left",
      priceFormat: {
        type: "volume",
      },
    });

    setSeries({ candleSeries, volumeSeries });
    setChartApi(chart);
    onReady?.();

    return () => {
      chart.remove();
    };
  }, [height, theme.colors]);

  // Update data
  useEffect(() => {
    if (!series || !chartApi) return;

    const { candleSeries, volumeSeries } = series;
    candleSeries.setData(processedData.candles);
    volumeSeries.setData(processedData.volumes);

    chartApi.timeScale().fitContent();
  }, [processedData, series, chartApi, onReady]);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (chartApi && chartContainerRef.current) {
        chartApi.applyOptions({
          width: chartContainerRef.current.clientWidth,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [chartApi]);

  return (
    <div style={{ height: "100%", display: "flex" }}>
      <div style={{ position: "relative", height: "100%", flexGrow: 1 }}>
        <div
          ref={chartContainerRef}
          style={{
            position: "absolute",
            inset: 0,
          }}
        />
      </div>
      {showControls && (
        <Stack gap="2" w="107">
          {productTypes.map((type) => (
            <Chip
              key={type}
              css={{
                label: {
                  width: "100%",
                },
              }}
              w="100%"
              radius="xs"
              size="xs"
              checked={selectedProductType === type}
              onChange={() => setSelectedProductType(type)}
            >
              {type}
            </Chip>
          ))}
        </Stack>
      )}
    </div>
  );
};

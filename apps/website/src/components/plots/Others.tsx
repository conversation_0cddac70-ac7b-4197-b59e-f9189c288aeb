import { useOperation, useApiData } from "#root/src/openapi/openapi.query";
import { useMantineTheme, Group, Button } from "@mantine/core";
import {
  createChart,
  ColorType,
  PriceScaleMode,
  type IChartApi,
  type ISeriesApi,
  type UTCTimestamp,
} from "lightweight-charts";
import { useRef, useState, useEffect } from "react";
import type { ChartComponentProps } from "./types";
import type { Operation } from "#root/src/openapi/openapi";

type DataPoint = {
  time: UTCTimestamp;
  value: number;
  quantity: number;
  side: "BUY" | "SELL";
  [key: string]: any;
};

type VolumeType = "ALL" | "BUY" | "SELL" | "NET";

export const OthersChart: React.FC<ChartComponentProps> = (props) => {
  const { operationId, onReady, height, params } = props;

  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const tooltipRef = useRef<HTMLDivElement | null>(null);
  const operation = useOperation({ operationId });
  const { data } = useApiData({ operationId, params });
  const theme = useMantineTheme();
  const [volumeType, setVolumeType] = useState<VolumeType>("ALL");

  useEffect(() => {
    if (!chartContainerRef.current || !data?.data || !operation.plotConfig)
      return;

    const chart = createChart(chartContainerRef.current, getChartOptions());
    chartRef.current = chart;
    const toolTip = tooltipRef.current ?? createTooltip();
    if (!tooltipRef.current) tooltipRef.current = toolTip;

    const { buyPriceSeries, sellPriceSeries, volumeSeries } = createSeries(
      chart,
      operation
    );
    const processedData = processData(data.data, operation);

    updateChartData(
      buyPriceSeries,
      sellPriceSeries,
      volumeSeries,
      processedData,
      volumeType
    );
    setupCrosshairMove(chart, toolTip, processedData, operation);

    chart.timeScale().fitContent();
    onReady?.();

    const handleResize = () => resizeChart(chart);
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
      chart.remove();
    };
  }, [data, operation, height, onReady, theme, volumeType]);

  const getChartOptions = () => ({
    width: chartContainerRef.current!.clientWidth,
    height,
    layout: {
      background: { type: ColorType.Solid, color: "white" },
      textColor: "black",
    },
    grid: {
      vertLines: { color: "#e0e0e0" },
      horzLines: { color: "#e0e0e0" },
    },
    timeScale: {
      timeVisible: true,
      secondsVisible: true,
    },
    rightPriceScale: {
      scaleMargins: { top: 0.1, bottom: 0.4 },
    },
    leftPriceScale: {
      visible: true,
      scaleMargins: { top: 0.8, bottom: 0 },
    },
  });

  const createTooltip = () => {
    const toolTip = document.createElement("div");
    Object.assign(toolTip.style, {
      position: "absolute",
      display: "none",
      padding: "8px",
      boxSizing: "border-box",
      fontSize: "12px",
      textAlign: "left",
      zIndex: "100000",
      background: "white",
      color: "black",
      border: `1px solid ${theme.colors.gray[6]}`,
      borderRadius: "4px",
      pointerEvents: "none",
    });
    chartContainerRef.current!.appendChild(toolTip);
    return toolTip;
  };

  const createSeries = (chart: IChartApi, operation: Operation) => {
    const isScatter = operation.plotConfig?.type === "scatter";

    const createPriceSeries = (color: string, title: string) => {
      if (isScatter) {
        return chart.addLineSeries({
          color: color,
          lineWidth: 2,
          priceScaleId: "right",
          title: title,
          pointMarkersVisible: true,
          lineVisible: false,
        });
      } else {
        return chart.addAreaSeries({
          lineColor: color,
          topColor: color,
          bottomColor: "rgba(255, 255, 255, 0)",
          priceScaleId: "right",
          title: title,
        });
      }
    };

    const buyPriceSeries = createPriceSeries(theme.colors.gray[6], "Buy Price");
    const sellPriceSeries = createPriceSeries(
      theme.colors.red[6],
      "Sell Price"
    );

    const volumeSeries = chart.addHistogramSeries({
      priceFormat: { type: "volume" },
      priceScaleId: "left",
      color: theme.colors.gray[6],
    });

    chart.priceScale("left").applyOptions({
      scaleMargins: { top: 0.8, bottom: 0 },
      mode: PriceScaleMode.Normal,
    });

    return { buyPriceSeries, sellPriceSeries, volumeSeries };
  };

  const processData = (rawData: any[], operation: any): DataPoint[] => {
    const xField = operation.plotConfig.x!;
    const yField = operation.plotConfig.y![0];
    const quantityField = "Quantity";
    const sideField = "side";

    return rawData
      .map((item) => ({
        ...item,
        time: (new Date(item[xField]).getTime() / 1000) as UTCTimestamp,
        value: parseFloat(item[yField]),
        quantity: parseFloat(item[quantityField]),
        side: (item[sideField] as "BUY" | "SELL") || "BUY",
      }))
      .sort((a, b) => a.time - b.time)
      .reduce((acc, curr, index, array) => {
        if (index === 0 || curr.time > array[index - 1].time) {
          acc.push(curr);
        } else {
          const lastIndex = acc.length - 1;
          acc[lastIndex].quantity += curr.quantity;
        }
        return acc;
      }, [] as DataPoint[]);
  };

  const updateChartData = (
    buyPriceSeries: ISeriesApi<"Area"> | ISeriesApi<"Line">,
    sellPriceSeries: ISeriesApi<"Area"> | ISeriesApi<"Line">,
    volumeSeries: ISeriesApi<"Histogram">,
    processedData: DataPoint[],
    volumeType: VolumeType
  ) => {
    const buyPriceData = processedData
      .filter((item) => item.side === "BUY")
      .map(({ time, value }) => ({ time, value }));
    buyPriceSeries.setData(buyPriceData);

    const sellPriceData = processedData
      .filter((item) => item.side === "SELL")
      .map(({ time, value }) => ({ time, value }));
    sellPriceSeries.setData(sellPriceData);

    const volumeData = processedData.map((item, index, array) => {
      const nextTime =
        index < array.length - 1 ? array[index + 1].time : item.time + 1;
      let value = item.quantity;
      let color = theme.colors.gray[6];

      switch (volumeType) {
        case "BUY":
          value = item.side === "BUY" ? item.quantity : 0;
          color = theme.colors.gray[6];
          break;
        case "SELL":
          value = item.side === "SELL" ? item.quantity : 0;
          color = theme.colors.red[6];
          break;
        case "NET":
          value = item.side === "BUY" ? item.quantity : -item.quantity;
          color = value >= 0 ? theme.colors.gray[6] : theme.colors.red[6];
          break;
        case "ALL":
          color =
            item.side === "BUY" ? theme.colors.gray[6] : theme.colors.red[6];
          break;
      }

      return {
        time: item.time,
        value,
        color,
        timeEnd: nextTime as UTCTimestamp,
      };
    });

    volumeSeries.setData(volumeData);
  };

  const setupCrosshairMove = (
    chart: IChartApi,
    toolTip: HTMLDivElement,
    processedData: DataPoint[],
    operation: Operation
  ) => {
    chart.subscribeCrosshairMove((param) => {
      if (
        param.point === undefined ||
        !param.time ||
        param.point.x < 0 ||
        param.point.x > chartContainerRef.current!.clientWidth ||
        param.point.y < 0 ||
        param.point.y > chartContainerRef.current!.clientHeight
      ) {
        toolTip.style.display = "none";
      } else {
        const dataPoint = processedData.find((d) => d.time === param.time);

        if (dataPoint) {
          updateTooltipContent(toolTip, dataPoint, operation);
          positionTooltip(toolTip, param.point);
        } else {
          toolTip.style.display = "none";
        }
      }
    });
  };

  const updateTooltipContent = (
    toolTip: HTMLDivElement,
    dataPoint: DataPoint,
    operation: any
  ) => {
    let tooltipContent = `<div style="font-weight: bold;">Date: ${new Date(
      dataPoint.time * 1000
    ).toLocaleString()}</div>`;

    Object.entries(dataPoint).forEach(([key, value]) => {
      if (key !== "time") {
        const title =
          operation.columns.find((c: any) => c.name === key)?.title || key;
        tooltipContent += `<div>${title}: ${value}</div>`;
      }
    });

    toolTip.innerHTML = tooltipContent;
    toolTip.style.display = "block";
  };

  const positionTooltip = (
    toolTip: HTMLDivElement,
    point: { x: number; y: number }
  ) => {
    let left = point.x + 15;
    if (left > chartContainerRef.current!.clientWidth - toolTip.clientWidth) {
      left = point.x - toolTip.clientWidth - 15;
    }

    let top = point.y + 15;
    if (top > chartContainerRef.current!.clientHeight - toolTip.clientHeight) {
      top = point.y - toolTip.clientHeight - 15;
    }

    toolTip.style.left = `${left}px`;
    toolTip.style.top = `${top}px`;
  };

  const resetChart = () => {
    if (!chartRef.current) return;
    const chartApi = chartRef.current;
    chartApi.timeScale().fitContent();
  };

  const resizeChart = (chart: IChartApi) => {
    if (chartContainerRef.current) {
      chart.applyOptions({
        width: chartContainerRef.current.clientWidth,
        height: chartContainerRef.current.clientHeight,
      });
    }
  };

  return (
    <div style={{ position: "relative", height: "100%" }}>
      <div style={{ position: "absolute", top: 10, right: 100, zIndex: 1 }}>
        <Group>
          <Button onClick={resetChart}>Reset</Button>
        </Group>
      </div>
      <div
        ref={chartContainerRef}
        style={{ position: "absolute", inset: 0, zIndex: 0 }}
      />
    </div>
  );
};

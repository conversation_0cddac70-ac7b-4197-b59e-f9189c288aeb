import { DAMChart } from "./ApiDataPlotDAM";
import { isDisabledPlot } from "./constants";
import { DAMAggregatedChart } from "./DAM_Aggregated_Trading_Data";
import { IDA_QuarterHourly_v1 } from "./IDA_QuarterHourly_v1";
import { IDC_Hourly_v1 } from "./IDC_Hourly_v1";
import { IDC_v1 } from "./IDC_v1";
import { ISI_v1 } from "#root/src/components/plots/ISI_v1";
import { OthersChart } from "./Others";

const Empty = () => <></>;

export const getPlotForOperationId = (operationId: string) => {
  if (isDisabledPlot(operationId)) return Empty;
  switch (operationId) {
    case "DAM_Aggregated_curve_v1":
    case "IDA_Aggregated_curve_v1":
      return DAMChart;
      break;
    case "DAM_Aggregated_Trading_Data_v1":
      return DAMAggregatedChart;
      break;
    case "IDA_QuarterHourly_v1":
      return IDA_QuarterHourly_v1;
      break;
    case "IDC_v1":
      return IDC_v1;
      break;
    case "IDC_Hourly_v1":
    case "IDC_QuarterHourly_v1":
      return IDC_Hourly_v1;
      break;
    case "Imbalance_settlement_inputs_v1":
      return ISI_v1;
    default:
      return OthersChart;
      break;
  }
};

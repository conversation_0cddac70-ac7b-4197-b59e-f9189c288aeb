import { useProvisionUser } from "#root/src/api/users/query.tsx";
import { TextInput } from "#root/src/components/formfields/TextInput.tsx";
import { Button, Group, Modal } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";

type InviteUserFormValues = {
  email: string;
  firstName: string;
  lastName: string;
};

export const InviteUserButton = () => {
  const [opened, { open, close }] = useDisclosure(false);
  const form = useForm<InviteUserFormValues>();
  const provisionUser = useProvisionUser();
  const { t } = useTranslation();

  const submit = form.handleSubmit(async (values) => {
    await provisionUser.mutateAsync(values);
    form.reset();
    close();
  });

  const handleCancel = () => {
    form.reset();
    close();
  };

  return (
    <>
      <Button onClick={open}>Invite User</Button>
      <Modal opened={opened} onClose={close} title="Invite User">
        <FormProvider {...form}>
          <form onSubmit={submit}>
            <TextInput
              {...form.register("email")}
              label="Email"
              type="email"
              required
            />
            <TextInput
              {...form.register("firstName")}
              label="First Name"
              type="text"
              required
            />
            <TextInput
              {...form.register("lastName")}
              label="Last Name"
              type="text"
              required
            />
            <Group mt="md">
              <Button variant="outline" onClick={handleCancel}>
                {t("Cancel")}
              </Button>
              <Button type="submit" loading={provisionUser.isPending}>
                {t("Submit")}
              </Button>
            </Group>
          </form>
        </FormProvider>
      </Modal>
    </>
  );
};

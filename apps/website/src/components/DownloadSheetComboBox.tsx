import type { ComboboxStore } from "@mantine/core";
import {
  ActionIcon,
  Button,
  Combobox,
  Group,
  useCombobox,
} from "@mantine/core";
import { FaFileCsv, FaFileExcel } from "react-icons/fa";
import { FiDownload } from "react-icons/fi";
import type { FormatType } from "#root/src/api/tabular-formats/encode";

export type DownloadSheetComboBoxProps = {
  onClick: (option: FormatType, combobox: ComboboxStore) => void;
};

export const DownloadSheetComboBox = ({
  onClick,
}: DownloadSheetComboBoxProps) => {
  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const fileTypeIcon = (fileType: string) => {
    if (fileType === "csv") {
      return <FaFileCsv />;
    } else if (fileType === "xlsx") {
      return <FaFileExcel />;
    }
  };

  const options = ["csv", "xlsx"].map((item) => (
    <Combobox.Option value={item} key={item}>
      <Group>
        <ActionIcon>{fileTypeIcon(item)}</ActionIcon>
        {item}
      </Group>
    </Combobox.Option>
  ));

  return (
    <Combobox
      store={combobox}
      withArrow
      withinPortal={false}
      onOptionSubmit={(value) => onClick(value as FormatType, combobox)}
    >
      <Combobox.Target>
        <Button
          leftSection={<FiDownload />}
          onClick={() => combobox.toggleDropdown()}
          variant="light"
        >
          Download
        </Button>
      </Combobox.Target>

      <Combobox.Dropdown>
        <Combobox.Options>{options}</Combobox.Options>
      </Combobox.Dropdown>
    </Combobox>
  );
};

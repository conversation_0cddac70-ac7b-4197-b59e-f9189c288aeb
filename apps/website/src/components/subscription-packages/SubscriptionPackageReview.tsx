import type { CreateContractFormDTO } from "#root/src/api/contract/contract.service";
import { BlockTitle } from "#root/src/components/BlockTitle";
import { formatAddress } from "#root/src/utils/address";
import { Box, Button, Group, Paper, Stack, Text } from "@mantine/core";
import { useTranslation } from "react-i18next";

export type SubscriptionPackageReviewProps = {
  formData: CreateContractFormDTO;
  subscriptionPackageMap: Record<string, any>;
  onEdit: () => void;
  onConfirm: () => void;
  confirmButtonText?: string;
};

export const SubscriptionPackageReview = ({
  formData,
  subscriptionPackageMap,
  onEdit,
  onConfirm,
  confirmButtonText = "Confirm and Submit",
}: SubscriptionPackageReviewProps) => {
  const { t } = useTranslation();

  return (
    <Paper radius="md">
      <Stack gap="xl">
        <Text>{t("Please review your information before submitting")}</Text>
        <Box>
          <BlockTitle label={t("Company Information")} mb="md" />
          <Stack>
            <Group justify="space-between">
              <Text fw={500}>{t("Company Name")}:</Text>
              <Text>{formData.companyName}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Country")}:</Text>
              <Text>{formData.companyCountry}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Address")}:</Text>
              <Text>
                {formatAddress(
                  formData.companyStreetName || "",
                  formData.companyStreetType || "",
                  formData.companyHouseNumber || "",
                  formData.companyFloorDoor || ""
                )}
              </Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("City")}:</Text>
              <Text>{formData.companyCity}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("ZIP Code")}:</Text>
              <Text>{formData.companyZipCode}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("EU TAX Number")}:</Text>
              <Text>{formData.companyTaxNumber}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Company Registration Number")}:</Text>
              <Text>{formData.companyRegistrationNumber}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("E-invoice Email")}:</Text>
              <Text>{formData.companyInvoiceEmail}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Authorized Representative")}:</Text>
              <Text>{formData.companyAuthorizedRepName}</Text>
            </Group>
            {formData.companyAdditionalRepName && (
              <Group justify="space-between">
                <Text fw={500}>{t("Additional Representative")}:</Text>
                <Text>{formData.companyAdditionalRepName}</Text>
              </Group>
            )}
            <Group justify="space-between">
              <Text fw={500}>{t("Preferred Commencement Date")}:</Text>
              <Text>{formData.companyCommencementDate}</Text>
            </Group>
          </Stack>
        </Box>

        {!formData.billingIsSameAsCompanyAddress && (
          <Box>
            <BlockTitle label={t("Billing Information")} mb="md" />
            <Stack>
              <Group justify="space-between">
                <Text fw={500}>{t("Address")}:</Text>
                <Text>
                  {formatAddress(
                    formData.billingStreetName || "",
                    formData.billingStreetType || "",
                    formData.billingHouseNumber || "",
                    formData.billingFloorDoor || ""
                  )}
                </Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>{t("City")}:</Text>
                <Text>{formData.billingCity}</Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>{t("ZIP Code")}:</Text>
                <Text>{formData.billingZipCode}</Text>
              </Group>
            </Stack>
          </Box>
        )}

        <Box>
          <BlockTitle label={t("Main Contact")} mb="md" />
          <Stack>
            <Group justify="space-between">
              <Text fw={500}>{t("Name")}:</Text>
              <Text>{`${formData.mainContactFirstName} ${formData.mainContactLastName}`}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Title")}:</Text>
              <Text>{formData.mainContactPosition}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Phone")}:</Text>
              <Text>{formData.mainContactPhone}</Text>
            </Group>
            <Group justify="space-between">
              <Text fw={500}>{t("Email")}:</Text>
              <Text>{formData.mainContactEmail}</Text>
            </Group>
          </Stack>
        </Box>

        {formData.secondaryContacts.length > 0 && (
          <Box>
            <BlockTitle label={t("Secondary Contacts")} mb="md" />
            {formData.secondaryContacts.map((contact, index) => (
              <Box
                key={index}
                mb={index < formData.secondaryContacts.length - 1 ? "md" : 0}
              >
                <Stack>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Name")}:</Text>
                    <Text>{`${contact.firstName} ${contact.lastName}`}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Title")}:</Text>
                    <Text>{contact.position}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Phone")}:</Text>
                    <Text>{contact.phone}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Email")}:</Text>
                    <Text>{contact.email}</Text>
                  </Group>
                </Stack>
                {index < formData.secondaryContacts.length - 1 && (
                  <Box my="md" />
                )}
              </Box>
            ))}
          </Box>
        )}

        {formData.affiliates.length > 0 && (
          <Box>
            <BlockTitle label={t("Affiliated Companies")} mb="md" />
            {formData.affiliates.map((affiliate, index) => (
              <Box
                key={index}
                mb={index < formData.affiliates.length - 1 ? "md" : 0}
              >
                <Stack>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Company Name")}:</Text>
                    <Text>{affiliate.name}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Address")}:</Text>
                    <Text>{affiliate.address}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("City")}:</Text>
                    <Text>{affiliate.city}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("ZIP Code")}:</Text>
                    <Text>{affiliate.zipCode}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("EU TAX Number")}:</Text>
                    <Text>{affiliate.taxNumber}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Company Registration Number")}:</Text>
                    <Text>{affiliate.companyRegistration}</Text>
                  </Group>
                </Stack>
                {index < formData.affiliates.length - 1 && <Box my="md" />}
              </Box>
            ))}
          </Box>
        )}

        <Box>
          <BlockTitle label={t("Ordered Packages")} mb="md" />
          {formData.packages.map((package_, index) => {
            const packageInfo = subscriptionPackageMap[package_.packageId];
            return (
              <Box
                key={package_.packageId}
                mb={index < formData.packages.length - 1 ? "md" : 0}
              >
                <Stack>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Package Name")}:</Text>
                    <Text>{packageInfo.name}</Text>
                  </Group>
                  <Group justify="space-between">
                    <Text fw={500}>{t("Price")}:</Text>
                    <Text>{`${packageInfo.priceEur} EUR`}</Text>
                  </Group>
                  {(subscriptionPackageMap[package_.packageId].name ==
                    "Additional M7 API user" ||
                    subscriptionPackageMap[package_.packageId].name ==
                      "Additional Trayport user (Trading system)") && (
                    <Group justify="space-between">
                      <Text fw={500}>{t("Number of users")}:</Text>
                      <Text>{`${package_.quantity}`}</Text>
                    </Group>
                  )}
                  {subscriptionPackageMap[package_.packageId].name == 
                    "Historical IDC orders and trades via sFTP" && (
                    <Group justify="space-between">
                      <Text fw={500}>{t("Ordered historical months")}:</Text>
                      <Text>{`${package_.historicalData}`}</Text>
                    </Group>
                  )}
                </Stack>
                {index < formData.packages.length - 1 && <Box my="md" />}
              </Box>
            );
          })}
        </Box>

        <Group justify="flex-end" mt="xl">
          <Button variant="outline" onClick={onEdit} size="md">
            {t("Edit")}
          </Button>
          <Button onClick={onConfirm} size="md">
            {confirmButtonText}
          </Button>
        </Group>
      </Stack>
    </Paper>
  );
};

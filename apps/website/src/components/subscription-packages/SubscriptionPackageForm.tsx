import type { CreateContractFormDTO } from "#root/src/api/contract/contract.service";
import { useSubscriptionPackages } from "#root/src/api/subscription-packages/subscription-packages.query.ts";
import { BlockTitle } from "#root/src/components/BlockTitle";
import { Checkbox } from "#root/src/components/formfields/Checkbox";
import { SubscriptionPackageReview } from "#root/src/components/subscription-packages/SubscriptionPackageReview";
import {
  Anchor,
  Box,
  Button,
  Divider,
  Group,
  Select,
  Stack,
  Text,
  TextInput,
  Tooltip,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import { type FormEventHandler } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { MultiSelect } from "../formfields/MultiSelect";
import dayjs from "dayjs";
import { NumberInput } from "../formfields/NumberInput";

export type SubscriptionPackageFormProps = {
  initialValues?: Partial<CreateContractFormDTO>;
  onSubmit: (values: CreateContractFormDTO) => Promise<void>;
  submitButtonText?: string;
  showTermsText?: boolean;
  allowZeroPackages?: boolean;
};

export const SubscriptionPackageForm = ({
  initialValues,
  onSubmit,
  submitButtonText = "Submit",
  showTermsText = true,
  allowZeroPackages = false,
}: SubscriptionPackageFormProps) => {
  const { t } = useTranslation();
  const { data: subscriptionPackages } = useSubscriptionPackages();
  const subscriptionPackageMap = subscriptionPackages.reduce(
    (acc, pkg) => ({ ...acc, [pkg.id]: pkg }),
    {} as Record<string, (typeof subscriptionPackages)[0]>
  );

  const form = useForm<CreateContractFormDTO>({
    defaultValues: {
      ...initialValues,
    },
  });

  const {
    fields: packages,
    append: appendPackage,
    remove: removePackage,
    update: updatePackage,
  } = useFieldArray({
    control: form.control,
    name: "packages",
  });

  const {
    fields: affiliates,
    append: appendAffiliate,
    remove: removeAffiliate,
  } = useFieldArray({
    control: form.control,
    name: "affiliates",
  });

  const {
    fields: secondaryContacts,
    append: appendSecondaryContact,
    remove: removeSecondaryContact,
  } = useFieldArray({
    control: form.control,
    name: "secondaryContacts",
  });

  const isSubmitEnabled =
    allowZeroPackages || form.getValues("packages").length !== 0;
  const onReview: FormEventHandler<HTMLFormElement> = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    const isCancellation = form.getValues("packages").length === 0;
    if (isCancellation) {
      modals.openConfirmModal({
        title: "Confirm Subscription Cancellation",
        children: (
          <Text size="sm">
            You have no subscription packages selected in the update. Are you
            sure you want to cancel your subscription(s)?
          </Text>
        ),
        labels: { confirm: "Confirm", cancel: "Cancel" },
        onConfirm: () => {
          modals.closeAll();
          form.handleSubmit(onSubmit)(e);
        },
      });
      return;
    }

    modals.open({
      size: "xl",
      title: "Review your order",
      children: (
        <SubscriptionPackageReview
          formData={form.getValues()}
          subscriptionPackageMap={subscriptionPackageMap}
          onEdit={() => modals.closeAll()}
          onConfirm={async () => {
            modals.closeAll();
            form.handleSubmit(onSubmit)(e);
          }}
          confirmButtonText={submitButtonText}
        />
      ),
    });
  };

  const generateMonths = (): { value: string; label: string }[] => {
    const start = dayjs("2020-01-01");
    const end = dayjs();
    const months: { value: string; label: string }[] = [];

    let current = start;
    while (current.isBefore(end) || current.isSame(end, "month")) {
      months.push({
        value: current.format("YYYY-MM"),
        label: current.format("MMMM YYYY"),
      });
      current = current.add(1, "month");
    }

    return months;
  };

  return (
    <FormProvider {...form}>
      <form onSubmit={onReview}>
        <Stack>
          <TextInput
            {...form.register("companyName")}
            label="Company Name"
            required
          />
          <TextInput
            {...form.register("companyCountry")}
            label="Country"
            required
          />
          <Group grow>
            <TextInput
              {...form.register("companyZipCode")}
              label="ZIP Code"
              required
            />
            <TextInput
              {...form.register("companyCity")}
              label="City"
              required
            />
          </Group>
          <Group grow>
            <TextInput
              {...form.register("companyStreetName")}
              label="Street Name"
              required
            />
            <TextInput
              {...form.register("companyStreetType")}
              label="Street Type"
              required
            />
          </Group>
          <Group grow>
            <TextInput
              {...form.register("companyHouseNumber")}
              label="House Number"
              required
            />
            <TextInput
              {...form.register("companyFloorDoor")}
              label="Floor / Door"
            />
          </Group>
          <Checkbox
            {...form.register("billingIsSameAsCompanyAddress")}
            label="Billing address is the same"
          />

          {!form.watch("billingIsSameAsCompanyAddress") && (
            <>
              <Group grow>
                <TextInput
                  {...form.register("billingCountry")}
                  label="Billing Country"
                  required
                />
                <TextInput
                  {...form.register("billingZipCode")}
                  label="Billing ZIP Code"
                  required
                />
                <TextInput
                  {...form.register("billingCity")}
                  label="Billing City"
                  required
                />
              </Group>
              <Group grow>
                <TextInput
                  {...form.register("billingStreetName")}
                  label="Billing Street Name"
                  required
                />
                <TextInput
                  {...form.register("billingStreetType")}
                  label="Billing Street Type"
                  required
                />
                <TextInput
                  {...form.register("billingName")}
                  label="Billing Name"
                  required
                />
              </Group>
              <Group grow>
                <TextInput
                  {...form.register("billingHouseNumber")}
                  label="Billing House Number"
                  required
                />
                <TextInput
                  {...form.register("billingFloorDoor")}
                  label="Billing Floor / Door"
                />
              </Group>
            </>
          )}

          <TextInput
            {...form.register("companyInvoiceEmail")}
            label="E-invoice Delivery Email"
            required
          />
          <TextInput
            {...form.register("companyAuthorizedRepName")}
            label="Authorized Representative Name"
            required
          />
          <TextInput
            {...form.register("companyAdditionalRepName")}
            label="Additional Authorized Representative's Name"
          />
          <TextInput
            {...form.register("companyTaxNumber")}
            label="EU TAX Number"
            required
          />
          <TextInput
            {...form.register("companyRegistrationNumber")}
            label="Company Registration Number"
            required
          />
          <TextInput
            {...form.register("companyCommencementDate")}
            label="Preferred Commencement Date"
            required
            type="date"
          />

          <BlockTitle label={t("Affiliated Companies")} mb="md" />
          {affiliates.map((field, index) => (
            <Box key={field.id}>
              <Divider orientation="horizontal" />
              <Group mb="sm" w="100%" gap="xs" wrap={"nowrap"} align="flex-end">
                <Box style={{ flex: 1 }}>
                  <TextInput
                    {...form.register(`affiliates.${index}.name`)}
                    label="Company Name"
                    required
                  />
                </Box>
                <Button
                  variant="light"
                  color="red"
                  size="xs"
                  mb={"3"}
                  onClick={() => removeAffiliate(index)}
                >
                  Remove
                </Button>
              </Group>

              <Group grow>
                <TextInput
                  {...form.register(`affiliates.${index}.zipCode`)}
                  label="ZIP Code"
                  required
                />
                <TextInput
                  {...form.register(`affiliates.${index}.city`)}
                  label="City"
                  required
                />
              </Group>
              <Group grow>
                <TextInput
                  {...form.register(`affiliates.${index}.address`)}
                  label="Address"
                  required
                />
              </Group>
              <TextInput
                {...form.register(`affiliates.${index}.taxNumber`)}
                label="EU TAX Number"
                required
              />
              <TextInput
                {...form.register(`affiliates.${index}.companyRegistration`)}
                label="Company Registration Number"
                required
              />
              <NumberInput
                name={`affiliates.${index}.customersHoldingPercentage`}
                label="Customers Holding Percentage [%]"
                required
                allowDecimal={false}
                allowNegative={false}
                max={100}
                min={0}
              />
            </Box>
          ))}
          <Button
            variant="outline"
            onClick={() =>
              appendAffiliate({
                name: "",
                taxNumber: "",
                companyRegistration: "",
                address: "",
                city: "",
                zipCode: "",
                country: "",
                customersHoldingPercentage: 0,
              })
            }
          >
            {t("Add Affiliated Company")}
          </Button>

          <BlockTitle
            label={t("User Contacts Authorized to deal with HUPX")}
            mb="md"
          />
          <TextInput
            {...form.register("mainContactFirstName")}
            label="Main Contact First Name"
            required
          />
          <TextInput
            {...form.register("mainContactLastName")}
            label="Main Contact Last Name"
            required
          />
          <TextInput
            {...form.register("mainContactPosition")}
            label="Main Contact Title"
            required
          />
          <TextInput
            {...form.register("mainContactPhone")}
            label="Main Contact Phone"
            required
          />
          <TextInput
            {...form.register("mainContactEmail")}
            label="Main Contact Email"
            required
          />
          <BlockTitle label={t("Secondary Contacts")} mb="md" />
          {secondaryContacts.map((field, index) => (
            <Box
              key={field.id}
              p="md"
              style={{
                border: "1px solid #ccc",
                borderRadius: 8,
                marginBottom: 16,
              }}
            >
              <Group w="100%" gap="xs" wrap={"nowrap"} align="flex-end">
                <TextInput
                  {...form.register(`secondaryContacts.${index}.firstName`)}
                  label="First Name"
                  style={{ flex: 1 }}
                />
                <TextInput
                  {...form.register(`secondaryContacts.${index}.lastName`)}
                  label="Last Name"
                  style={{ flex: 1 }}
                />
              </Group>
              <Group grow mt="xs">
                <TextInput
                  {...form.register(`secondaryContacts.${index}.position`)}
                  label="Title"
                />
                <TextInput
                  {...form.register(`secondaryContacts.${index}.phone`)}
                  label="Phone"
                />
                <TextInput
                  {...form.register(`secondaryContacts.${index}.email`)}
                  label="Email"
                />
              </Group>
              <Button
                variant="light"
                color="red"
                w={"100%"}
                mt={"md"}
                onClick={() => removeSecondaryContact(index)}
              >
                Remove
              </Button>
            </Box>
          ))}
          <Button variant="outline" onClick={() => appendSecondaryContact({})}>
            {t("Add Secondary Contact")}
          </Button>
          <BlockTitle label={t("Ordered Packages")} mb="md" />

          {packages.map((package_, index) => (
            <Box
              key={package_.id}
              p="md"
              style={{
                border: "1px solid #ccc",
                borderRadius: 8,
                marginBottom: 16,
              }}
            >
              <Group mb="sm" w="100%" gap="xs" wrap="nowrap" align="flex-end">
                <Box style={{ flex: 1 }}>
                  <Select
                    label="Package"
                    value={package_.packageId}
                    name={`packages.${index}`}
                    required
                    data={subscriptionPackages
                      .filter(
                        (pkg) =>
                          pkg.id === package_.packageId ||
                          !packages.some(
                            (p) =>
                              p.packageId === pkg.id && p.id !== package_.id
                          )
                      )
                      .map((pkg) => ({
                        value: pkg.id,
                        label: pkg.name,
                      }))}
                    onChange={(value) => {
                      if (!value) {
                        return;
                      }
                      updatePackage(index, {
                        price: subscriptionPackageMap[value].priceEur,
                        packageId: value,
                      });
                    }}
                  />
                </Box>
                <Button
                  variant="light"
                  color="red"
                  size="xs"
                  mb="3"
                  onClick={() => {
                    removePackage(index);
                  }}
                >
                  Remove
                </Button>
              </Group>

              <Group grow>
                <TextInput
                  value={package_.price}
                  label="Package Price (EUR)"
                  type="number"
                  readOnly
                />

                <TextInput value="EUR" label="Currency" required readOnly />

                {(subscriptionPackageMap[package_.packageId].name ==
                  "Additional M7 API user" ||
                  subscriptionPackageMap[package_.packageId].name ==
                    "Additional Trayport user (Trading system)") && (
                  <TextInput
                    {...form.register(`packages.${index}.quantity`, {
                      valueAsNumber: true,
                    })}
                    label="Number of users"
                    type="number"
                    required
                  />
                )}

                {subscriptionPackageMap[package_.packageId].name ===
                  "Historical IDC orders and trades via sFTP" && (
                  <MultiSelect
                    label="Select Historical Data"
                    data={generateMonths()}
                    placeholder="Select options"
                    name={`packages.${index}.historicalData`}
                    required
                  />
                )}
              </Group>
            </Box>
          ))}

          <Button
            variant="outline"
            onClick={() => {
              const availablePackage = subscriptionPackages.find(
                (pkg) => !packages.some((p) => p.packageId === pkg.id)
              );

              if (!availablePackage) {
                return;
              }

              appendPackage({
                price: availablePackage.priceEur || 0,
                packageId: availablePackage.id,
              });
            }}
          >
            {t("Add Package")}
          </Button>

          {isSubmitEnabled ? (
            <Button mt="md" type="submit" loading={form.formState.isSubmitting}>
              {submitButtonText ?? t("Review Order")}
            </Button>
          ) : (
            <Tooltip label={"Add at least one package to enable order"}>
              <Button mt="md" type="submit" disabled>
                {submitButtonText ?? t("Review Order")}
              </Button>
            </Tooltip>
          )}
        </Stack>
      </form>
      {showTermsText && (
        <Text mt="xs" fw="400" fz="14" c="#595959">
          By registering, you agree to{" "}
          <Anchor
            href="/terms"
            target="_blank"
            size="sm"
            c="gray"
            underline="always"
          >
            Terms and Conditions
          </Anchor>
          .
        </Text>
      )}
    </FormProvider>
  );
};

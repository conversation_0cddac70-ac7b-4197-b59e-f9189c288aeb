import { TextInput as MantineTextInput, Text } from "@mantine/core";
import type { TextInputProps } from "./types";
import { ErrorMessage } from "./ErrorMessage";
import { forwardRef } from "react";
import { useShortHandRequireController } from "#root/src/components/formfields/useShortHandRequireController";

export const TextInput = forwardRef<HTMLInputElement, TextInputProps>(
  (props, ref) => {
    const { label, name, format = (v) => v, ...rest } = props;
    const {
      rules,
      field,
      fieldState: { error: fieldError },
    } = useShortHandRequireController(props);

    const error = fieldError ? (
      fieldError?.message ? (
        <ErrorMessage>{fieldError.message?.toString()}</ErrorMessage>
      ) : (
        true
      )
    ) : undefined;

    return (
      <MantineTextInput
        id={name}
        label={
          <Text style={{ fontWeight: "500", display: "inline" }}>{label}</Text>
        }
        error={error}
        {...field}
        {...rest}
        required={false}
        withAsterisk={!!rules.required}
        onChange={field.onChange}
        onBlur={field.onBlur}
        value={field.value ? format(field.value) : ""}
        ref={ref}
      />
    );
  }
);

TextInput.displayName = "TextInput";

import { ErrorMessage } from "#root/src/components/formfields/ErrorMessage";
import type { ControllerFieldState } from "react-hook-form";
import type { ReactNode } from "react";

export const FormControlErrorIndicator = ({
  fieldState,
}: {
  fieldState: ControllerFieldState;
}): ReactNode => {
  // If there's no error, return undefined
  if (!fieldState.error) {
    return undefined;
  }

  // If there's an error with a message, return the error message component
  if (fieldState.error.message) {
    return <ErrorMessage>{fieldState.error.message.toString()}</ErrorMessage>;
  }

  // Otherwise, return true
  return true;
};

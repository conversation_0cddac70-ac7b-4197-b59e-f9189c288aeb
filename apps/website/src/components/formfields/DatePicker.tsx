import { DatePickerInput as MantineDateInput } from "@mantine/dates";
import type { DatePickerProps } from "./types";
import { ErrorMessage } from "./ErrorMessage";
import { useShortHandRequireController } from "#root/src/components/formfields/useShortHandRequireController";

export function DatePicker(props: DatePickerProps) {
  const { label, name, ...rest } = props;
  const {
    field,
    fieldState: { error: fieldError },
  } = useShortHandRequireController(props);

  const error = fieldError ? (
    fieldError?.message ? (
      <ErrorMessage>{fieldError.message?.toString()}</ErrorMessage>
    ) : (
      true
    )
  ) : undefined;

  return (
    <MantineDateInput
      id={name}
      label={label}
      locale={"en"}
      error={error}
      {...rest}
      {...field}
      {...(rest.type === "range" && {
        value: field.value ?? [null, null],
      })}
    />
  );
}

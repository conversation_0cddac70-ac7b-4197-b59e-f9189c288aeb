import { MultiSelect as MantineMultiSelect } from "@mantine/core";
import { startTransition } from "react";
import type { MultiSelectProps } from "./types";
import { Controller } from "react-hook-form";
import { FormControlErrorIndicator } from "#root/src/components/formfields/FormControlErrorIndicator";

function MultiSelectComponent(props: MultiSelectProps) {
  return (
    <Controller
      name={props.name}
      rules={props}
      render={({ field, fieldState }) => (
        <MantineMultiSelect
          ref={field.ref}
          withScrollArea={false}
          required={props.required}
          label={props.label}
          data={props.data}
          clearable={props.clearable}
          readOnly={props.readOnly}
          value={field.value || []}
          styles={{
            ...props.styles,
            dropdown: { maxHeight: 200, overflowY: "auto" },
            section: {
              pointerEvents:
                props.clearable && Array.isArray(field.value) ? "all" : "none",
            },
          }}
          onChange={(selectedValues) => {
            startTransition(() => {
              field.onChange(selectedValues);
            });
          }}
          error={FormControlErrorIndicator({ fieldState })}
        />
      )}
    />
  );
}

export const MultiSelect = MultiSelectComponent;

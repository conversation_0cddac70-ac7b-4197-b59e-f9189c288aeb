import { Select as MantineSelect } from "@mantine/core";
import type { SelectProps } from "./types";
import { useController } from "react-hook-form";
import { FormControlErrorIndicator } from "#root/src/components/formfields/FormControlErrorIndicator";

const SelectComponent = (props: SelectProps) => {
  const { field, fieldState } = useController({
    name: props.name,
    rules: props.rules,
  });

  return (
    <MantineSelect
      styles={{
        ...props.styles,
        section: {
          pointerEvents: field.value && props.clearable ? "all" : "none",
        },
      }}
      label={props.label}
      onChange={(value) => {
        field.onChange(value);
        // Call onAfterChange if provided
        if (props.onAfterChange && value) {
          props.onAfterChange(value);
        }
      }}
      onBlur={field.onBlur}
      error={FormControlErrorIndicator({ fieldState })}
      readOnly={props.readOnly}
      clearable={props.clearable}
      data={props.data}
      value={field.value}
      required={props.required}
    />
  );
};

export const Select = SelectComponent;

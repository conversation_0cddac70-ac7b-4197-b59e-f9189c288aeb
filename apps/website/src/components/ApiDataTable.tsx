import { useApiData, useOperation } from "#root/src/openapi/openapi.query";
import { useMantineTheme } from "@mantine/core";
import type { ColDef } from "ag-grid-community";
import "ag-grid-community/styles/ag-grid.css"; // Mandatory CSS required by the grid
import "ag-grid-community/styles/ag-theme-material.css"; // Optional Theme applied to the grid
import { AgGridReact } from "ag-grid-react"; // React Data Grid Component
import { useMemo } from "react";
import type { Operation } from "../openapi/openapi";
import { assert } from "../shared/assert";

const autoSizeStrategy = {
  type: "fitCellContents",
  defaultMinWidth: 100,
} as const;

export const ApiDataTable = ({
  operationId,
  onReady,
  height,
  params,
}: {
  operationId: string;
  onReady?: () => void;
  height?: number;
  params?: {
    [key: string]: string | number | string[] | number[] | Date[];
  };
  embedToken?: string;
  origReferer?: string;
}) => {
  const theme = useMantineTheme();

  const operation = useOperation({ operationId });
  const { data } = useApiData({ operationId, params });

  const rowData = useMemo(() => {
    // Add null checks
    if (!data?.data) return [];
    return getTableData({ data: data.data, operation });
  }, [data?.data, operation]);

  const columnDefs = useMemo(() => {
    if (!data?.data) return [];
    return getColDefs({ data: data.data, operation });
  }, [data?.data, operation]);

  return (
    <>
      <div
        className="ag-theme-material"
        style={{
          height: height ?? "100%",
          display: "flex",
          flexDirection: "column",
        }}
        css={{ position: "relative" }}
      >
        <AgGridReact
          domLayout={"normal"}
          onGridReady={(prms) => {
            onReady?.();
          }}
          autoSizeStrategy={autoSizeStrategy}
          rowData={rowData}
          columnDefs={columnDefs}
        />
      </div>
    </>
  );
};

function getTableData<T>({ data }: { data: T[]; operation: Operation }) {
  if (!data.length) {
    return [];
  }
  const rowData = Object.values(data).flat();
  return rowData;
}

const valueFormatter = (value: number): string => {
  if (Number.isInteger(value)) {
    return value.toString();
  }

  const limitedNum = parseFloat(value.toFixed(3));

  const strNum = limitedNum.toString();
  const [whole, decimal] = strNum.split(".");

  if (!decimal) {
    assert(whole);
    return whole;
  }

  const trimmedDecimal = decimal.replace(/0+$/, "");

  if (!trimmedDecimal) {
    assert(whole);
    return whole;
  }

  return `${whole}.${trimmedDecimal}`;
};

function getColDefs<T>({
  data,
  operation,
}: {
  data: T[];
  operation: Operation;
}) {
  if (!data.length) {
    return [];
  }

  const columnDefs: ColDef[] = operation.columns.map((col) => ({
    field: col.name,
    headerName: col.title || "No title",
    sortable: true,
    valueFormatter: (params) => {
      if (typeof params.value === "number") {
        return valueFormatter(params.value);
      }
      return params.value as string;
    },
  }));

  return columnDefs;
}

import { Card, Group, Text } from "@mantine/core";
import { useOperationFilters } from "../filters/store";
import { useOperation } from "../openapi/openapi.query";
import ApiDataPlotClientOnly from "./ApiDataPlotClientOnly";

export const ApiDataCardSimple = ({ operationId }: { operationId: string }) => {
  const { params } = useOperationFilters(operationId);
  return <ApiDataPlotClientOnly operationId={operationId} params={params} />;
};

export const ApiDataCard = ({ operationId }: { operationId: string }) => {
  const operation = useOperation({ operationId });
  const { params } = useOperationFilters(operationId);

  return (
    <Card component="a" href={`/view/${operationId}`}>
      <Card.Section>
        <Group>
          <Text fw={500} size="xl">
            {operation.summary!}
          </Text>
        </Group>
      </Card.Section>
      <Card.Section h={250}>
        <ApiDataPlotClientOnly operationId={operationId} params={params} />
      </Card.Section>
    </Card>
  );
};

export default ApiDataCard;

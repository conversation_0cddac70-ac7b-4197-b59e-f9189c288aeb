import { useAuth } from "#root/src/api/auth/auth.query";
import { useDeletePartner } from "#root/src/api/partners/partner.query";
import {
  useAdminPartners,
  useAdminUsers,
  useDeleteUser,
} from "#root/src/api/users/query";
import { DateTime } from "#root/src/components/DateTime";
import {
  ActionIcon,
  Badge,
  Button,
  Group,
  Modal,
  Stack,
  Text,
  TextInput,
  Title,
  Tooltip,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { DataTable } from "mantine-datatable";
import { useState } from "react";
import { FiDelete } from "react-icons/fi";

const ADMIN_EMAIL = "<EMAIL>";

export const Page = () => {
  const auth = useAuth();
  const { data: users = [] } = useAdminUsers();
  const { data: partners = [] } = useAdminPartners();

  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [selectedPartner, setSelectedPartner] = useState<any>(null);
  const [userDeleteOpened, { open: openUserDelete, close: closeUserDelete }] =
    useDisclosure(false);
  const [
    partnerDeleteOpened,
    { open: openPartnerDelete, close: closePartnerDelete },
  ] = useDisclosure(false);

  if (!auth.profile || auth.profile.email !== ADMIN_EMAIL) {
    return (
      <div
        style={{
          display: "flex",
          padding: 14,
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
        }}
      >
        <Text size="xl" c="red">
          Access Denied: This page is restricted to {ADMIN_EMAIL}
        </Text>
      </div>
    );
  }

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    openUserDelete();
  };

  const handleDeletePartner = (partner) => {
    setSelectedPartner(partner);
    openPartnerDelete();
  };

  return (
    <>
      <div style={{ display: "flex", padding: 14 }}>
        <Title order={2}>Admin Panel</Title>
      </div>

      <Stack gap="xl" p={14}>
        <div>
          <Title order={4} mb="md">
            Users
          </Title>
          <DataTable
            withTableBorder
            borderRadius="sm"
            withColumnBorders
            striped
            highlightOnHover
            columns={[
              {
                accessor: "id",
                title: "ID",
                width: 80,
              },
              {
                accessor: "email",
                title: "Email",
                width: 250,
              },
              {
                accessor: "firstName",
                title: "First Name",
                width: 120,
              },
              {
                accessor: "lastName",
                title: "Last Name",
                width: 120,
              },
              {
                accessor: "type",
                title: "Type",
                width: 120,
                render: (record) => (
                  <Badge variant="light">{record.type}</Badge>
                ),
              },
              {
                accessor: "status",
                title: "Status",
                width: 100,
                render: (record) => (
                  <Badge
                    color={record.status === "ACTIVE" ? "green" : "orange"}
                    variant="light"
                  >
                    {record.status}
                  </Badge>
                ),
              },
              {
                accessor: "partner",
                title: "Partner",
                width: 180,
                render: (record) => (
                  <Text size="sm">{record.partner?.name || "No Partner"}</Text>
                ),
              },
              {
                accessor: "createdAt",
                title: "Created",
                width: 120,
                render: (record) => (
                  <DateTime
                    date={record.createdAt}
                    format={(d) => d.toLocaleDateString()}
                  />
                ),
              },
              {
                accessor: "actions",
                title: "Actions",
                width: 80,
                render: (record) => (
                  <UserActions onDelete={() => handleDeleteUser(record)} />
                ),
              },
            ]}
            records={users}
            emptyState={
              <Stack align="center" gap="xs" py="xl">
                <Text size="sm" c="dimmed">
                  No users found
                </Text>
              </Stack>
            }
          />
        </div>

        <div>
          <Title order={4} mb="md">
            Partners
          </Title>
          <DataTable
            withTableBorder
            borderRadius="sm"
            withColumnBorders
            striped
            highlightOnHover
            columns={[
              {
                accessor: "id",
                title: "ID",
                width: 80,
              },
              {
                accessor: "name",
                title: "Name",
                width: 250,
              },
              {
                accessor: "country",
                title: "Country",
                width: 120,
              },
              {
                accessor: "status",
                title: "Status",
                width: 120,
                render: (record) => (
                  <Badge
                    color={record.status === "active" ? "green" : "red"}
                    variant="light"
                  >
                    {record.status}
                  </Badge>
                ),
              },
              {
                accessor: "createdAt",
                title: "Created",
                width: 120,
                render: (record) => (
                  <DateTime
                    date={record.createdAt}
                    format={(d) => d.toLocaleDateString()}
                  />
                ),
              },
              {
                accessor: "subscriptionPackageAssignment",
                title: "Subscriptions",
                width: 120,
                render: (record) => (
                  <Text size="sm">
                    {record.subscriptionPackageAssignment?.length || 0}
                  </Text>
                ),
              },
              {
                accessor: "actions",
                title: "Actions",
                width: 80,
                render: (record) => (
                  <PartnerActions
                    onDelete={() => handleDeletePartner(record)}
                  />
                ),
              },
            ]}
            records={partners}
            emptyState={
              <Stack align="center" gap="xs" py="xl">
                <Text size="sm" c="dimmed">
                  No partners found
                </Text>
              </Stack>
            }
          />
        </div>
      </Stack>

      <DeleteUserModal
        user={selectedUser}
        opened={userDeleteOpened}
        onClose={closeUserDelete}
      />

      <DeletePartnerModal
        partner={selectedPartner}
        opened={partnerDeleteOpened}
        onClose={closePartnerDelete}
      />
    </>
  );
};

const UserActions = ({ onDelete }) => {
  return (
    <Group gap="xs">
      <Tooltip label="Delete User">
        <ActionIcon color="red" variant="light" onClick={onDelete}>
          <FiDelete size={16} />
        </ActionIcon>
      </Tooltip>
    </Group>
  );
};

const PartnerActions = ({ onDelete }) => {
  return (
    <Group gap="xs">
      <Tooltip label="Delete Partner">
        <ActionIcon color="red" variant="light" onClick={onDelete}>
          <FiDelete size={16} />
        </ActionIcon>
      </Tooltip>
    </Group>
  );
};

const DeleteUserModal = ({ user, opened, onClose }) => {
  const [confirmationText, setConfirmationText] = useState("");
  const deleteUser = useDeleteUser();

  if (!user) return null;

  const isConfirmationValid = confirmationText === user.email;

  const handleDelete = async () => {
    if (!isConfirmationValid) return;

    try {
      await deleteUser.mutateAsync(user.id);
      onClose();
      setConfirmationText("");
    } catch (error) {
      console.error("Failed to delete user:", error);
    }
  };

  const handleClose = () => {
    onClose();
    setConfirmationText("");
  };

  return (
    <Modal opened={opened} onClose={handleClose} title="Delete User" size="md">
      <Stack gap="md">
        <Text c="red" fw={500}>
          Are you sure you want to delete this user?
        </Text>

        <Text size="sm">
          <strong>User:</strong> {user.firstName} {user.lastName} ({user.email})
        </Text>

        <Text size="sm" c="orange" fw={500}>
          Note: This will also delete any registrations with the same email
          address.
        </Text>

        <Text size="sm" c="dimmed">
          This action cannot be undone. To confirm, please type the user&apos;s
          email address:
        </Text>

        <TextInput
          placeholder={user.email}
          value={confirmationText}
          onChange={(e) => setConfirmationText(e.currentTarget.value)}
          error={
            confirmationText && !isConfirmationValid
              ? "Email address does not match"
              : null
          }
        />

        <Group mt="md">
          <Button variant="light" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            color="red"
            disabled={!isConfirmationValid}
            loading={deleteUser.isPending}
            onClick={handleDelete}
          >
            Delete User
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

const DeletePartnerModal = ({ partner, opened, onClose }) => {
  const [confirmationText, setConfirmationText] = useState("");
  const deletePartner = useDeletePartner();

  if (!partner) return null;

  const isConfirmationValid = confirmationText === partner.name;

  const handleDelete = async () => {
    if (!isConfirmationValid) return;

    try {
      await deletePartner.mutateAsync(partner.id);
      onClose();
      setConfirmationText("");
    } catch (error) {
      console.error("Failed to delete partner:", error);
    }
  };

  const handleClose = () => {
    onClose();
    setConfirmationText("");
  };

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      title="Delete Partner"
      size="md"
    >
      <Stack gap="md">
        <Text c="red" fw={500}>
          Are you sure you want to delete this partner?
        </Text>

        <Text size="sm">
          <strong>Partner:</strong> {partner.name}
        </Text>

        <Text size="sm" c="red" fw={500}>
          WARNING: This will also delete ALL related data:
        </Text>

        <Text size="sm" c="dimmed" component="ul" style={{ paddingLeft: 20 }}>
          <li>All users belonging to this partner</li>
          <li>All registrations with matching user emails</li>
          <li>All contracts and contract-related data</li>
          <li>All main partner contacts</li>
          <li>All subscription assignments</li>
          <li>All user data (API keys, embeddings, announcements, etc.)</li>
        </Text>

        <Text size="sm" c="dimmed">
          This action cannot be undone. To confirm, please type the
          partner&apos;s name:
        </Text>

        <TextInput
          placeholder={partner.name}
          value={confirmationText}
          onChange={(e) => setConfirmationText(e.currentTarget.value)}
          error={
            confirmationText && !isConfirmationValid
              ? "Partner name does not match"
              : null
          }
        />

        <Group mt="md">
          <Button variant="light" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            color="red"
            disabled={!isConfirmationValid}
            loading={deletePartner.isPending}
            onClick={handleDelete}
          >
            Delete Partner
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

import { DataTable } from "mantine-datatable";
import { useDebouncedCallback, useDebouncedValue } from "@mantine/hooks";
import { useMemo } from "react";
import { useEvents } from "#root/src/api/events/query.tsx";
import { Title } from "@mantine/core";
import JsonView from "@uiw/react-json-view";
import { DateTime } from "#root/src/components/DateTime.tsx";

export const Page = () => {
  const {
    query,
    setPage,
    page,
    setUserIdFilter,
    userIdFilter,
    typeFilter,
    setTypeFilter,
  } = useEvents();

  const [debouncedUserId] = useDebouncedValue(userIdFilter, 300);

  const data = useMemo(() => query.data || [], [query.data]);

  return (
    <>
      <Title order={4} css={{ paddingBottom: 16 }}>
        Audit Log
      </Title>

      <DataTable
        records={data}
        columns={[
          { accessor: "id", title: "ID", hidden: true },
          {
            accessor: "userId",
            title: "User ID",
            render: ({ userId }) => userId ?? "System",
          },
          { accessor: "eventType", title: "Event Type" },
          {
            accessor: "createdAt",
            title: "Created At",
            render: ({ createdAt }) => (
              <DateTime
                date={createdAt}
                format={(d) => d.toLocaleString("hu")}
              />
            ),
          },
          {
            accessor: "extraEventData",
            title: "Context",
            render: ({ extraEventData }) => (
              <JsonView
                value={(extraEventData as object) ?? {}}
                collapsed={true}
              />
            ),
          },
        ]}
      />
    </>
  );
};

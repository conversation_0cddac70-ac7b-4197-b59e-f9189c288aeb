import { useAuth, useProfile } from "#root/src/api/auth/auth.query";
import { FULL_HEADER_HEIGHT } from "#root/src/components/Header";
import {
  Box,
  Button,
  Collapse,
  Skeleton,
  useMantineTheme,
} from "@mantine/core";
import { Suspense } from "react";
import { usePageContext } from "vike-react/usePageContext";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";
import { useDisclosure } from "@mantine/hooks";
import { dark } from "../../theme";
import { type RawScopeDefinition, scope } from "#root/src/shared/rbac.ts";
import {
  ContractStatus,
  ContractType,
  FormType,
  UserType,
} from "#root/src/shared/constants";
import { assert } from "#root/src/shared/assert";
import { useContractsForPartner } from "#root/src/api/contract/contract.query";

const MenuSection = ({
  label,
  entries,
}: {
  label: string;
  entries: [string, string, RawScopeDefinition | undefined][];
}) => {
  const theme = useMantineTheme();
  const [opened, { toggle }] = useDisclosure(true);
  const auth = useAuth();
  const filteredEntries = entries.filter((x) => !x[2] || auth.hasScope(x[2]));
  if (!filteredEntries.length) {
    return undefined;
  }
  return (
    <>
      <Box
        css={{
          width: "100%",
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          fontWeight: 600,
          backgroundColor: theme.colors[theme.primaryColor][1],
          height: 40,
        }}
        onClick={toggle}
      >
        <Button
          variant="transparent"
          color={theme.colors[theme.primaryColor][8]}
        >
          {label}
        </Button>
        <div
          css={{
            marginLeft: "auto",
            display: "flex",
          }}
        >
          {opened ? (
            <MdKeyboardArrowUp size={"20"} />
          ) : (
            <MdKeyboardArrowDown size={"20"} />
          )}
        </div>
      </Box>

      <Collapse in={opened}>
        {filteredEntries.map((x) => (
          <SideNavLink key={x[0]} href={x[0]} title={x[1]} scope={x[2]} />
        ))}
      </Collapse>
    </>
  );
};

const SideNavLink = ({
  href,
  title,
  scope,
}: {
  href: string;
  title: string;
  scope?: RawScopeDefinition;
}) => {
  const theme = useMantineTheme();
  const ctx = usePageContext();
  const route = ctx.urlPathname;
  const isActive = route === href || route.startsWith(href + "/");
  const auth = useAuth();
  if (scope && !auth.hasScope(scope)) {
    return undefined;
  }

  return (
    <a
      css={{
        position: "relative",
        backgroundColor: isActive ? "#4f575f0a" : "none",
        display: "flex",
        alignItems: "center",
        gap: theme.spacing.sm,
        textDecoration: "none",
        fontSize: theme.fontSizes.sm,
        color: theme.colors.gray[7],
        paddingLeft: theme.spacing.md,
        height: 46,
        fontWeight: 500,
        [dark]: {
          color: theme.colors.dark[1],
        },
      }}
      data-active={undefined}
      href={href}
    >
      <div
        css={{
          position: "absolute",
          left: 0,
          top: 0,
          bottom: 0,
          width: 5,
          backgroundColor: theme.colors[theme.primaryColor][6],
          opacity: isActive ? 1 : 0,
          borderTopRightRadius: theme.radius.md,
          borderBottomRightRadius: theme.radius.md,
        }}
      ></div>
      <span>{title}</span>
    </a>
  );
};

function NavbarSimple() {
  const theme = useMantineTheme();
  const { data: user } = useProfile();
  assert(user);

  return (
    <nav
      style={{
        // height: `calc(100% - ${FULL_HEADER_HEIGHT}px)`,
        top: FULL_HEADER_HEIGHT,
      }}
      css={{
        position: "fixed",
        background: "white",
        fontWeight: 600,
        left: 0,
        bottom: 0,
        width: 250,
        marginRight: theme.spacing.md,
        borderRight: `1px solid ${theme.colors.gray[2]}`,
      }}
    >
      <div
        css={{
          height: "calc(100% - 36px);",
          overflow: "auto",
        }}
      >
        <>
          <MenuSection
            label="Settings"
            entries={[
              ["/settings/general", "General", undefined],
              ["/settings/api-keys", "API Keys", undefined],
              ["/settings/this-user", "Settings", undefined],
              ["/settings/embeddings", "Embeddings", undefined],

              // [
              //   `/settings/partners/${user?.partnerId}/edit`,
              //   "Partner Settings",
              //   scope("partner").RW,
              // ],
            ]}
          />
          {user.partnerId && <SubscriptionsSection />}

          <MenuSection
            label="Partners/Users"
            entries={[
              ["/settings/partners", "Partners", scope("partners").RW],
              ["/settings/users", "Users", scope("users").RW],
            ]}
          />

          <MenuSection
            label="Other"
            entries={[
              ["/settings/new-change", "New Change", scope("changelog").W],
              [
                "/settings/data-requests",
                "Data Requests",
                scope("dataRequests").R,
              ],
              ["/settings/audit", "Audit Log", scope("auditLog").R],
              [
                "/settings/subscription-packages",
                "Subscription Packages",
                scope("subscriptionPackages").R,
              ],
            ]}
          />
        </>
      </div>
    </nav>
  );
}

const SubscriptionsSection = () => {
  const { data: user } = useProfile();
  assert(user);
  assert(user.partnerId);
  const { data: contracts } = useContractsForPartner(user.partnerId);
  const isPartnerAdmin = user.type === UserType.PARTNER_ADMIN;
  const hasAcceptedOrPendingContracts = contracts.some(
    (c) =>
      c.status === ContractStatus.ACCEPTED ||
      c.status === ContractStatus.PENDING
  );
  const hasAtLeastOneAcceptedAndNoPendingContract =
    contracts.some((c) => c.status === ContractStatus.ACCEPTED) &&
    !contracts.some((c) => c.status === ContractStatus.PENDING);

  // these are kind of dubious...
  const lastContract = contracts.sort((a, b) =>
    b.updatedAt >= a.updatedAt ? 1 : -1
  )[0];
  const lastContractIsCancelOne = lastContract
    ? lastContract.type === ContractType.UPDATE &&
      lastContract.packages.length === 0 &&
      lastContract.status === ContractStatus.ACCEPTED
    : false;

  const subscriptionEntries: [
    string,
    string,
    RawScopeDefinition | undefined
  ][] = [
    ["/settings/contracts/current", "My subscription", undefined],
    ["/settings/contracts/list", "Orders", undefined],
  ];

  if (!hasAcceptedOrPendingContracts || lastContractIsCancelOne) {
    subscriptionEntries.push([
      "/settings/contracts/order",
      "Order Subscription Package",
      scope("subscriptionPackageOrder").W as RawScopeDefinition,
    ]);
  }

  if (
    isPartnerAdmin &&
    hasAtLeastOneAcceptedAndNoPendingContract &&
    !lastContractIsCancelOne
  ) {
    subscriptionEntries.push([
      "/settings/contracts/modify",
      "Update Partner Data, Subscriptions",
      scope("subscriptionPackageOrder").W as RawScopeDefinition,
    ]);
  }

  return <MenuSection label="Subscription" entries={subscriptionEntries} />;
};

export default function Layout({ children }) {
  return (
    <div
      css={{
        height: "100%",
        position: "relative",
        background: "white",
        display: "flex",
      }}
    >
      <Suspense fallback={<Skeleton h="100%" />}>
        <NavbarSimple />
      </Suspense>
      <div
        css={{
          flexGrow: 1,
          marginLeft: 250,
          display: "flex",
          flexDirection: "column",
          padding: 32,
        }}
      >
        <Suspense fallback={<Skeleton h="100%" />}>{children}</Suspense>
      </div>
    </div>
  );
}

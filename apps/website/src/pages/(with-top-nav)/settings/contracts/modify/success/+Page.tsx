import { Button, Title } from "@mantine/core";
import { useTranslation } from "react-i18next";

export const Page = () => {
  const { t } = useTranslation();

  return (
    <>
      <Title m={"auto"} mt={"0"} mb={"0"}>
        {t("Partner data and subscription update request submitted!")}
      </Title>
      <Button
        component={"a"}
        href={"/settings/contracts/current"}
        m={"auto"}
        mt={"md"}
        w={"min-content"}
      >
        {t("Back to Subscriptions")}
      </Button>
    </>
  );
};

import { useProfile } from "#root/src/api/auth/auth.query";
import {
  useContractsFor<PERSON><PERSON>ner,
  useUpdateOrCancelContract,
} from "#root/src/api/contract/contract.query";
import type { CreateContractFormDTO } from "#root/src/api/contract/contract.service";
import { BlockTitle } from "#root/src/components/BlockTitle";
import { SubscriptionPackageForm } from "#root/src/components/subscription-packages/SubscriptionPackageForm";
import { assert } from "#root/src/shared/assert";
import { ContractStatus } from "#root/src/shared/constants";
import { Box } from "@mantine/core";
import { pick } from "lodash-es";
import { useTranslation } from "react-i18next";
import { navigate } from "vike/client/router";

export const Page = () => {
  const { t } = useTranslation();
  const updateOrCancelContract = useUpdateOrCancelContract();

  const { data: user } = useProfile();
  assert(user);
  assert(user.partnerId);

  const { data: contracts } = useContractsForPartner(user.partnerId);
  const lastAcceptedContract = contracts!
    .filter((c) => c.status === ContractStatus.ACCEPTED)
    .sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )[0];

  if (!lastAcceptedContract) {
    navigate("/settings/contracts/order");
    return null;
  }

  const handleSubmit = async (values: CreateContractFormDTO) => {
    await updateOrCancelContract.mutateAsync(values);
    navigate("/settings/contracts/modify/success");
  };

  return (
    <Box p="md" w="100%" maw="800" mx="auto">
      <BlockTitle
        label={t("Update Partner Data and Subscriptions")}
        mb="xl"
        css={{ fontSize: 30 }}
      />
      <SubscriptionPackageForm
        initialValues={{
          ...pick(
            lastAcceptedContract,
            "companyName",
            "companyTaxNumber",
            "companyRegistrationNumber",
            "companyStreetName",
            "companyStreetType",
            "companyHouseNumber",
            "companyFloorDoor",
            "companyCity",
            "companyZipCode",
            "companyCountry",
            "companyAdditionalRepName",
            "companyCommencementDate",
            "companyAuthorizedRepName",
            "companyInvoiceEmail",
            "billingIsSameAsCompanyAddress",
            "billingName",
            "billingStreetName",
            "billingStreetType",
            "billingHouseNumber",
            "billingFloorDoor",
            "billingCity",
            "billingZipCode",
            "billingCountry",
            "mainContactFirstName",
            "mainContactLastName",
            "mainContactPosition",
            "mainContactPhone",
            "mainContactEmail"
          ),
          packages: lastAcceptedContract.packages.map((pkg) => ({
            ...pick(pkg, "price", "packageId", "quantity"),
            historicalData: [...(pkg.historicalData || [])],
          })),
          affiliates: lastAcceptedContract.affiliates.map((affiliate) => {
            return pick(
              affiliate,
              "name",
              "address",
              "zipCode",
              "country",
              "customersHoldingPercentage",
              "city",
              "taxNumber",
              "companyRegistration"
            );
          }),
        }}
        onSubmit={handleSubmit}
        submitButtonText={t("Send Update Request")}
        showTermsText={false}
        allowZeroPackages={true}
      />
    </Box>
  );
};

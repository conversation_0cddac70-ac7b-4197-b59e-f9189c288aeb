import { useCreateContract } from "#root/src/api/contract/contract.query";
import type { CreateContractFormDTO } from "#root/src/api/contract/contract.service";
import { usePartnerMainContact } from "#root/src/api/partners/partner.query.ts";
import { BlockTitle } from "#root/src/components/BlockTitle";
import { SubscriptionPackageForm } from "#root/src/components/subscription-packages/SubscriptionPackageForm";
import { Box } from "@mantine/core";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { navigate } from "vike/client/router";

export const Page = () => {
  const { t } = useTranslation();
  const { data: mainPartnerContact } = usePartnerMainContact();
  const createContract = useCreateContract();

  const handleSubmit = async (values: CreateContractFormDTO) => {
    await createContract.mutateAsync(values);
    navigate("/settings/contracts/order/success");
  };

  const initialValues = useMemo(() => {
    return {
      billingIsSameAsCompanyAddress: true,
      affiliates: [],
      packages: [],
      secondaryContacts: [],
      companyName: mainPartnerContact.companyName ?? "",
      companyInvoiceEmail: mainPartnerContact.email ?? "",
      companyAuthorizedRepName:
        mainPartnerContact.firstName && mainPartnerContact.lastName
          ? mainPartnerContact.firstName + " " + mainPartnerContact.lastName
          : "",
      companyCountry: mainPartnerContact.country ?? "",
      mainContactEmail: mainPartnerContact.email,
      mainContactFirstName: mainPartnerContact.firstName,
      mainContactLastName: mainPartnerContact.lastName,
      mainContactPhone: mainPartnerContact.phoneNumber,
      mainContactPosition: mainPartnerContact.jobTitle,
    } satisfies Partial<CreateContractFormDTO>;
  }, [mainPartnerContact]);

  return (
    <Box p="md" w="100%" maw="800" mx="auto">
      <BlockTitle
        label={t("Subscription Package Order Form")}
        mb="xl"
        css={{ fontSize: 30 }}
      />
      <SubscriptionPackageForm
        initialValues={initialValues}
        onSubmit={handleSubmit}
        submitButtonText={t("Submit")}
        showTermsText={true}
      />
    </Box>
  );
};

import { getAllContracts } from "#root/src/api/contract/contract.telefunc";
import { ContractStatus, ContractType } from "#root/src/shared/constants";
import { redirect } from "vike/abort";

export const guard = async () => {
  const contracts = await getAllContracts();

  // no contracts, order page is active
  if (contracts.length === 0) {
    return;
  }

  // order should be reachable, if
  // 1) last order was ORDER type and is REJECTED
  // 2) last order was update type
  //      AND it was ACCEPTED
  //      AND it is a cancel (e.g. packages.length === 0)
  const lastContract = contracts.sort((a, b) =>
    b.updatedAt > a.updatedAt ? 1 : -1
  )[0];

  // number 1); rejected first order
  if (lastContract.type === ContractType.NEW_ORDER && lastContract.status === ContractStatus.REJECTED) {
    return;
  }

  // number 2); accepted cancellation
  const isLastAcceptedOrderCancel = lastContract
    ? lastContract.type === ContractType.UPDATE &&
    lastContract.packages.length === 0 &&
    lastContract.status === ContractStatus.ACCEPTED
    : false;
  if (isLastAcceptedOrderCancel) {
    return;
  }

  // show them the list
  throw redirect(`/settings/contracts/list`);
};

import { useAuth } from "#root/src/api/auth/auth.query";
import { useAllContracts } from "#root/src/api/contract/contract.query";
import { Box, Paper, Skeleton, Stack, Title } from "@mantine/core";
import { DataTable } from "mantine-datatable";
import { Suspense } from "react";
import { useTranslation } from "react-i18next";
import { navigate } from "vike/client/router";
import { UserType } from "#root/src/shared/constants";

const ContractsTable = () => {
  const { t } = useTranslation();
  const { data: contracts } = useAllContracts();
  const { profile } = useAuth();
  
  const isAdmin = profile?.type === UserType.ADMIN || profile?.type === UserType.SUPER_ADMIN;

  return (
    <Paper shadow="xs" p="2rem">
      <Stack>
        <DataTable
          columns={[
            {
              accessor: "id",
              title: "Contract ID",
              width: 220,
            },
            {
              accessor: "companyName",
              title: t("Company Name"),
            },
            {
              accessor: "status",
              title: t("Status"),
              render: (record) => {
                return t(record.status);
              },
            },
            {
              accessor: "type",
              title: t("Type"),
              render: (record) => {
                return t(record.type === "NEW_ORDER" ? "New Order" : "Update");
              },
            },
            {
              accessor: "createdAt",
              title: t("Created At"),
              render: (record) => {
                return new Date(record.createdAt).toLocaleDateString();
              },
            },
            ...(isAdmin ? [
              {
                accessor: "partner.name",
                title: t("Partner"),
                render: (record) => record.partner?.name || "N/A",
              }
            ] : []),
          ]}
          records={contracts}
          onRowClick={({record}) => navigate(`/settings/contracts/${record.id}`)}
          highlightOnHover
          withTableBorder
          borderRadius="sm"
          withColumnBorders
          emptyState={t("No contracts found")}
        />
      </Stack>
    </Paper>
  );
};

export const Page = () => {
  const { t } = useTranslation();

  return (
    <Box p="md">
      <Title order={2} mb="xl">
        {t("Contract Orders")}
      </Title>

      <Suspense fallback={<Skeleton height={400} />}>
        <ContractsTable />
      </Suspense>
    </Box>
  );
};
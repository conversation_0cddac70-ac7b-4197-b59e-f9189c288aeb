import { usePageContext } from "vike-react/usePageContext";
import { ContractView } from "#root/src/components/contracts/ContractView";
import { Box, Button, Group, Modal, Stack, Text, Title } from "@mantine/core";
import {
  useAcceptContract,
  useContract,
  useRejectContract,
} from "#root/src/api/contract/contract.query";
import { ContractStatus, UserType } from "#root/src/shared/constants";
import { useAuth } from "#root/src/api/auth/auth.query";
import { useDisclosure } from "@mantine/hooks";
import { FiCheck, FiX } from "react-icons/fi";
import { navigate } from "vike/client/router";

type AcceptContractModalProps = {
  opened: boolean;
  onClose: () => void;
  contractId: string;
  partnerId?: number;
};

const AcceptContractModal = ({
  opened,
  onClose,
  contractId,
  partnerId,
}: AcceptContractModalProps) => {
  const acceptMutation = useAcceptContract();

  const handleAccept = async () => {
    try {
      await acceptMutation.mutateAsync(contractId);
      onClose();
      if (partnerId) {
        navigate(`/settings/partners/${partnerId}`);
      } else {
        navigate("/settings/partners");
      }
    } catch (error) {
      console.error("Error accepting contract:", error);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Text fw={700} size="lg">
          Accept Contract
        </Text>
      }
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="sm">
          Are you sure you want to accept this contract? This will:
        </Text>
        <Box pl="md">
          <Text component="ul" size="sm">
            <Text component="li">Update the partner information</Text>
            <Text component="li">
              Replace all existing subscription packages with the ones in this
              contract
            </Text>
            <Text component="li">Mark the contract as accepted</Text>
          </Text>
        </Box>

        <Group justify="flex-end" mt="lg">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            color="green"
            onClick={handleAccept}
            loading={acceptMutation.isPending}
          >
            Accept
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

type RejectContractModalProps = {
  opened: boolean;
  onClose: () => void;
  contractId: string;
  partnerId?: number;
};

const RejectContractModal = ({
  opened,
  onClose,
  contractId,
  partnerId,
}: RejectContractModalProps) => {
  const rejectMutation = useRejectContract();

  const handleReject = async () => {
    try {
      await rejectMutation.mutateAsync(contractId);
      onClose();
      if (partnerId) {
        navigate(`/settings/partners/${partnerId}`);
      } else {
        navigate("/settings/partners");
      }
    } catch (error) {
      console.error("Error rejecting contract:", error);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Text fw={700} size="lg">
          Reject Contract
        </Text>
      }
      centered
      size="md"
    >
      <Stack gap="md">
        <Text size="sm">
          Are you sure you want to reject this contract? This action cannot be
          undone.
        </Text>

        <Group justify="flex-end" mt="lg">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            color="red"
            onClick={handleReject}
            loading={rejectMutation.isPending}
          >
            Reject
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export const Page = () => {
  const ctx = usePageContext();
  const contractId = ctx.routeParams["id"];
  const { data: contract } = useContract(contractId);
  const { profile } = useAuth();

  const [
    acceptModalOpened,
    { open: openAcceptModal, close: closeAcceptModal },
  ] = useDisclosure(false);
  const [
    rejectModalOpened,
    { open: openRejectModal, close: closeRejectModal },
  ] = useDisclosure(false);

  const isAdmin =
    profile?.type === UserType.ADMIN || profile?.type === UserType.SUPER_ADMIN;
  const isPending = contract?.status === ContractStatus.PENDING;
  const showActionButtons = isAdmin && isPending;

  return (
    <Box p="md">
      <Group mb="xl" justify="space-between">
        <Title order={2}>Contract Details</Title>

        {showActionButtons && (
          <Group>
            <Button
              color="red"
              leftSection={<FiX size={16} />}
              onClick={openRejectModal}
            >
              Reject Contract
            </Button>
            <Button
              color="green"
              leftSection={<FiCheck size={16} />}
              onClick={openAcceptModal}
            >
              Accept Contract
            </Button>
          </Group>
        )}
      </Group>

      <ContractView contractId={contractId} />

      <AcceptContractModal
        opened={acceptModalOpened}
        onClose={closeAcceptModal}
        contractId={contractId}
        partnerId={contract?.partnerId}
      />

      <RejectContractModal
        opened={rejectModalOpened}
        onClose={closeRejectModal}
        contractId={contractId}
        partnerId={contract?.partnerId}
      />
    </Box>
  );
};

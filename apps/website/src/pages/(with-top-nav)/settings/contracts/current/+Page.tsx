import { useProfile } from "#root/src/api/auth/auth.query";
import { useContractsForPartner } from "#root/src/api/contract/contract.query";
import { useSubscriptionPackageAssignements } from "#root/src/api/subscription-packages/subscription-packages.query";
import { assert } from "#root/src/shared/assert";
import { ContractStatus } from "#root/src/shared/constants";
import { Alert, Box, Paper, Stack, Title, Text, Tooltip } from "@mantine/core";
import { DataTable } from "mantine-datatable";
import { useTranslation } from "react-i18next";

const SubscriptionTable = () => {
  const { data: subscriptionPackages } = useSubscriptionPackageAssignements();
  const nextQuarterlyBillingDate = () => {
    const currentDate = new Date();
    if (currentDate.getMonth() < 3) {
      return new Date(currentDate.getFullYear(), 2, 1).toLocaleDateString("en");
    } else if (currentDate.getMonth() < 6) {
      return new Date(currentDate.getFullYear(), 5, 1).toLocaleDateString("en");
    } else if (currentDate.getMonth() < 9) {
      return new Date(currentDate.getFullYear(), 8, 1).toLocaleDateString("en");
    } else {
      return new Date(currentDate.getFullYear(), 11, 1).toLocaleDateString(
        "en"
      );
    }
  };

  return (
    <Paper shadow={"xs"} p={"2rem"}>
      <Stack>
        <Title order={3}>Subscription Packages</Title>
        <Text fs="italic">Next billing date: {nextQuarterlyBillingDate()}</Text>
        <DataTable
          columns={[
            {
              accessor: "subscriptionPackage.name",
              title: "Name",
            },
            {
              accessor: "_price",
              title: "Price",
              render: (record) => {
                return `${record.subscriptionPackage.priceEur} €`;
              },
            },
            {
              accessor: "_subscriptions",
              title: "Subscriptions Included",
              render: (record) => {
                return record.subscriptionPackage.subscriptions
                  .map((s) => s.subscription)
                  .map((s) => `${s.dataSetName} - ${s.subscriptionName}`)
                  .join(", ");
              },
            },
            {
              accessor: "_status",
              title: "Status",
              render: (record) => {
                return record.deleteNextCycle ? (
                  // eslint-disable-next-line sonarjs/no-nested-conditional
                  record.deletedAt ? (
                    "Inactive"
                  ) : (
                    <Tooltip
                      label={`The subscription is active until ${nextQuarterlyBillingDate()}`}
                    >
                      <Text size="sm">{"Cancelled"}</Text>
                    </Tooltip>
                  )
                ) : (
                  <Text size="sm">{"Active"}</Text>
                );
              },
            },
          ]}
          records={subscriptionPackages}
        />
        <Text size="sm">
          {
            "* The cancelled subscription are going to be inactivated after the next billing date."
          }
        </Text>
      </Stack>
    </Paper>
  );
};

export const Page = () => {
  const { t } = useTranslation();
  const { data: user } = useProfile();
  assert(user);
  assert(user.partnerId);
  const { data: contracts } = useContractsForPartner(user.partnerId);

  return (
    <Box p="md">
      <Title order={2} mb="xl">
        {t("My Subscription")}
      </Title>

      {contracts?.some((c) => c.status === ContractStatus.PENDING) && (
        <Alert title={t("Subscription Order in Progress")} color="blue" mb="lg">
          {t(
            "Your subscription package order is being processed. Please check back later to see your active subscriptions."
          )}
        </Alert>
      )}

      <SubscriptionTable />
    </Box>
  );
};

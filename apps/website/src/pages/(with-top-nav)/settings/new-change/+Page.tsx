import { <PERSON><PERSON><PERSON><PERSON>, useForm } from "react-hook-form";
import { useCreateAnnouncement } from "#root/src/api/announcements/query";
import type { createAnnouncement } from "#root/src/api/announcements/announcements.telefunc";
import { <PERSON>ton, Group, Stack, Title } from "@mantine/core";
import { TextInput } from "#root/src/components/formfields/TextInput";
import { useMemo } from "react";
import { FiSearch } from "react-icons/fi";
import { useOpenApi } from "#root/src/openapi/openapi.query";
import { AnnouncementTypeLabel } from "#root/src/shared/constants";
import { RtfEditor } from "#root/src/components/formfields/RtfEditor";
import { MultiSelect } from "#root/src/components/formfields/MultiSelect";
import { Select } from "#root/src/components/formfields/Select";
import { navigate } from "vike/client/router";

type Form = Parameters<typeof createAnnouncement>[0];

export const Page = () => {
  const { data } = useOpenApi();
  const dataSetsFormatted = useMemo(
    () =>
      data?.groupedOperations?.map((x) => ({
        group: x.group!,
        items: x.operations.map((op) => ({
          value: op.operationId!,
          label: op.dataViewUrlName ?? op.url ?? op.operationId!,
        })),
      })) || [],
    [data]
  );

  const methods = useCreateAnnouncement();
  const form = useForm<Form>({
    defaultValues: {
      datasets: [],
      message: "<b>Hello!</b>",
    },
  });

  const onSubmit = form.handleSubmit(async (values) => {
    await methods.mutateAsync(values);
    navigate("/changelog");
  });

  return (
    <Stack>
      <Title order={4}>New Change</Title>
      <FormProvider {...form}>
        <form onSubmit={onSubmit}>
          <Stack mah={"screen"}>
            <Group grow={false} gap="md">
              <Select
                data={AnnouncementTypeLabel}
                label="Type"
                required
                name="type"
              />
              <TextInput
                {...form.register("title", {
                  minLength: {
                    value: 10,
                    message: "Title must have at least 10 characters.",
                  },
                  maxLength: {
                    value: 80,
                    message: "Title must have at most 80 characters.",
                  },
                })}
                label="Title"
                type={"text"}
                required
                style={{
                  flex: 1,
                }}
              />
            </Group>
            <MultiSelect
              label="Datasets"
              data={dataSetsFormatted}
              placeholder="Search dataset…"
              leftSection={<FiSearch size={16} />}
              comboboxProps={{ withinPortal: false }}
              clearable
              searchable
              name={"datasets"}
            />
            {/*<Select
                w={"full"}
                data={apiEndpointsFormatted}
                label="API Endpoint"
                name={"apiEndpoint"}
                clearable
            />*/}
            <RtfEditor name={"message"} label={"Message"} required />
            <Button ml="auto" type="submit" loading={methods.isPending}>
              Create
            </Button>
          </Stack>
        </form>
      </FormProvider>
    </Stack>
  );
};

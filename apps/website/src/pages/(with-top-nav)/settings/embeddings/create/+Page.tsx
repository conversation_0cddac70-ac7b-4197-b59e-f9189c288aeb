import {
  use<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Controller,
  useFormContext,
} from "react-hook-form";
import {
  Checkbox,
  TextInput,
  Button,
  Select,
  Textarea,
  Box,
  Group,
  Divider,
} from "@mantine/core";
import { useCreateEmbedding } from "#root/src/api/embeddings/embeddings.query";
import { useOpenApi } from "#root/src/openapi/openapi.query";

export const Page = () => {
  const openapi = useOpenApi();
  const datasets = openapi.data.operations.map(
    (operation) => operation.operationId
  );
  const methods = useForm<FormValues>({
    defaultValues: {
      allowedOrigins: ["*.anything.anything"],
      dataSet: datasets[0],
      dateRange: "1 month",
      includeTable: false,
      includeChart: false,
      apiParams: "",
    },
  });

  const createEmbedding = useCreateEmbedding();

  const onSubmit = async (data: FormValues) => {
    await createEmbedding.mutateAsync({
      ...data,
      userId: 1,
      name: "new embedding",
      dataSetOperationId: data.dataSet,
    });
  };

  return (
    <FormProvider {...methods}>
      <Box style={{ padding: "md", maxWidth: "100%", overflow: "hidden" }}>
        <h2>Embeddings</h2>

        <form onSubmit={methods.handleSubmit(onSubmit)}>
          <Group align="flex-middle" grow>
            <WhitelistSection />
            <DataSetSection />
          </Group>

          <Divider my="lg" />

          <Button type="submit" fullWidth>
            Generate link
          </Button>

          <TextInput
            label="Generated link"
            value="https://changeme.com"
            readOnly
            mt="sm"
            style={{ width: "100%" }}
          />
        </form>
      </Box>
    </FormProvider>
  );
};

type FormValues = {
  allowedOrigins: string[];
  dataSet: string;
  dateRange: string;
  includeTable: boolean;
  includeChart: boolean;
  apiParams: string;
};

const WhitelistSection = () => {
  const { control, setValue, getValues } = useFormContext<FormValues>();

  const addNewWhitelist = () => {
    const currentList = getValues("allowedOrigins");
    setValue("allowedOrigins", [...currentList, "*.anything.anything"]);
  };

  return (
    <Box mb="lg" style={{ maxWidth: "100%" }}>
      <h3>1. Allowed origins</h3>
      <Controller
        name="allowedOrigins"
        control={control}
        render={({ field }) => (
          <Box>
            {field.value.map((website, index) => (
              <Group
                key={index}
                align="center"
                mb="xs"
                style={{ width: "100%" }}
              >
                <Checkbox defaultChecked />
                <TextInput
                  value={website}
                  onChange={(e) => {
                    const newList = [...field.value];
                    newList[index] = e.currentTarget.value;
                    field.onChange(newList);
                  }}
                  style={{ flexGrow: 1 }}
                />
              </Group>
            ))}
            <Button
              onClick={addNewWhitelist}
              variant="outline"
              mt="sm"
              fullWidth
            >
              Add New
            </Button>
          </Box>
        )}
      />
      <Divider orientation="vertical" mx="lg" />
    </Box>
  );
};

const DataSetSection = () => {
  const { control } = useFormContext<FormValues>();
  const openapi = useOpenApi();
  const datasets = openapi.data.operations
    .filter((o) => o.operationId !== undefined)
    .map((operation) => {
      return {
        value: operation.operationId!,
        label: operation.operationId!,
      };
    });

  return (
    <Box mb="lg">
      <h3>2. Dataset</h3>

      <Controller
        name="dataSet"
        control={control}
        render={({ field }) => (
          <Select
            label="Select dataset"
            data={datasets}
            {...field}
            style={{ width: "100%" }}
          />
        )}
      />

      <Controller
        name="dateRange"
        control={control}
        render={({ field }) => (
          <Select
            label="Date range"
            data={["1 month", "3 months", "6 months"]}
            {...field}
            style={{ marginTop: 10, width: "100%" }}
          />
        )}
      />

      <Box mt="sm">
        <h4>Format</h4>
        <Group style={{ spacing: "sm" }}>
          <Controller
            name="includeTable"
            control={control}
            render={({ field }) => (
              <Checkbox
                label="Table"
                checked={field.value}
                onChange={(e) => field.onChange(e.currentTarget.checked)}
              />
            )}
          />
          <Controller
            name="includeChart"
            control={control}
            render={({ field }) => (
              <Checkbox
                label="Chart"
                checked={field.value}
                onChange={(e) => field.onChange(e.currentTarget.checked)}
              />
            )}
          />
        </Group>
      </Box>

      <Controller
        name="apiParams"
        control={control}
        render={({ field }) => (
          <Textarea
            label="API request"
            placeholder="Your API query parameters come here"
            {...field}
            mt="md"
            style={{ width: "100%" }}
          />
        )}
      />
    </Box>
  );
};

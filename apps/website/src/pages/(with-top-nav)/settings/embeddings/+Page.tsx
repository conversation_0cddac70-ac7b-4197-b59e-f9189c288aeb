import {
  useD<PERSON><PERSON>Embedding,
  useEmbeddings,
} from "#root/src/api/embeddings/embeddings.query";
import { Confirmed } from "#root/src/components/Confirmed";
import { DateTime } from "#root/src/components/DateTime";
import { UpdateEmbeddingModal } from "#root/src/components/EmbeddingModals";
import { iframeForEmbedding } from "#root/src/utils/embedding";
import {
  ActionIcon,
  Badge,
  Button,
  Code,
  CopyButton,
  Group,
  Stack,
  Text,
  Title,
  Tooltip,
} from "@mantine/core";
import { modals } from "@mantine/modals";
import { DataTable } from "mantine-datatable";
import {
  FiCheck,
  FiCopy,
  FiDelete,
  FiEdit,
  FiExternalLink,
} from "react-icons/fi";

export const Page = () => {
  const { data: embeddings = [] } = useEmbeddings();

  const onView = (embedding: (typeof embeddings)[0]) => {
    modals.open({
      centered: true,
      title: "Embedding Details",
      size: "lg",
      children: (
        <EmbeddingDetailsModal
          embedding={embedding}
          onEdit={() => {
            modals.closeAll();
            onEdit(embedding.id);
          }}
        />
      ),
    });
  };

  const onEdit = (embeddingId: string) => {
    modals.open({
      centered: true,
      title: "Edit Embedding",
      size: "xl",
      children: (
        <UpdateEmbeddingModal
          onClose={() => modals.closeAll()}
          embeddingId={embeddingId}
        />
      ),
    });
  };

  return (
    <>
      <div css={{ display: "flex", padding: 14 }}>
        <Title order={4}>Embeddings</Title>
      </div>
      <DataTable
        // withBorder
        withTableBorder
        borderRadius="sm"
        withColumnBorders
        striped
        highlightOnHover
        // loading={isLoading}
        columns={[
          {
            accessor: "name",
            title: "Name",
            width: 160,
          },
          {
            accessor: "dataSetOperationId",
            title: "Dataset",
            width: 180,
            render: (record) => (
              <Tooltip label={record.dataSetOperationId}>
                <Text truncate size="sm">
                  {record.dataSetOperationId}
                </Text>
              </Tooltip>
            ),
          },
          {
            accessor: "origins",
            title: "Origins",
            width: 180,
            render: (record) => (
              <Group gap="xs">
                <Tooltip
                  label={record.EmbeddingsAllowedOrigin.map(
                    (ao) => ao.origin
                  ).join(", ")}
                >
                  <Text truncate size="sm">
                    {record.EmbeddingsAllowedOrigin.map((ao) => ao.origin).join(
                      ", "
                    )}
                  </Text>
                </Tooltip>
              </Group>
            ),
          },
          {
            accessor: "createdAt",
            title: "Created",
            width: 120,
            render: (record) => (
              <DateTime
                date={record.createdAt}
                format={(d) => d.toLocaleDateString()}
              />
            ),
          },
          {
            accessor: "includeTable",
            title: "Format",
            width: 120,
            render: (record) => (
              <Group gap="xs">
                {record.includeTable && <Badge color="blue">Table</Badge>}
                {record.includeChart && <Badge color="green">Chart</Badge>}
              </Group>
            ),
          },
          {
            accessor: "actions",
            title: "Actions",
            width: 120,
            render: (record) => (
              <EmbeddingActions
                embedding={record}
                onView={() => onView(record)}
                onEdit={() => onEdit(record.id)}
              />
            ),
          },
        ]}
        records={embeddings}
        emptyState={
          <Stack align="center" gap="xs" py="xl">
            <Text size="sm" c="dimmed">
              No embeddings found
            </Text>
            <Text
              size="sm"
              c="dimmed"
            >{`Create new embeddings through the "Data View" page`}</Text>
          </Stack>
        }
      />
    </>
  );
};

const EmbeddingActions = ({ embedding, onView, onEdit }) => {
  const deleteEmbedding = useDeleteEmbedding();

  const handleDelete = async () => {
    await deleteEmbedding.mutateAsync(embedding.id);
  };

  return (
    <Group gap="xs">
      <Tooltip label="View details">
        <ActionIcon variant="light" onClick={onView}>
          <FiExternalLink size={16} />
        </ActionIcon>
      </Tooltip>
      <Tooltip label="Edit">
        <ActionIcon variant="light" onClick={onEdit}>
          <FiEdit size={16} />
        </ActionIcon>
      </Tooltip>
      <Tooltip label="Delete">
        <Confirmed
          onConfirm={handleDelete}
          title="Delete Embedding"
          content="Are you sure you want to delete this embedding? This action cannot be undone."
          confirmColor="red"
        >
          <ActionIcon color="red" variant="light">
            <FiDelete size={16} />
          </ActionIcon>
        </Confirmed>
      </Tooltip>
    </Group>
  );
};

const EmbeddingDetailsModal = ({ embedding, onEdit }) => {
  if (!embedding) return null;

  const embedCode = iframeForEmbedding(embedding.id);

  return (
    <>
      <Stack gap={"md"}>
        <Group>
          <Title order={5}>{embedding.name}</Title>
          <Badge>{embedding.dataSet}</Badge>
        </Group>

        <Group>
          <Text size="sm" c="dimmed">
            Created on:
          </Text>
          <DateTime
            date={embedding.createdAt}
            format={(d) => d.toLocaleString()}
          />
        </Group>

        <Stack gap={"xs"}>
          <Text fw={500}>Allowed Origins</Text>
          {embedding.EmbeddingsAllowedOrigin?.map((origin) => (
            <Text key={origin.id} size="sm">
              {origin.origin}{" "}
              {!origin.enabled && <Badge color="red">Disabled</Badge>}
            </Text>
          )) || (
            <Text size="sm" c="dimmed">
              No origins defined
            </Text>
          )}
        </Stack>

        <Stack gap="xs">
          <Group>
            <Text fw={500}>Embed Code</Text>
            <CopyButton value={embedCode} timeout={2000}>
              {({ copied, copy }) => (
                <Tooltip
                  label={copied ? "Copied" : "Copy to clipboard"}
                  withArrow
                >
                  <ActionIcon onClick={copy} color={copied ? "teal" : "gray"}>
                    {copied ? <FiCheck size={16} /> : <FiCopy size={16} />}
                  </ActionIcon>
                </Tooltip>
              )}
            </CopyButton>
          </Group>
          <Code block>{embedCode}</Code>
        </Stack>

        <Group mt="md">
          <Button variant="light" onClick={onEdit}>
            Edit
          </Button>
          <Button
            color="blue"
            onClick={() => window.open(`/embed/${embedding.id}`, "_blank")}
          >
            Preview
          </Button>
        </Group>
      </Stack>
    </>
  );
};

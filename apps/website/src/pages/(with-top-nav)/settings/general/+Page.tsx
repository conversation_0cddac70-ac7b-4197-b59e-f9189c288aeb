import { useProfile } from "#root/src/api/auth/auth.query";
import { usePartnerMainContact } from "#root/src/api/partners/partner.query.ts";
import { useUpdateUserProperties, useUsers } from "#root/src/api/users/query";
import { InviteUserButton } from "#root/src/components/InviteUserModal";
import { Checkbox } from "#root/src/components/formfields/Checkbox.tsx";
import { TextInput } from "#root/src/components/formfields/TextInput";
import { assert } from "#root/src/shared/assert";
import { UserType } from "#root/src/shared/constants";
import {
  Alert,
  Button,
  Flex,
  Group,
  Modal,
  Paper,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { DataTable } from "mantine-datatable";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import CommitHashText from "#root/src/components/CommitHashText.tsx";

const SaveConfirmationModal = ({
  opened,
  onClose,
  onConfirm,
  formData,
  originalData,
}: {
  opened: boolean;
  onClose: () => void;
  onConfirm: () => void;
  formData: any;
  originalData: any;
}) => {
  const emailChanged = formData.email !== originalData.email;

  return (
    <Modal opened={opened} onClose={onClose} title="Confirm Changes">
      <Stack>
        <Text>Are you sure you want to save these changes?</Text>

        {emailChanged && (
          <Alert color="red" title="Email Address Changed">
            <strong>Important:</strong> You have changed your email address.
            Next time you log in, you will need to use{" "}
            <strong>{formData.email}</strong> as your login email.
            <br />
            <br />
            <strong>Warning:</strong> the email change causes the system to log
            you out!
          </Alert>
        )}

        <Group justify="flex-end" mt="md">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={onConfirm}>Confirm Changes</Button>
        </Group>
      </Stack>
    </Modal>
  );
};

const BasicPartnerInfo = () => {
  const { data: contact } = usePartnerMainContact();
  const dummyForm = useForm({
    defaultValues: {
      firstName: contact?.firstName || "",
      lastName: contact?.lastName || "",
      email: contact?.email || "",
      enableNotifications: contact?.enableNotifications ?? true,
      phoneNumber: contact?.phoneNumber || "",
      jobTitle: contact?.jobTitle || "",
      country: contact?.country || "",
      companyName: contact?.companyName || "",
    },
  });

  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16}>
          <Title order={2}>{contact?.companyName}</Title>
        </Flex>
        <FormProvider {...dummyForm}>
          <form>
            <Stack gap="md">
              <Group grow>
                <TextInput
                  {...dummyForm.register("firstName")}
                  label="First Name"
                  required
                  disabled={true}
                />
                <TextInput
                  {...dummyForm.register("lastName")}
                  label="Last Name"
                  required
                  disabled={true}
                />
              </Group>
              <TextInput
                {...dummyForm.register("email")}
                label="Email"
                type="email"
                required
                disabled={true}
              />
              <Group grow>
                <TextInput
                  {...dummyForm.register("phoneNumber")}
                  label="Phone Number"
                  disabled={true}
                />
                <TextInput
                  {...dummyForm.register("jobTitle")}
                  label="Job Title"
                  disabled={true}
                />
              </Group>
              <Group grow>
                <TextInput
                  {...dummyForm.register("country")}
                  label="Country"
                  disabled={true}
                />
              </Group>
            </Stack>
          </form>
        </FormProvider>
      </Stack>
    </Paper>
  );
};

const UserData = () => {
  const { data: profileData } = useProfile();
  assert(profileData);
  const form = useForm<any>({
    defaultValues: {
      firstName: profileData.firstName,
      lastName: profileData.lastName,
      email: profileData.email,
      enableNotifications: profileData.enableNotifications,
      phoneNumber: profileData.phoneNumber,
      jobTitle: profileData.jobTitle,
    },
  });

  const [edited, setEdited] = useState(false);
  const [
    confirmModalOpened,
    { open: openConfirmModal, close: closeConfirmModal },
  ] = useDisclosure(false);
  const updateUserPropertiesMutation = useUpdateUserProperties();

  const onChange = () => {
    if (!edited) setEdited(true);
    if (
      edited &&
      Object.entries(form.getValues()).every(([k, v]) => profileData?.[k] === v)
    )
      setEdited(false);
  };

  const handleSubmit = () => {
    openConfirmModal();
  };

  const onSave = async (values: any) => {
    // Find changed fields
    const changedFields = Object.entries(values).filter(
      ([key, value]) => profileData[key as keyof typeof profileData] !== value
    );

    // Create updates object for batch update
    const updates = Object.fromEntries(changedFields);

    // Update all changed fields in a single request
    await updateUserPropertiesMutation.mutateAsync({ updates });

    closeConfirmModal();
    setEdited(false);
  };

  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16}>
          <Title order={2}>Contact information</Title>
        </Flex>
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)}>
            <Stack gap="md">
              <Group grow>
                <TextInput
                  {...form.register("firstName", { onChange })}
                  label="First Name"
                  required
                />
                <TextInput
                  {...form.register("lastName", { onChange })}
                  label="Last Name"
                  required
                />
              </Group>
              <TextInput
                {...form.register("email", { onChange })}
                label="Email"
                type="email"
                required
              />
              <Group grow>
                <TextInput
                  {...form.register("phoneNumber", { onChange })}
                  label="Phone Number"
                />
                <TextInput
                  {...form.register("jobTitle", { onChange })}
                  label="Position"
                />
              </Group>
              <Checkbox
                {...form.register("enableNotifications", { onChange })}
                label="Enable Notifications"
              />
              <Group justify="flex-end" mt="md">
                <Button
                  type="submit"
                  variant="filled"
                  disabled={!edited}
                  size="sm"
                >
                  Save Changes
                </Button>
              </Group>
            </Stack>
          </form>
        </FormProvider>

        <SaveConfirmationModal
          opened={confirmModalOpened}
          onClose={closeConfirmModal}
          onConfirm={form.handleSubmit(onSave)}
          formData={form.getValues()}
          originalData={profileData}
        />
      </Stack>
    </Paper>
  );
};

const UsersSection = () => {
  const { data: user } = useProfile();
  assert(user);
  const { data: users } = useUsers(user.partnerId || undefined);

  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16} justify="space-between" align="center">
          <Title order={2}>Users</Title>
          {user.type === UserType.PARTNER_ADMIN && <InviteUserButton />}
        </Flex>
        <DataTable
          minHeight={150}
          highlightOnHover
          columns={[
            {
              accessor: "firstName",
              title: "First Name",
            },
            {
              accessor: "lastName",
              title: "Last Name",
            },
            {
              accessor: "email",
              title: "Email",
            },
            {
              accessor: "jobTitle",
              title: "Job Title",
              render: (record) => record.jobTitle || "-",
            },
            {
              accessor: "type",
              title: "User Type",
            },
            {
              accessor: "status",
              title: "Status",
            },
          ]}
          records={users || []}
          noRecordsText="No users found"
          emptyState={<Text c="dimmed">No users have been added yet</Text>}
        />
      </Stack>
    </Paper>
  );
};

export const Page = () => {
  const { data: profile } = useProfile();

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        height: "100%",
      }}
    >
      <Stack>
        <UserData />
        {profile?.partner && <BasicPartnerInfo />}
        <UsersSection />
      </Stack>
      <CommitHashText />
    </div>
  );
};

import { usePartners } from "#root/src/api/partners/partner.query.ts";
import { getPartnersAsTabularData } from "#root/src/api/partners/partners.telefunc.ts";
import { useSubscriptionPackages } from "#root/src/api/subscription-packages/subscription-packages.query";
import type { FormatType } from "#root/src/api/tabular-formats/encode.ts";
import type { QueryData0 } from "#root/src/api/types";
import { DownloadSheetComboBox } from "#root/src/components/DownloadSheetComboBox";
import PartnerFilter from "#root/src/components/PartnerFilter";
import { useAppStore } from "#root/src/pages/store.ts";
import { PartnerStatus } from "#root/src/shared/constants.ts";
import { downloadFile } from "#root/src/utils/file-utils";
import { Button, Group, Menu, Modal, Title } from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { DataTable } from "mantine-datatable";
import { useCallback, useMemo, useState } from "react";
import { withFallback } from "vike-react-query";
import { navigate } from "vike/client/router";
import { useTranslation } from "react-i18next";

type T = QueryData0<typeof usePartners>;

const PartnerPageHeader = ({
  selectedPartners,
}: {
  selectedPartners: any[];
}) => {
  const onDownload = useCallback(async (format: FormatType) => {
    const data = await getPartnersAsTabularData(format);
    downloadFile(
      `partners.${format}`,
      data,
      format === "xlsx"
        ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        : "text/csv"
    );
  }, []);

  return (
    <div css={{ display: "flex", paddingBottom: 16 }}>
      <Title order={4}>Partners</Title>
      <Group ml="auto">
        <Menu keepMounted>
          <Menu.Dropdown>
            <Menu.Item>
              <UpdatePartnerSubscriptions partners={selectedPartners} />
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
        <DownloadSheetComboBox onClick={onDownload} />
        <PartnerFilter />
      </Group>
    </div>
  );
};

export const Page = () => {
  const { data: partners } = usePartners();
  const { t } = useTranslation();

  const [selectedRows, setSelectedRows] = useState<{ id: number }[]>([]);
  const selectedPartners = partners.filter((p) =>
    selectedRows.some((r) => r.id === p.id)
  );

  const nameFilter = useAppStore((state) => state.partnerFilter.name);
  const statusFilter = useAppStore((state) => state.partnerFilter.status);

  const filteredRecords: T[] = useMemo(() => {
    if (!partners) return [];
    const normalizedFilter = nameFilter.trim().toLowerCase();
    return partners.filter((partner) => {
      // this is done because partnerFilter is an enum[], partner.status is string and afaik types are
      // not enforced on the ORM/database side
      const isPartnerStatus = (val: string): val is PartnerStatus =>
        Object.keys(PartnerStatus).includes(val);
      const validStatus = isPartnerStatus(partner.status)
        ? partner.status
        : undefined;

      const nameMatches = partner.name.toLowerCase().includes(normalizedFilter);
      const statusMatches =
        statusFilter.length === 0 ||
        (validStatus && statusFilter.includes(validStatus));

      return nameMatches && statusMatches;
    });
  }, [partners, nameFilter, statusFilter]);

  return (
    <>
      <PartnerPageHeader selectedPartners={selectedPartners} />
      <DataTable
        selectedRecords={selectedPartners}
        onSelectedRecordsChange={setSelectedRows}
        columns={[
          {
            accessor: "name",
            title: "Name",
          },
          {
            accessor: "status",
            title: "Status",
            render: (r) => t(`PartnerStatus.${r.status}`),
          },
          {
            accessor: "subscriptionAssignments",
            title: "Subscriptions",
            render(record, index) {
              return record.subscriptionPackageAssignment.length;
            },
          },
        ]}
        records={filteredRecords}
        onRowClick={({ record }) => {
          navigate(`/settings/partners/${record.id}`);
        }}
      />
    </>
  );
};

const UpdatePartnerSubscriptionsLazy = withFallback(
  ({ close }: { partners: T[]; close: () => void }) => {
    const subscriptionPackages = useSubscriptionPackages();

    const defaultSelection = [];

    const [selectedRecords, setSelectedRecords] =
      useState<{ id: string }[]>(defaultSelection);

    return (
      <>
        <DataTable
          selectedRecords={selectedRecords}
          onSelectedRecordsChange={setSelectedRecords}
          columns={[
            {
              accessor: "name",
              title: "Name",
            },
          ]}
          records={subscriptionPackages.data}
        />

        <Group justify="end" mt="lg">
          <Button onClick={close} variant="light">
            Cancel
          </Button>
          <Button
            onClick={async () => {
              console.log("selected packages", selectedRecords);
              close();
            }}
          >
            Add
          </Button>
        </Group>
      </>
    );
  }
);

const UpdatePartnerSubscriptions = ({ partners }: { partners: T[] }) => {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <Modal
        opened={opened}
        onClose={close}
        title="Add datasources"
        withinPortal
      >
        <UpdatePartnerSubscriptionsLazy partners={partners} close={close} />
      </Modal>
      <div
        onClick={(e) => {
          open();
        }}
      ></div>
    </>
  );
};

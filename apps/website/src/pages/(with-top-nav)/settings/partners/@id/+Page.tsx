import {
  useDisable<PERSON><PERSON><PERSON>,
  use<PERSON><PERSON><PERSON>,
  usePartnerMainContact,
} from "#root/src/api/partners/partner.query";
import { useContractsForPartner } from "#root/src/api/contract/contract.query";
import {
  useAssignSubscriptionPackageToPartner,
  useSubscriptionPackages,
  useUnassignSubscriptionPackage,
} from "#root/src/api/subscription-packages/subscription-packages.query";
import { useUsers } from "#root/src/api/users/query.tsx";
import { Checkbox } from "#root/src/components/formfields/Checkbox.tsx";
import { DatePicker } from "#root/src/components/formfields/DatePicker";
import { Select } from "#root/src/components/formfields/Select";
import { TextInput } from "#root/src/components/formfields/TextInput";
import {
  ActionIcon,
  Button,
  Flex,
  Group,
  Modal,
  Paper,
  Stack,
  Text,
  Title,
  type ButtonProps,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import dayjs from "dayjs";
import { DataTable } from "mantine-datatable";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { FiEdit, FiTrash } from "react-icons/fi";
import { usePageContext } from "vike-react/usePageContext";
import { navigate } from "vike/client/router";

type AssingSubscriptionPackageToPartnerModalProps = {
  partnerId: number;
} & ButtonProps;

type FormValues = {
  partnerId: number;
  subscriptionPackageId: string;
  validUntil: Date;
};

const AssingSubscriptionPackageToPartner = (
  props: AssingSubscriptionPackageToPartnerModalProps
) => {
  const { partnerId, ...buttonProps } = props;
  const form = useForm<FormValues>({
    defaultValues: {
      partnerId: partnerId,
      subscriptionPackageId: "",
      validUntil: dayjs().add(1, "year").toDate(),
    },
  });
  const { data: subscriptionPackages } = useSubscriptionPackages();
  const methods = useAssignSubscriptionPackageToPartner();
  const [opened, { open, close }] = useDisclosure();

  const onSubmit = async (data: FormValues) => {
    await methods.mutateAsync(data);
    close();
  };

  return (
    <>
      <Button onClick={open} {...buttonProps}>
        Add Subscription Package
      </Button>
      <Modal onClose={close} opened={opened}>
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <Stack gap={"md"}>
              <h1>Assign Subscription Package</h1>
              <TextInput name={"partnerId"} readOnly label={"Partner ID"} />
              <Select
                name="subscriptionPackageId"
                label="Subscription Package"
                data={subscriptionPackages?.map((sp) => {
                  return {
                    value: sp.id,
                    label: sp.name,
                  };
                })}
              />
              <DatePicker name="validUntil" label="Valid Until" />
              <Button type="submit">Ok</Button>
            </Stack>
          </form>
        </FormProvider>
      </Modal>
    </>
  );
};
type RemoveSubscriptionPackageAssignementModalProps = {
  assignementId: string;
  partnerId: number;
  subPackageName: string;
};

const RemoveSubscriptionPackageAssignement = (
  props: RemoveSubscriptionPackageAssignementModalProps
) => {
  const [opened, { open, close }] = useDisclosure();
  const { assignementId, partnerId, subPackageName } = props;
  const methods = useUnassignSubscriptionPackage();

  const onConfirm = async () => {
    await methods.mutateAsync(assignementId);
    close();
  };

  return (
    <>
      <ActionIcon onClick={open} variant="light" c="red">
        <FiTrash />
      </ActionIcon>
      <Modal opened={opened} onClose={close}>
        <Stack>
          <Text>{`Are you sure you want to remove '${subPackageName}' from partner ${partnerId}`}</Text>
          <Group dir="row">
            <Button onClick={onConfirm}>Confirm</Button>
            <Button onClick={close}>Cancel</Button>
          </Group>
        </Stack>
      </Modal>
    </>
  );
};

export const PartnerSubscriptionPackagesTable = ({
  partnerId,
}: {
  partnerId: number;
}) => {
  const { data: partner } = usePartner(partnerId);

  return (
    <>
      <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
        <Stack>
          <Flex pb={16}>
            <Title order={2}>Subscription packages</Title>
            <AssingSubscriptionPackageToPartner
              partnerId={partnerId}
              ml={"auto"}
            />
          </Flex>
          <DataTable
            // withTableBorder
            withColumnBorders
            withRowBorders
            columns={[
              {
                accessor: "id",
                title: "Id",
              },
              {
                accessor: "name",
                title: "Name",
              },
              {
                accessor: "subscriptionPackageId",
                title: "Subscription Package Id",
              },
              {
                accessor: "validUntil",
                title: "Valid Until",
                render(record, _index) {
                  return dayjs(record.validUntil).format("DD-MM-YYYY HH:mm");
                },
              },
              {
                accessor: "_",
                title: "Actions",
                render(record, _index) {
                  return (
                    <Group>
                      <RemoveSubscriptionPackageAssignement
                        assignementId={record.id}
                        partnerId={partnerId}
                        subPackageName={record.name}
                      />
                      <ActionIcon
                        onClick={() => {
                          console.log("edit");
                        }}
                        variant="light"
                        c="grey"
                      >
                        <FiEdit />
                      </ActionIcon>
                    </Group>
                  );
                },
              },
            ]}
            records={
              partner?.subscriptionPackageAssignment?.map((assignment) => ({
                id: assignment.id,
                subscriptionPackageId: assignment.subscriptionPackageId,
                validUntil: assignment.validUntil,
                name: assignment.subscriptionPackage.name,
              })) || []
            }
          />
        </Stack>
      </Paper>
    </>
  );
};

const PartnerContactInfo = ({ partnerId }: { partnerId: number }) => {
  const { data: registrationData } = usePartnerMainContact(partnerId);
  const form = useForm<any>({
    defaultValues: {
      firstName: registrationData?.firstName || "",
      lastName: registrationData?.lastName || "",
      email: registrationData?.email || "",
      enableNotifications: registrationData?.enableNotifications ?? true,
      phoneNumber: registrationData?.phoneNumber || "",
      jobTitle: registrationData?.jobTitle || "",
      country: registrationData?.country || "",
      companyName: registrationData?.companyName || "",
    },
  });

  const [edited, setEdited] = useState(false);

  const onChange = () => {
    if (!edited) setEdited(true);
    if (
      edited &&
      Object.entries(form.getValues()).every(
        ([k, v]) => registrationData?.[k] === v
      )
    )
      setEdited(false);
  };

  const onSave = (values: any) => {
    console.log("Saving changes:", values);
    setEdited(false);
  };

  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16}>
          <Title order={2}>Contact information</Title>
        </Flex>
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(onSave)}>
            <Stack gap="md">
              <Group grow>
                <TextInput
                  {...form.register("firstName", { onChange })}
                  label="First Name"
                  required
                />
                <TextInput
                  {...form.register("lastName", { onChange })}
                  label="Last Name"
                  required
                />
              </Group>
              <TextInput
                {...form.register("email", { onChange })}
                label="Email"
                type="email"
                required
              />
              <Group grow>
                <TextInput
                  {...form.register("phoneNumber", { onChange })}
                  label="Phone Number"
                />
                <TextInput
                  {...form.register("jobTitle", { onChange })}
                  label="Job Title"
                />
              </Group>
              <Group grow>
                <TextInput
                  {...form.register("country", { onChange })}
                  label="Country"
                />
                <TextInput
                  {...form.register("companyName", { onChange })}
                  label="Company Name"
                  required
                />
              </Group>
              <Checkbox
                {...form.register("enableNotifications", { onChange })}
                label="Enable Notifications"
              />
              {edited && (
                <Button type="submit" color="blue" mt="md">
                  Save Changes
                </Button>
              )}
            </Stack>
          </form>
        </FormProvider>
      </Stack>
    </Paper>
  );
};

const PartnerUserTable = ({ partnerId }: { partnerId: number }) => {
  const { data: users } = useUsers(partnerId);
  const usersMapped = users.map((x) => ({
    ...x,
    id: x.id,
    email: x.email,
    name: x.firstName + " " + x.lastName,
  }));
  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16}>
          <Title order={2}>Users</Title>
        </Flex>
        <DataTable
          records={usersMapped}
          columns={[
            {
              accessor: "id",
              title: "ID",
            },
            {
              accessor: "name",
            },
            {
              accessor: "email",
            },
            {
              accessor: "phoneNumber",
              title: "Phone Number",
            },
            {
              accessor: "jobTitle",
              title: "Job Title",
            },
          ]}
          onRowClick={({ record }) => {
            navigate(`/settings/users/${record.id}`);
          }}
        />
      </Stack>
    </Paper>
  );
};

const PartnerContractTable = ({ partnerId }: { partnerId: number }) => {
  const { data: contracts } = useContractsForPartner(partnerId);

  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16}>
          <Title order={2}>Contracts</Title>
        </Flex>
        <DataTable
          records={contracts || []}
          columns={[
            {
              accessor: "id",
              title: "Id",
            },
            {
              accessor: "status",
              title: "Status",
            },
            {
              accessor: "createdAt",
              title: "Created At",
              render(record, _index) {
                return dayjs(record.createdAt).format("DD-MM-YYYY HH:mm");
              },
            },
          ]}
          onRowClick={({ record }) => {
            navigate(`/settings/contracts/${record.id}`);
          }}
        />
      </Stack>
    </Paper>
  );
};

const PartnerSftpUSersTable = ({ partnerId }: { partnerId: number }) => {
  return (
    <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
      <Stack>
        <Flex pb={16}>
          <Title order={2}>SFTP Users</Title>
        </Flex>
        <DataTable
          records={[]}
          columns={[
            {
              accessor: "name",
            },
            {
              accessor: "email",
            },
          ]}
        />
      </Stack>
    </Paper>
  );
};

export const Page = () => {
  const ctx = usePageContext();
  const partnerId = parseInt(ctx.routeParams!["id"]);
  const { data: partner } = usePartner(partnerId);
  const disable = useDisablePartner(partnerId);

  return (
    <>
      <Group>
        <Title order={1}>{partner?.name}</Title>
        <Group gap={"md"} ml={"auto"}>
          <Button ml={"auto"} color="red" onClick={() => disable.mutate()}>
            Deactivate Partner
          </Button>
        </Group>
      </Group>
      <PartnerContactInfo partnerId={partnerId} />
      <PartnerUserTable partnerId={partnerId} />
      <PartnerSftpUSersTable partnerId={partnerId} />
      <PartnerContractTable partnerId={partnerId} />
      <PartnerSubscriptionPackagesTable partnerId={partnerId} />
    </>
  );
};

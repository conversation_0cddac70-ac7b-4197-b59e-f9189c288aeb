import { useAuth } from "#root/src/api/auth/auth.query";
import { usePartners } from "#root/src/api/partners/partner.query";
import type { FormatType } from "#root/src/api/tabular-formats/encode";
import type { MutationVariables, QueryData0 } from "#root/src/api/types";
import { useCreateUser, useUsers } from "#root/src/api/users/query";
import { getUsersAsTabularData } from "#root/src/api/users/users.telefunc";
import { DateTime } from "#root/src/components/DateTime.tsx";
import { DownloadSheetComboBox } from "#root/src/components/DownloadSheetComboBox";
import { Checkbox } from "#root/src/components/formfields/Checkbox";
import { Select } from "#root/src/components/formfields/Select";
import { TextInput } from "#root/src/components/formfields/TextInput";
import { scope } from "#root/src/shared/rbac.ts";
import { downloadFile } from "#root/src/utils/file-utils";
import {
  Badge,
  Button,
  Group,
  Modal,
  Stack,
  Text,
  Title,
  useMantineTheme,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { DataTable } from "mantine-datatable";
import { useCallback } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { navigate } from "vike/client/router";
import { useTranslation } from "react-i18next";

type User = QueryData0<typeof useUsers>;

const UserStatusBadge = (user: User) => {
  const theme = useMantineTheme();

  const StatusConfig: Record<
    string,
    { badgeColor: string; textColor: string; fullWidth?: boolean }
  > = {
    SUSPENDED: {
      badgeColor: theme.colors.red[4],
      textColor: theme.colors.red[8],
      fullWidth: true,
    },
    ACTIVE: {
      badgeColor: theme.colors.green[4],
      textColor: theme.colors.green[8],
    },
    DELETED: {
      badgeColor: theme.colors.gray[4],
      textColor: theme.colors.gray[8],
    },
    PENDING: {
      badgeColor: theme.colors.yellow[4],
      textColor: theme.colors.yellow[8],
    },
  };

  const statusConfig = StatusConfig[user.status];
  if (!statusConfig) return;

  return (
    <Badge
      fullWidth={statusConfig.fullWidth}
      radius={0}
      color={statusConfig.badgeColor}
    >
      <Text c={statusConfig.textColor} size={"sm"}>
        {user.status}
      </Text>
    </Badge>
  );
};

const PartnerSelectField = () => {
  const partners = usePartners();

  return (
    <Select
      name="partnerId"
      label="Partner"
      data={partners.data.map((partner) => ({
        value: partner.id.toString(),
        label: partner.name,
      }))}
    />
  );
};

const CreateUser = () => {
  const { hasScope } = useAuth();
  const createUser = useCreateUser();
  const [opened, { open, close }] = useDisclosure(false);
  const form = useForm<MutationVariables<typeof createUser>>();

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      // @ts-ignore
      await createUser.mutateAsync({ ...values, partnerId: parseInt(values.partnerId) });
      close();
      form.reset();
    } catch (error) {
      console.error("Failed to create user:", error);
    }
  });

  return (
    <>
      <Modal opened={opened} onClose={close} title="Create User">
        <FormProvider {...form}>
          <form onSubmit={onSubmit}>
            <Stack>
              <TextInput name="email" label="Email" type="email" required />
              <TextInput name="firstName" label="First Name" required />
              <TextInput name="lastName" label="Last Name" required />
              <TextInput name="phoneNumber" label="Phone number" required />
              <TextInput name="jobTitle" label="Job Title" required />
              {hasScope(scope("partners").R) && <PartnerSelectField />}
              <Checkbox
                name="enableNotifications"
                label="Enable Email Notifications"
              />
              <Button type="submit" loading={createUser.isPending}>
                Create
              </Button>
            </Stack>
          </form>
        </FormProvider>
      </Modal>
      <Button ml="auto" onClick={open}>
        Add new
      </Button>
    </>
  );
};

export const Page = () => {
  const { data: users } = useUsers();
  const { t } = useTranslation();

  const onDownload = useCallback(async (format: FormatType) => {
    const data = await getUsersAsTabularData(format);
    downloadFile(
      `users.${format}`,
      data,
      format === "xlsx"
        ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        : "text/csv"
    );
  }, []);

  return (
    <>
      <Group pb="16" align="flex-start">
        <Title order={4}>Users</Title>
        <CreateUser />
        <DownloadSheetComboBox onClick={onDownload} />
      </Group>
      <DataTable
        onRowClick={(row) => {
          navigate(`/settings/users/${row.record.id}`);
        }}
        columns={[
          { accessor: "_", title: "#", render: (_record, index) => index + 1 },
          { accessor: "id", title: "Database Id" },
          { accessor: "firstName" },
          { accessor: "lastName" },
          { accessor: "partner.name" },
          {
            accessor: "email",
          },
          {
            accessor: "status",
            title: "User status",
            render: (r) => {
              return UserStatusBadge(r);
            },
          },
          {
            accessor: "type",
            title: "User type",
            render: (r) => t(`UserType.${r.type}`),
          },
          {
            accessor: "lastOnline",
            title: "Last Online",
            render: ({ lastOnline }) =>
              lastOnline ? (
                <DateTime
                  date={lastOnline}
                  format={(d) =>
                    `${d.toLocaleDateString("hu")} ${d.toLocaleTimeString(
                      "hu"
                    )}`
                  }
                />
              ) : (
                "N/A"
              ),
          },
        ]}
        records={users}
      />
    </>
  );
};

import { useAuth } from "#root/src/api/auth/auth.query.ts";
import { usePartners } from "#root/src/api/partners/partner.query";
import { useUpdateUser, useUser } from "#root/src/api/users/query.tsx";
import { Checkbox } from "#root/src/components/formfields/Checkbox.tsx";
import { Select } from "#root/src/components/formfields/Select.tsx";
import { TextInput } from "#root/src/components/formfields/TextInput.tsx";
import { UserStatus, type UserType } from "#root/src/shared/constants.ts";
import { Button, Group, Paper, Stack, Text, Title } from "@mantine/core";
import { modals } from "@mantine/modals";
import { useEffect, useMemo } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { CiHashtag } from "react-icons/ci";
import { usePageContext } from "vike-react/usePageContext";
import { useTranslation } from "react-i18next";
import type { TFunction } from "i18next";

const mapUserToFormValues = (user?: ReturnType<typeof useUser>["data"]) => ({
  id: user?.id ?? 0,
  email: user?.email ?? "",
  status: (user?.status as UserStatus) ?? undefined,
  type: (user?.type as UserType) ?? undefined,
  partnerId: user?.partnerId?.toString() ?? undefined,
  firstName: user?.firstName ?? "",
  lastName: user?.lastName ?? "",
  phoneNumber: user?.phoneNumber ?? "",
  jobTitle: user?.jobTitle ?? "",
  enableNotifications: user?.enableNotifications ?? false,
});

type UserFormValues = ReturnType<typeof mapUserToFormValues>;

export const Page = () => {
  const { t } = useTranslation();

  const ctx = usePageContext();
  const userId = parseInt(ctx.routeParams!["id"]);
  const { data: user } = useUser(userId);
  const canEditPartnerMapping =
    !user?.partner || user?.partner?.subscriptionPackageAssignment.length === 0;
  const partners = usePartners();
  const updateUser = useUpdateUser();

  const form = useForm<UserFormValues>({
    defaultValues: mapUserToFormValues(user),
  });

  const { hierarchyBelow } = useAuth();

  useEffect(() => {
    form.reset(mapUserToFormValues(user));
  }, [user, form]);

  const currentPartnerId = form.watch("partnerId");
  const currentToPartnerName = useMemo(
    () => partners.data.find((x) => x.id.toString() === currentPartnerId)?.name,
    [currentPartnerId, partners.data]
  );

  const openModal = () =>
    new Promise<void>((res, rej) =>
      modals.openConfirmModal({
        title: "Partner remapping",
        children: (
          <Text size="sm">
            Are you sure that you want to remap this users partner from{" "}
            <strong>{user?.partner?.name ?? "No assigned partner"}</strong> to{" "}
            <strong>
              {currentToPartnerName ?? "Invalid modal invocation"}
            </strong>
            This action is so important that you are required to confirm it with
            a modal.
          </Text>
        ),
        labels: { confirm: "Confirm", cancel: "Cancel" },
        onCancel: rej,
        onConfirm: res,
      })
    );

  const onSubmit = form.handleSubmit(async (values) => {
    try {
      if (user && values.partnerId !== user?.partnerId?.toString()) {
        await openModal();
      }
      await updateUser.mutateAsync(values);
      form.reset(values);
    } catch (error) {
      if (error) {
        console.error("Failed to update user: ", error);
      }
    }
  });

  return (
    <>
      <Group gap={"sm"}>
        <Title order={1} size={"lg"}>
          <Group gap={0}>
            <CiHashtag />
            {user?.id}
          </Group>
        </Title>
        <Title order={2}>
          {user?.firstName} {user?.lastName}
        </Title>
      </Group>
      <Paper shadow={"xs"} p={"2rem"} mt={"1rem"}>
        <FormProvider {...form}>
          <form onSubmit={onSubmit}>
            <Stack>
              <TextInput
                {...form.register("email")}
                label="Email"
                type="email"
                required
              />
              <TextInput
                {...form.register("firstName")}
                label="First Name"
                required
              />
              <TextInput
                {...form.register("lastName")}
                label="Last Name"
                required
              />
              <TextInput
                {...form.register("phoneNumber")}
                label="Phone number"
                required
              />
              <TextInput
                {...form.register("jobTitle")}
                label="Job Title"
                required
              />
              <Select
                name="status"
                label="Status"
                data={userStatusEnumToSelectData(UserStatus, t)}
                required
              />
              <Select
                name="type"
                label="Type"
                data={hierarchyBelow.map((k) => ({
                  value: k,
                  label: t(`UserType.${k}`),
                }))}
                required
              />
              {canEditPartnerMapping && (
                <Select
                  name="partnerId"
                  label="Partner"
                  data={partners.data.map((partner) => ({
                    value: partner.id.toString(),
                    label: partner.name,
                  }))}
                  required
                />
              )}
              <Checkbox
                name="enableNotifications"
                label="Enable Email Notifications"
              />
              {form.formState.isDirty && (
                <Button type="submit" loading={updateUser.isPending}>
                  Update
                </Button>
              )}
            </Stack>
          </form>
        </FormProvider>
      </Paper>
    </>
  );
};

function userStatusEnumToSelectData<T extends string>(
  enumObject: Record<string, T>,
  t: TFunction<"translation">
) {
  return Object.entries(enumObject).map(([key, value]) => ({
    value: value,
    label: t(`UserStatus.${key}`),
  }));
}

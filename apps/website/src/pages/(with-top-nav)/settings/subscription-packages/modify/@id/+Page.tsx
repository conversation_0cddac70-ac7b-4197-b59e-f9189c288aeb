import { TextInput } from "#root/src/components/formfields/TextInput";
import { Button, Group, Modal, Paper, Stack, Text, Title } from "@mantine/core";
import { FormProvider, useForm } from "react-hook-form";
import { useEffect } from "react";
import { useSubscriptionPackageAssignement } from "#root/src/api/subscription-packages/subscription-packages.query.ts";
import { usePageContext } from "vike-react/usePageContext";
import { DatePicker } from "#root/src/components/formfields/DatePicker";
import { useDisclosure } from "@mantine/hooks";

type ModifySubscriptionPackageFormProps = {
  assignmentId: string;
};

type ModifySubscriptionPackageFormValues = {
  packagePrice: number;
  numberOfUsers: number;
  currency: string;
  validUntil: Date;
};

export const ModifySubscriptionPackageForm = (
  props: ModifySubscriptionPackageFormProps
) => {
  const { assignmentId } = props;
  const { data: subPackAssignment } =
    useSubscriptionPackageAssignement(assignmentId);
  const [confirmOpened, { open: confirmOpen, close: confirmClose }] =
    useDisclosure();
  const [respOpened, { open: respOpen, close: respClose }] = useDisclosure();

  const form = useForm<ModifySubscriptionPackageFormValues>({
    defaultValues: {
      packagePrice: subPackAssignment?.subscriptionPackage.priceEur || 0,
      numberOfUsers: 0,
      currency: "EUR",
      validUntil: subPackAssignment?.validUntil,
    },
  });

  const onSubmit = form.handleSubmit(async (values) => {
    console.log("NOT IMPLEMENTED, values are", values);
    confirmClose();
    respOpen();
  });

  useEffect(() => {
    if (subPackAssignment) {
      form.reset({
        packagePrice: subPackAssignment.subscriptionPackage.priceEur,
        numberOfUsers: 0,
        currency: "EUR",
        validUntil: subPackAssignment.validUntil,
      });
    }
  }, [subPackAssignment]);

  return (
    <Paper mx={"auto"} p="md" shadow="xs">
      <Stack w={500}>
        <Text fs={"italic"}>{`Modifying Subscription Package`}</Text>
        <Title order={2} mb="sm">
          {subPackAssignment?.subscriptionPackage.name}
        </Title>
        <FormProvider {...form}>
          <form onSubmit={onSubmit}>
            <Modal
              opened={confirmOpened}
              onClose={confirmClose}
              title={
                <Text fw={900} size={"xl"}>
                  Confirmation needed
                </Text>
              }
              size="lg"
            >
              <Stack>
                <Text>
                  {
                    "Modifying the subscription requires a new contract to be signed."
                  }
                </Text>
                <Text mb={"md"}>{"Are you sure you want to continue?"}</Text>
                <Group ml={"auto"}>
                  <Button type="submit" onClick={onSubmit}>
                    {"Confirm"}
                  </Button>
                  <Button onClick={confirmClose}>{"Cancel"}</Button>
                </Group>
              </Stack>
            </Modal>

            <TextInput
              name="packagePrice"
              label="Package Price"
              readOnly
              type="number"
            />
            <TextInput name="currency" label="Currency" readOnly />
            <TextInput
              name="numberOfUsers"
              label="Number of Users"
              type="number"
            />
            <DatePicker label={"Expiration Date"} name={"validUntil"} />
            <Button
              onClick={confirmOpen}
              mt="md"
              disabled={!form.formState.isDirty}
            >
              Modify Subscription Package
            </Button>
          </form>
        </FormProvider>
      </Stack>
      <Modal opened={respOpened} onClose={respClose}>
        <Stack>
          <Text mx={"auto"} mb="xl">
            {"The update request has been sent to HUPEX!"}
          </Text>
          <Button mx={"auto"} w={150} onClick={respClose}>
            {"Ok"}
          </Button>
        </Stack>
      </Modal>
    </Paper>
  );
};

export const Page = () => {
  const ctx = usePageContext();
  const assignmentId = ctx.routeParams["id"];

  return <ModifySubscriptionPackageForm assignmentId={assignmentId} />;
};

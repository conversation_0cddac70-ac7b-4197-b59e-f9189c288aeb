import { useSubscriptionPackages } from "#root/src/api/subscription-packages/subscription-packages.query";
import { Button, Group, Stack, Text, Title, Tooltip } from "@mantine/core";
import { DataTable } from "mantine-datatable";
import { navigate } from "vike/client/router";

export const Page = () => {
  const { data: subscriptionPackages } = useSubscriptionPackages();

  const columns = [
    { title: "Id", accessor: "id" },
    { title: "Name", accessor: "name" },
    { title: "Price [EUR]", accessor: "priceEur" },
    {
      title: "Included subscriptions",
      accessor: "-",
      render: (record: { subscriptions: any[] }) => {
        const subs = record.subscriptions
          .map(
            (s) =>
              `${s.subscription.dataSetName} - ${s.subscription.subscriptionName}`
          )
          .join(", ");
        return (
          <Tooltip label={subs}>
            <Text w={300} truncate={"end"} size={"sm"}>
              {subs}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: "Created at",
      accessor: "createdAt",
      render: (row: { createdAt: Date }) =>
        new Date(row.createdAt).toLocaleString(),
    },
  ];

  return (
    <>
      <Stack p={"1rem"}>
        <Group gap={"xs"}>
          <Title>Subscription Packages</Title>
          <Button
            onClick={() => navigate("/settings/subscription-packages/new")}
            ml={"auto"}
          >
            Add New
          </Button>
        </Group>
        <DataTable records={subscriptionPackages || []} columns={columns} />
      </Stack>
    </>
  );
};

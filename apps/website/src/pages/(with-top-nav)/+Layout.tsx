import { usePageContext } from "vike-react/usePageContext";
import { FULL_HEADER_HEIGHT, Header } from "#root/src/components/Header";
import { navigate } from "vike/client/router";
import { useAuth } from "#root/src/api/auth/auth.query";
import { Loader, useMantineTheme } from "@mantine/core";
import { Suspense } from "react";

const publicRoutes = [
  "/signin",
  "/signup",
  "/",
  "/view",
  "/data-request",
  "/pricing",
  "/about_us",
  "/changelog",
];

export default function Layout({ children }) {
  const { profile } = useAuth();
  const ctx = usePageContext();
  const theme = useMantineTheme();
  const isPublic = publicRoutes.some((p) => {
    return (
      ctx.urlPathname.startsWith(p) && !ctx.urlPathname.startsWith("/settings")
    );
  });
  if (!profile && !isPublic) {
    navigate("/");
    return;
  }
  return (
    <>
      <Header />
      <div
        css={{
          flexGrow: 1,
          display: "flex",
          flexDirection: "column",
        }}
        style={{
          marginTop: FULL_HEADER_HEIGHT,
        }}
      >
        <Suspense fallback={<Loader mx="auto" mt="2xl" />}>{children}</Suspense>
      </div>
      {/*<ReactQueryDevtools initialIsOpen={false} />*/}
    </>
  );
}

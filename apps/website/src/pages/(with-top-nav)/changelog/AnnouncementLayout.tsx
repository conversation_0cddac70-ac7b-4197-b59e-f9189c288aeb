import { FULL_HEADER_HEIGHT } from "#root/src/components/Header";
import { useOpenApi } from "#root/src/openapi/openapi.query";
import { AnnouncementType } from "#root/src/shared/constants";
import {
  Box,
  Button,
  Collapse,
  MultiSelect,
  Stack,
  TextInput,
  useMantineTheme,
} from "@mantine/core";
import { DatePickerInput } from "@mantine/dates";
import dayjs from "dayjs";
import { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { IoClose } from "react-icons/io5";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";
import { useAppStore } from "../../store";
import { useDisclosure } from "@mantine/hooks";

function NavbarSimple(props) {
  const theme = useMantineTheme();
  const { filter, setFilter, clearFilter } = useAppStore(
    (s) => s.announcements
  );
  const { t } = useTranslation();
  const { data } = useOpenApi();
  const [isOpen, { toggle }] = useDisclosure(true);

  const dataSetsFormatted = useMemo(
    () =>
      data?.groupedOperations?.map((x) => ({
        group: x.group!,
        items: x.operations.map((op) => ({
          value: op.operationId!,
          label: op.dataViewUrlName ?? op.url ?? op.operationId!,
        })),
      })) || [],
    [data]
  );

  return (
    <nav style={props.style} className={props.className}>
      <div
        css={{
          height: "calc(100% - 36px);",
          overflow: "auto",
        }}
      >
        <Box
          css={{
            width: "100%",
            display: "flex",
            alignItems: "center",
            cursor: "pointer",
            fontWeight: 600,
            backgroundColor: theme.colors[theme.primaryColor][1],
          }}
          onClick={toggle}
        >
          <Button
            variant="transparent"
            color={theme.colors[theme.primaryColor][8]}
          >
            Filters
          </Button>
          <div
            css={{
              marginLeft: "auto",
              display: "flex",
            }}
          >
            {isOpen ? (
              <MdKeyboardArrowUp size={"20"} />
            ) : (
              <MdKeyboardArrowDown size={"20"} />
            )}
          </div>
        </Box>

        <Collapse in={isOpen}>
          <Stack p="xs">
            <Button
              variant="light"
              leftSection={<IoClose size={18} />}
              onClick={() => clearFilter()}
            >
              Clear Filters
            </Button>

            <TextInput
              label="Content"
              value={filter.textContent}
              onChange={(e) =>
                setFilter({ ...filter, textContent: e.target.value })
              }
            />

            <MultiSelect
              label="Data Sources"
              value={filter.dataSources}
              onChange={(v: any) =>
                setFilter({ ...filter, dataSources: v ?? undefined })
              }
              data={dataSetsFormatted}
            />

            <MultiSelect
              label="Type"
              value={filter.type ? filter.type : []}
              onChange={(v: any) =>
                setFilter({ ...filter, type: v ?? undefined })
              }
              data={Object.values(AnnouncementType).map((e) => ({
                value: e,
                label: t(`AnnouncementType.${e}`),
              }))}
              clearable
            />

            <DatePickerInput
              label="Date Range"
              type="range"
              value={
                (filter.dateRange?.map((v) => v && new Date(v)) || [
                  null,
                  null,
                ]) as [Date, Date]
              }
              onChange={(v) =>
                setFilter({
                  ...filter,
                  dateRange:
                    v[0] === null && v[1] === null
                      ? undefined
                      : ([v[0], v[1]].map(
                          (v) => v && dayjs(v).format("YYYY-MM-DD")
                        ) as [string, string]),
                })
              }
              clearable
            />
          </Stack>
        </Collapse>
      </div>
    </nav>
  );
}

export function AnnouncementLayout({ children }) {
  const theme = useMantineTheme();
  return (
    <div
      css={{
        height: "100%",
        position: "relative",
        display: "flex",
        background: "white",
      }}
    >
      <NavbarSimple
        style={{
          top: FULL_HEADER_HEIGHT,
        }}
        css={{
          position: "fixed",
          left: 0,
          bottom: 0,
          width: 250,
          borderRight: `1px solid ${theme.colors.gray[2]}`,
          background: "inherit",
        }}
      />
      <div
        css={{
          flexGrow: 1,
          marginLeft: 250,
          display: "flex",
          flexDirection: "column",
          background: "inherit",
        }}
      >
        {children}
      </div>
    </div>
  );
}

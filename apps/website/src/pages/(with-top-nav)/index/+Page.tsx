import { BlockTitle } from "#root/src/components/BlockTitle";
import { DoYouFeelLike } from "#root/src/components/DoYouFeelLike";
import { isDisabledPlot } from "#root/src/components/plots/constants";
import { TradingViewFooter } from "#root/src/components/TradingViewFooter";
import { useOpenApi } from "#root/src/openapi/openapi.query";
import {
  Badge,
  Button,
  Card,
  Grid,
  Group,
  SimpleGrid,
  Stack,
  Text,
} from "@mantine/core";

import ApiDataCard, {
  ApiDataCardSimple,
} from "#root/src/components/ApiDataCard";
import { DateTime } from "#root/src/components/DateTime";
import IIPTable from "#root/src/components/IIPTable";
import LatestTradesIDMComponent from "#root/src/components/LatestTradesIDMComponent";
import {
  DAMBaseLoadCard,
  DAMCard,
  ID3Card,
  IDACard,
} from "#root/src/components/StatCards";
import { useNow } from "#root/src/hooks/useNow";
import dayjs from "dayjs";
import timezone from "dayjs/plugin/timezone";
import utc from "dayjs/plugin/utc";
import { Suspense } from "react";

// Configure dayjs to use the plugins
dayjs.extend(utc);
dayjs.extend(timezone);

const ChartGrid = () => {
  const openapi = useOpenApi();
  return (
    <Grid>
      {openapi.data.operations
        .filter((item) => !isDisabledPlot(item.operationId!))
        .map((item) => (
          <Grid.Col span={4} key={item.operationId}>
            <Suspense>
              <ApiDataCard
                key={item.operationId}
                operationId={item.operationId!}
              />
            </Suspense>
          </Grid.Col>
        ))}
    </Grid>
  );
};

export default function Page() {
  const now = useNow();
  // return <div>ok</div>
  return (
    <Stack p="xl">
      <BlockTitle label="Welcome to HUPX Labs Platform" fz="26" mb="xl" />

      <Card>
        <Card.Section>
          <SimpleGrid cols={2} w="fit-content">
            <Text fw={500} size="xl">
              Current Prices
            </Text>
            <Badge h="100%">
              <DateTime
                date={now.toDate()}
                format={(d) => dayjs(d).format("ddd MMM DD HH:mm")}
              />
            </Badge>
          </SimpleGrid>
        </Card.Section>
        <Card.Section>
          <SimpleGrid cols={{ base: 1, md: 6 }}>
            <DAMBaseLoadCard />
            <DAMCard />
            <ID3Card />
            <IDACard n={1} />
            <IDACard n={2} />
            <IDACard n={3} />
          </SimpleGrid>
        </Card.Section>
      </Card>

      <Grid>
        <Grid.Col
          span={{
            base: 12,
            lg: 4,
          }}
        >
          <Card>
            <Card.Section>
              <Group>
                <Text fw={500} size="xl">
                  Latest trades - IDC
                </Text>

                <Button
                  component="a"
                  href="/view/IDC_v1"
                  size="sm"
                  ml="auto"
                  variant="subtle"
                >
                  View More
                </Button>
              </Group>
            </Card.Section>
            <Card.Section h={400}>
              <LatestTradesIDMComponent />
            </Card.Section>
          </Card>
        </Grid.Col>
        <Grid.Col
          span={{
            base: 12,
            lg: 8,
          }}
        >
          <Card>
            <Card.Section>
              <Group>
                <Text fw={500} size="xl">
                  Day-Ahead Market
                </Text>

                <Button
                  component="a"
                  href="/view/DAM_Aggregated_Trading_Data_v1"
                  size="sm"
                  ml="auto"
                  variant="subtle"
                >
                  View More
                </Button>
              </Group>
            </Card.Section>
            <Card.Section h={400}>
              <ApiDataCardSimple operationId="DAM_Aggregated_Trading_Data_v1" />
            </Card.Section>
          </Card>
        </Grid.Col>
      </Grid>

      <Card>
        <Card.Section>
          <Group>
            <Text fw={500} size="xl">
              Current outages
            </Text>
            <Button
              component="a"
              href="/view/IIP_v1"
              size="sm"
              ml="auto"
              variant="subtle"
            >
              View More
            </Button>
          </Group>
        </Card.Section>
        <Card.Section>
          <Suspense fallback="">
            <IIPTable />
          </Suspense>
        </Card.Section>
      </Card>
      <ChartGrid />
      <DoYouFeelLike text="Do you feel like you're missing data?" />
      <TradingViewFooter />
    </Stack>
  );
}

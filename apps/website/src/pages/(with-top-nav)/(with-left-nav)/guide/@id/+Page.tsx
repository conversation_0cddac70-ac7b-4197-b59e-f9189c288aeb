/** eslint-disable sonarjs/no-duplicate-string */
import { BlockTitle } from "#root/src/components/BlockTitle";
import { CodeBlock } from "#root/src/components/Codeblock";
import { OperationIcon } from "#root/src/components/OperationBadge";
import { OperationHeader } from "#root/src/components/OperationHeader";
import { ActionBar } from "#root/src/filters/components/ActionBar";
import { useOperationFilters } from "#root/src/filters/store";
import { getOperatorDisplayName } from "#root/src/filters/utils";
import {
  useNonSuspenseApiData,
  useOperation,
} from "#root/src/openapi/openapi.query";
import { ExplanationTable } from "#root/src/pages/(with-top-nav)/(with-left-nav)/view/@id/ExplanationTable";
import {
  Badge,
  Box,
  Button,
  Card,
  Divider,
  Group,
  Select,
  Stack,
  Table,
  Text,
  TextInput,
  useMantineTheme,
} from "@mantine/core";
import { useMemo, useState } from "react";
import { FiSearch } from "react-icons/fi";
import { usePageContext } from "vike-react/usePageContext";

function ExampleData({ operationId }: { operationId: string }) {
  const { serialized, hasFilters, dateField } =
    useOperationFilters(operationId);
  const theme = useMantineTheme();
  const [query, languages] = useNonSuspenseApiData({
    generateCode: true,
    operationId,
    params: {
      ...(hasFilters && {
        filter: serialized,
      }),
      limit: 50,
    },
  });

  const [selectedLang, setSelectedLang] = useState("JavaScript");
  const changeLanguage = (value: string | null) => {
    if (!value) {
      setSelectedLang("JavaScript");
      return;
    }
    setSelectedLang(value);
  };
  const [exampleCode, examplePrismGrammar] = languages[selectedLang]!;
  const formattedJsonString = useMemo(
    () => query.data && JSON.stringify(query.data, null, 2),
    [query.data]
  );
  return (
    <>
      <Table stickyHeader>
        <Table.Thead>
          <Table.Tr>
            <Table.Th fw={500} c="dark.4" style={{ width: "20%" }}>
              Filter name
            </Table.Th>
            <Table.Th fw={500} c="dark.4">
              Value
            </Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {serialized.map((filter, index) => {
            const [field, operator, value] = filter.split("__");
            return (
              <Table.Tr key={index}>
                <Table.Td fw={600}>
                  <Group>
                    {field === dateField ? <Badge>Date</Badge> : null}
                    {`${field} ${operator}`}
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Text>{value}</Text>
                </Table.Td>
              </Table.Tr>
            );
          })}
        </Table.Tbody>
      </Table>

      <Group mt="md">
        <Select
          label="Choose Language"
          placeholder="JavaScript"
          value={selectedLang}
          onChange={changeLanguage}
          data={Object.keys(languages)}
        />
        <Button
          mt="24px"
          onClick={() => query.refetch()}
          loading={query.isLoading || query.isRefetching}
        >
          Generate Response
        </Button>
      </Group>

      <Group mt="md">
        <CodeBlock code={exampleCode} language={examplePrismGrammar} />
      </Group>

      <Group mt={theme.spacing.xs}>
        <CodeBlock
          code={
            formattedJsonString ||
            "Press the 'Try it out' button to see the response."
          }
          language={"javascript"}
        />
      </Group>
    </>
  );
}

function Loader({ operationId }: { operationId: string }) {
  const theme = useMantineTheme();
  const operation = useOperation({ operationId });
  const { regularFilterDefinitions } = useOperationFilters(operationId);

  const [searchQuery, setSearchQuery] = useState<string>();
  const [selectedFormat, setSelectedFormat] = useState<"XML" | "JSON">();

  return (
    <Box m="lg">
      <Card>
        <Card.Section p="lg">
          <Group>
            <OperationIcon operation={operation} />
            <Text fw={500} size="xl">
              {operation.url}
            </Text>

            <Button
              component="a"
              href={`/view/${operationId}`}
              size="sm"
              ml="auto"
              variant="subtle"
            >
              Go to Data View
            </Button>
          </Group>
        </Card.Section>
        <Card.Section p="lg">
          {operation.description}
          <Divider mt={theme.spacing.lg} mb={0} />
          <ActionBar
            my="lg"
            operationId={operationId}
            title="Active Parameters"
          />
          <ExampleData operationId={operationId} />
          <Group my="lg">
            <Text size="lg" fw={500}>
              All Valid Parameters
            </Text>
            <TextInput
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </Group>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th
                  style={{ width: "20%" }}
                  fw={600}
                  c={theme.colors.neutral[4]}
                >
                  Filter name
                </Table.Th>
                <Table.Th style={{ width: "40%" }} c={theme.colors.neutral[4]}>
                  Description
                </Table.Th>
                <Table.Th style={{ width: "40%" }} c={theme.colors.neutral[4]}>
                  Example Value
                </Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {regularFilterDefinitions
                .filter((x) =>
                  searchQuery && searchQuery.length > 0
                    ? Object.values(x).some(
                        (v: any) =>
                          v.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          searchQuery.toLowerCase().includes(v.toLowerCase())
                      )
                    : true
                )
                .map((x) => (
                  <Table.Tr key={x.name}>
                    <Table.Td style={{ whiteSpace: "nowrap" }}>
                      {x.title} {getOperatorDisplayName(x.operator)}
                    </Table.Td>
                    <Table.Td style={{ whiteSpace: "nowrap" }}>
                      Description
                    </Table.Td>
                    <Table.Td style={{ whiteSpace: "nowrap" }}>100</Table.Td>
                  </Table.Tr>
                ))}
            </Table.Tbody>
          </Table>
          <Group my="lg">
            <Text size="lg" fw={500}>
              Responses
            </Text>
            <Select
              placeholder="JSON"
              value={selectedFormat}
              onChange={(value) => setSelectedFormat((value as any) ?? null)}
              data={["JSON", "XML"]}
            />
          </Group>

          <Stack>
            {/*Not sure if we need to filter this out but on the design we do FIXME!*/}
            {operation.responses
              .filter((x) => x.statusCode !== "default")
              .map((item) => (
                <Box key={item.statusCode}>
                  <Group
                    style={{
                      display: "flex",
                      alignItems: "center",
                      gap: theme.spacing.md,
                    }}
                  >
                    <Badge size="lg" color="dark" radius={0}>
                      Code: {item.statusCode}
                    </Badge>
                    <Badge
                      size="lg"
                      radius={0}
                      color={
                        item.statusCode.startsWith("2") ||
                        item.statusCode.startsWith("3")
                          ? "green"
                          : "red"
                      }
                    >
                      {item.statusCode.startsWith("2") ||
                      item.statusCode.startsWith("3")
                        ? "Successful response"
                        : "Error response"}
                    </Badge>
                  </Group>
                  <Group>
                    <CodeBlock
                      code={
                        selectedFormat === "XML"
                          ? item.xmlSample!
                          : item.sample!
                      }
                      language={selectedFormat === "XML" ? "xml" : "javascript"}
                    />
                  </Group>
                </Box>
              ))}
          </Stack>
          <Text size="lg" fw={500} my="lg">
            Column Descriptions
          </Text>
          <ExplanationTable
            data={operation.columns.map((col) => ({
              name: col.name,
              explanation: col.description || "",
              unit: col.unit,
            }))}
          />
        </Card.Section>
      </Card>
    </Box>
  );
}

export default function Page() {
  const ctx = usePageContext();
  const operationId = ctx.routeParams!["id"];
  return <Loader operationId={operationId} key={operationId} />;
}

import { Table, useMantineTheme } from "@mantine/core";

type Data = { name: string; unit: string; explanation: string }[];

export const ExplanationTable = ({ data }: { data: Data }) => {
  const theme = useMantineTheme();

  return (
    <Table stickyHeader>
      <Table.Thead>
        <Table.Tr>
          <Table.Th
            style={{ width: "15%" }}
            fw={600}
            c={theme.colors.neutral[4]}
          >
            Column name
          </Table.Th>
          <Table.Th
            style={{ width: "15%" }}
            fw={600}
            c={theme.colors.neutral[4]}
          >
            Unit
          </Table.Th>
          <Table.Th
            style={{ width: "70%" }}
            fw={600}
            c={theme.colors.neutral[4]}
          >
            Description
          </Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {data.map((x) => (
          <Table.Tr key={x.name}>
            <Table.Td>{x.name}</Table.Td>
            <Table.Td>{x.unit}</Table.Td>
            <Table.Td
              style={{
                whiteSpace: "normal",
                wordBreak: "break-word",
                lineHeight: "1.5",
              }}
            >
              {x.explanation}
            </Table.Td>
          </Table.Tr>
        ))}
      </Table.Tbody>
    </Table>
  );
};

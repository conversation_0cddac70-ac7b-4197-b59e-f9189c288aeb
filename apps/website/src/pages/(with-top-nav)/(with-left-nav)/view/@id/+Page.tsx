import { OperationIcon } from "#root/src/components/OperationBadge";
import { isDisabledPlot } from "#root/src/components/plots/constants";
import { TradingViewFooter } from "#root/src/components/TradingViewFooter";
import { ActionBar } from "#root/src/filters/components/ActionBar";
import { useOperationFilters } from "#root/src/filters/store";
import { useOperation } from "#root/src/openapi/openapi.query";
import { ExplanationTable } from "#root/src/pages/(with-top-nav)/(with-left-nav)/view/@id/ExplanationTable";
import {
  Box,
  Button,
  Card,
  Divider,
  Group,
  LoadingOverlay,
  Text,
  useMantineTheme,
} from "@mantine/core";
import { useState } from "react";
import { clientOnly } from "vike-react/clientOnly";
import { usePageContext } from "vike-react/usePageContext";

const ApiDataPlotWithTable = clientOnly(
  () => import("#root/src/components/ApiDataPlotWithTableClientOnly")
);

function Loader({
  operationId,
  params,
}: {
  operationId: string;
  params?: {
    [key: string]: string | number | string[] | number[] | Date[];
  };
}) {
  const [ready, setReady] = useState(false);

  return (
    <div
      id={"LOADING_WRAP"}
      css={{
        position: "relative",
        minHeight: 400,
      }}
    >
      <LoadingOverlay
        visible={!ready}
        zIndex={100}
        overlayProps={{ radius: "sm", blur: 2 }}
        loaderProps={{
          style: {
            alignSelf: "start",
            marginTop: 70,
          },
        }}
      />
      <ApiDataPlotWithTable
        operationId={operationId}
        onReady={() => {
          setReady(true);
        }}
        params={params}
        ChartComponentProps={{
          showControls: true,
        }}
      />
    </div>
  );
}

export default function Page() {
  const ctx = usePageContext();
  const operationId = ctx.routeParams!["id"];
  return <KeyedPage key={operationId} />;
}

function KeyedPage() {
  const ctx = usePageContext();
  const operationId = ctx.routeParams!["id"];
  const { params } = useOperationFilters(operationId);
  const theme = useMantineTheme();
  const operation = useOperation({ operationId });
  const showPlot = !isDisabledPlot(operationId);
  return (
    <Box m="lg">
      <Card>
        <Card.Section p="lg">
          <Group>
            <OperationIcon operation={operation} />
            <Text fw={500} size="xl">
              {operation.url}
            </Text>

            <Button
              component="a"
              href={`/guide/${operationId}`}
              size="sm"
              ml="auto"
              variant="subtle"
            >
              Go to Developer Guide
            </Button>
          </Group>
        </Card.Section>
        <Card.Section p="lg">
          {operation.description}
          <Divider mt={theme.spacing.lg} mb={0} />
          <ActionBar
            my="lg"
            operationId={operationId}
            title={showPlot ? "Chart View" : ""}
          />
          <Loader operationId={operationId} key={operationId} params={params} />
          <Text size="lg" fw={500} my="lg">
            Column Descriptions
          </Text>
          <ExplanationTable
            data={operation.columns.map((col) => ({
              name: col.title,
              explanation: col.description || "",
              unit: col.unit,
            }))}
          />
        </Card.Section>
      </Card>

      <Divider my="xl" />
      <TradingViewFooter />
    </Box>
  );
}

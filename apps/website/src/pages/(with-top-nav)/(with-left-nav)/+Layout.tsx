import { useProfile } from "#root/src/api/auth/auth.query";
import { FULL_HEADER_HEIGHT } from "#root/src/components/Header";
import { useOpenApi } from "#root/src/openapi/openapi.query";
import { dark, desktop } from "#root/src/pages/theme";
import { isAdminType } from "#root/src/utils/auth-utils";
import {
  Box,
  Button,
  Collapse,
  ScrollArea,
  TextInput,
  useMantineTheme,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useMemo, useState } from "react";
import { FiSearch } from "react-icons/fi";
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md";
import { usePageContext } from "vike-react/usePageContext";

const OperationGroup = ({
  group,
}: {
  group: ReturnType<typeof useOpenApi>["data"]["groupedOperations"][0];
}) => {
  const theme = useMantineTheme();
  const ctx = usePageContext();
  const route = ctx.urlPathname.split("/")[1];
  const [opened, { toggle }] = useDisclosure(true);
  const { data: profile } = useProfile();
  const links = group.operations.map((item) => {
    const isActive = ctx.urlPathname === `/${route}/${item.operationId}`;

    if (!profile && !item.hasPublicSubscription) {
      return null;
    }

    return (
      <a
        css={{
          position: "relative",

          display: "flex",
          alignItems: "center",
          gap: theme.spacing.sm,
          textDecoration: "none",
          fontSize: theme.fontSizes.sm,
          color: isActive ? "white" : theme.colors.gray[7],
          borderRadius: theme.radius.md,
          // marginRight: theme.spacing.md,
          width: "100%",
          padding: 8,
          fontWeight: 500,
          [dark]: {
            color: theme.colors.dark[1],
          },
        }}
        data-active={undefined}
        href={`/${route}/${item.operationId}`}
        key={item.operationId}
      >
        <span
          css={{
            backgroundColor: isActive
              ? theme.colors[theme.primaryColor][8]
              : "none",
            width: "100%",
            height: 38,
            display: "flex",
            alignItems: "center",
            borderRadius: theme.radius.md,
            paddingLeft: theme.spacing.md,
          }}
        >
          {item.dataViewUrlName ? `${item.dataViewUrlName}` : `${item.url}`}
        </span>
      </a>
    );
  });

  return (
    <>
      <Box
        onClick={toggle}
        css={{
          width: "100%",
          display: "flex",
          alignItems: "center",
          cursor: "pointer",
          fontWeight: 600,
          backgroundColor: theme.colors.gray[1],
          height: 40,
          borderBottom: `1px solid ${theme.colors.gray[4]}`,
          borderTop: `1px solid ${theme.colors.gray[4]}`,
        }}
      >
        <Button
          variant="transparent"
          pl="md"
          // color={theme.colors[theme.primaryColor][8]}
          color="dark"
        >
          {group.group}
        </Button>
        <div
          css={{
            marginLeft: "auto",
            display: "flex",
          }}
        >
          {opened ? (
            <MdKeyboardArrowUp size={"20"} />
          ) : (
            <MdKeyboardArrowDown size={"20"} />
          )}
        </div>
      </Box>

      <Collapse in={opened}>{links}</Collapse>
    </>
  );
};

function NavbarSimple(props) {
  const { data } = useOpenApi();
  const [searchQuery, setSearchQuery] = useState<string>();
  const { data: profile } = useProfile();
  const filteredGroups = useMemo(
    () =>
      Object.values(data.groupedOperations).map((x) => ({
        ...x,
        operations: x.operations.filter(
          searchQuery
            ? (x) =>
                x.dataViewUrlName?.toLowerCase().includes(searchQuery) ||
                x.url.toLowerCase().includes(searchQuery)
            : () => true
        ),
      })),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [searchQuery]
  );

  const userDatasets = profile?.partner?.subscriptionPackageAssignment
    .flatMap((spa) => {
      return spa.subscriptionPackage.subscriptions.map((s) => {
        return s.subscription;
      });
    })
    .map((s) => s.dataSetName);

  const publicDatasets = filteredGroups.flatMap((g) => {
    return g.operations
      .filter((o) => o.hasPublicSubscription)
      .map((o) => o.name)
      .filter((name) => name !== undefined);
  });

  const accessableDatasets = userDatasets
    ? [...userDatasets, ...publicDatasets]
    : publicDatasets;

  const links = filteredGroups.map((group) => {
    const operations = group.operations.filter((op) => {
      if (isAdminType(profile?.type)) {
        return true;
      }
      return accessableDatasets.includes(op.name!);
    });
    if (operations.length === 0) {
      return null;
    }

    return (
      <OperationGroup
        key={group.group}
        group={{
          ...group,
          operations,
        }}
      />
    );
  });

  return (
    <nav style={props.style} className={props.className}>
      <TextInput
        variant="default"
        onChange={(x) =>
          setSearchQuery(
            x.target.value.length == 0 ? undefined : x.target.value
          )
        }
        leftSectionPointerEvents="none"
        leftSection={<FiSearch />}
        placeholder="Search"
        m="xs"
        size="sm"
      />

      <ScrollArea
        type="auto"
        offsetScrollbars
        css={{
          height: "calc(100% - 46px);",
          overflow: "auto",
        }}
      >
        {links}
      </ScrollArea>
    </nav>
  );
}

export default function Layout({ children }) {
  const theme = useMantineTheme();
  return (
    <div
      css={{
        height: "100%",
        position: "relative",
        display: "flex",
      }}
    >
      <NavbarSimple
        style={{
          // height: `calc(100% - ${FULL_HEADER_HEIGHT}px)`,
          top: FULL_HEADER_HEIGHT,
        }}
        css={{
          position: "fixed",
          background: "white",
          left: 0,
          bottom: 0,
          width: 250,
          borderRight: `1px solid ${theme.colors.gray[2]}`,
          zIndex: 100,
        }}
      />
      <div
        css={{
          // height: "100%",
          flexGrow: 1,
          [desktop]: {
            marginLeft: 250,
          },
          display: "flex",
          flexDirection: "column",
        }}
      >
        {children}
      </div>
    </div>
  );
}

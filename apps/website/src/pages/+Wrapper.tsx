import "@fontsource-variable/inter";
import "@mantine/core/styles.layer.css";
import "@mantine/dates/styles.css";
import "@mantine/tiptap/styles.css";
import "@mantine/notifications/styles.css";
import "./global.css";
import "dayjs/locale/hu";
import "mantine-datatable/styles.layer.css";
import { ColorSchemeScript, MantineProvider } from "@mantine/core";
import { Suspense } from "react";
import { theme } from "./theme";
import { TranslationProvider } from "./i18n";
import { ModalsProvider } from "@mantine/modals";
import { Notifications } from "@mantine/notifications";

export function Wrapper({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Suspense fallback="">
        <ColorSchemeScript defaultColorScheme="light" />
        <MantineProvider defaultColorScheme="light" theme={theme}>
          <ModalsProvider>
            <Notifications limit={5} />
            <TranslationProvider>{children}</TranslationProvider>
          </ModalsProvider>
        </MantineProvider>
      </Suspense>
    </>
  );
}

import { useCreateRegistration } from "#root/src/api/auth/auth.query";
import { BlockTitle } from "#root/src/components/BlockTitle";
import { Checkbox } from "#root/src/components/formfields/Checkbox";
import { TextInput } from "#root/src/components/formfields/TextInput";
import { Anchor, Box, Button, Group, Stack, Text } from "@mantine/core";
import { FormProvider, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { navigate } from "vike/client/router";

export const Page = () => {
  const { t } = useTranslation();
  const methods = useCreateRegistration();
  const form = useForm<Parameters<typeof methods.mutate>[0]>({
    defaultValues: {
      enableNotifications: true,
    },
  });

  const onCreate = form.handleSubmit(async (values) => {
    const result = await methods.mutateAsync(values);
    if (result) {
      form.reset();
      navigate("/signup/success");
    }
  });

  return (
    <Box p="md" w="100%" maw="550" mx="auto">
      <BlockTitle
        label={t("Create Your Account")}
        mb="xl"
        css={{
          fontSize: 30,
        }}
      />
      <FormProvider {...form}>
        <form onSubmit={onCreate}>
          <Stack>
            <Group
              w="100%"
              css={{
                ">*": {
                  flexGrow: 1,
                },
              }}
            >
              <TextInput
                {...form.register("firstName")}
                label="First Name"
                required
              />
              <TextInput
                {...form.register("lastName")}
                label="Last Name"
                required
              />
            </Group>
            <TextInput
              {...form.register("email")}
              label="Company Email"
              type="email"
              required
            />
            <Group
              w="100%"
              css={{
                ">*": {
                  flexGrow: 1,
                },
              }}
            >
              <TextInput
                {...form.register("companyName")}
                label="Company Name"
                required
              />
              <TextInput {...form.register("jobTitle")} label="Job Title" />
            </Group>
            <TextInput {...form.register("country")} label="Country" />
            <TextInput {...form.register("phoneNumber")} label="Phone Number" />
            <Checkbox
              {...form.register("enableNotifications")}
              label="Receive email notifications"
            />
            <Button mt="md" type="submit">
              {t("Sign up")}
            </Button>
          </Stack>
        </form>
      </FormProvider>
      <Text mt="xs" fw="400" fz="14" c="#595959">
        By creating an account, you agree to{" "}
        <Anchor
          href="/about_us"
          target="_blank"
          size="sm"
          c="gray"
          underline="always"
        >
          Terms and Conditions.
        </Anchor>
      </Text>
      <Text mt="xl" fz="14">
        Already have an account?{" "}
        <Anchor href="/signin" size="sm" underline="always">
          Sign in
        </Anchor>
      </Text>
    </Box>
  );
};

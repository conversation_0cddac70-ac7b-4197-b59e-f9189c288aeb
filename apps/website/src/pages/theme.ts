// theme.ts
import "./theme.css";
import { Badge, Card, createTheme, Paper } from "@mantine/core";
// Do not forget to pass theme to MantineProvider
export const theme = createTheme({
  primaryColor: "brand",
  black: "#171B1C",
  white: "#FFFFFF",
  fontFamily: `'Inter Variable', sans-serif`,
  defaultRadius: "md",
  spacing: {
    "2xl": "3rem",
  },
  // autoContrast: true,
  cursorType: "pointer",
  components: {
    Paper: Paper.extend({
      defaultProps: {
        withBorder: true,
      },
    }),
    Card: Card.extend({
      defaultProps: {
        withBorder: true,
      },
    }),
    CardSection: Card.Section.extend({
      defaultProps: {
        withBorder: true,
        inheritPadding: true,
        p: "md",
      },
    }),
    Badge: Badge.extend({
      defaultProps: {
        variant: "light",
        radius: "sm",
      },
    }),
  },
  colors: {
    // error: "#EDFAFD",
    // app: {
    // },

    brand: [
      "#EDFAFD",
      "#DEF2F7",
      "#A4E1F1",
      "#71CEEA",
      "#19AFD7",
      "#0098BE",
      "#0083A9",
      "#006F92",
      "#005A7B",
      "#004764",
    ],
    neutral: [
      "#F5F5F5",
      "#E6E6E6",
      "#D6D6D6",
      "#959595",
      "#595959",
      "#e0e0e0",
      "#c0c0c0",
      "#a0a0a0",
      "#808080",
      "black",
    ],
  },
});

export const desktop = "@media (min-width: 900px)";
export const mobile = "@media (max-width: 499px)";
export const dark = '[data-mantine-color-scheme="dark"] &';
export const light = '[data-mantine-color-scheme="light"] &';

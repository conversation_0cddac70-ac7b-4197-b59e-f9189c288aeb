import { create, type StateCreator } from "zustand";

import type { AnnouncementsFilter } from "../api/announcements/announcements.telefunc";
import { PartnerStatus } from "#root/src/shared/constants.ts";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

type AnnouncementsState = {
  filter: AnnouncementsFilter;
  setFilter: (filter: AnnouncementsFilter) => void;
  clearFilter: () => void;
};

type PartnerFilter = {
  name: string;
  setName: (name: string) => void;
  status: PartnerStatus[];
  setStatus: (status: PartnerStatus[]) => void;
};

type Store = {
  partners: {
    selection: { id: number }[];
    setSelection: (selection: { id: number }[]) => void;
  };
  announcements: AnnouncementsState;
  partnerFilter: PartnerFilter;
};

const middlewares = (
  store: StateCreator<
    Store,
    [["zustand/devtools", never], ["zustand/immer", never]],
    [],
    Store
  >
) => devtools(immer(store));

export const useAppStore = create<Store>()(
  middlewares((set, get) => ({
    announcements: {
      filter: {
        textContent: "",
        dataSources: [],
        dateRange: [null, null],
        type: null,
      },
      clearFilter: () => {
        set((state) => {
          state.announcements.filter = {
            textContent: "",
            dataSources: [],
            dateRange: [null, null],
            type: null,
          };
        });
      },
      setFilter: (filter) => {
        set((state) => {
          state.announcements.filter = filter;
        });
      },
    },
    partners: {
      selection: [],
      setSelection: (selection) => {
        set((state) => {
          state.partners.selection = selection;
        });
      },
    },
    partnerFilter: {
      name: "",
      setName: (name) =>
        set((state) => {
          state.partnerFilter.name = name;
        }),
      status: [PartnerStatus.ACTIVE, PartnerStatus.PENDING],
      setStatus: (status: PartnerStatus[]) =>
        set((state) => {
          state.partnerFilter.status = status;
        }),
    },
  }))
);

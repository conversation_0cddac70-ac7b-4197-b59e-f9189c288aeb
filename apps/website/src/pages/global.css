body {
  font-family: "Inter Variable", sans-serif;
  background-color: #eaebeb;
  color: var(--mantine-color-gray-9) !important;
  @mixin dark {
    background-color: var(--mantine-color-dark-3) !important;
    color: var(--mantine-color-dark-0) !important;
  }
}

#root {
  /* FULL_HEADER_HEIGHT */
  height: calc(100dvh - 120px);
}

.mantine-Modal-header {
  padding: var(--mantine-spacing-lg);
}

.mantine-Modal-body {
  padding: var(--mantine-spacing-lg);
  padding-top: 0;
}
.mantine-Button-label {
  white-space: normal;
}

button {
  min-width: fit-content;
  /* flex-grow: 1; */
}

#tv-attr-logo {
  display: none !important;
}

import { getEmbedding } from "#root/src/api/embeddings/embeddings.telefunc";
import { render } from "vike/abort";

/**
 * Guard function to check embedding permissions and set appropriate headers
 * This protects iframes from being embedded on unauthorized sites
 */
export async function guard(ctx) {
  const res = ctx.res;
  const embedId = ctx.routeParams?.["embeddingId"];

  if (!embedId) {
    throw render(401, "Missing embedding ID");
  }

  // Get embedding data
  const embedding = await getEmbedding(embedId);
  if (!embedding) {
    throw render(404, "Embedding not found");
  }

  // Process allowed origins
  const allowedOrigins = embedding.EmbeddingsAllowedOrigin || [];

  // Only use enabled origins
  const enabledOrigins = allowedOrigins
    .filter((origin) => origin.enabled)
    .map((origin) => normalizeDomain(origin.origin));

  // Set security headers based on allowed origins
  setFrameHeaders(res, enabledOrigins);
}

/**
 * Normalize domain format to handle various inputs
 */
function normalizeDomain(site: string): string {
  if (!site || site === "*") {
    return "*";
  }

  // Remove protocol if present
  let domain = site;
  if (domain.includes("://")) {
    domain = domain.split("://")[1];
  }

  // Remove path or query parameters if present
  if (domain.includes("/")) {
    domain = domain.split("/")[0];
  }

  return domain;
}

/**
 * Set security headers to control iframe embedding
 */
function setFrameHeaders(res: any, allowedDomains: string[]): void {
  // Case 1: Wildcard is included - most permissive
  if (allowedDomains.includes("*") || import.meta.env.DEV) {
    // When '*' is specified, include all common schemes explicitly
    res.setHeader(
      "Content-Security-Policy",
      "frame-ancestors 'self' https: http: file: data: * chrome-extension: moz-extension:"
    );
    return;
  }

  // Case 2: No domains allowed - most restrictive
  if (allowedDomains.length === 0) {
    res.setHeader("Content-Security-Policy", "frame-ancestors 'none'");
    res.setHeader("X-Frame-Options", "DENY");
    return;
  }

  // Case 3: Specific domains allowed
  const cspSources = allowedDomains
    .map((domain) => convertToCspFormat(domain))
    .filter(Boolean)
    .join(" ");
  res.setHeader(
    "Content-Security-Policy",
    `frame-ancestors 'self' ${cspSources}`
  );
  res.setHeader("X-Frame-Options", "SAMEORIGIN");
}

/**
 * Convert domain to CSP format with explicit protocols
 */
function convertToCspFormat(domain: string): string {
  // Handle wildcard subdomains
  if (domain.startsWith("*.")) {
    const baseDomain = domain.substring(2);
    return `https://${baseDomain} https://*.${baseDomain} http://${baseDomain} http://*.${baseDomain}`;
  }

  // Regular domain with explicit protocols
  return `https://${domain} http://${domain}`;
}

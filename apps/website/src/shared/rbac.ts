import { UserType } from "#root/src/shared/constants.ts";

type RWPerm = {
  R: [Scope, 0 | 1 | 2];
  W: [Scope, 0 | 1 | 2];
  RW: [Scope, 0 | 1 | 2];
};
export const scope = (permission: Scope): RWPerm => ({
  R: [permission, 0] as const,
  W: [permission, 1] as const,
  RW: [permission, 2] as const,
});

export const SCOPES = [
  "users",
  "partners",
  "partner",
  "auditLog",
  "dataRequests",
  "subscriptionPackageOrder",
  "subscriptionPackageUpdate",
  "subscriptionPackages",
  "changelog",
] as const;

export type Scope = (typeof SCOPES)[number];

const inherit =
  (from: UserType): (() => RawScopeDefProvider[]) =>
  () =>
    ROLE_MATRIX[from]();

export type RawScopeDefinition = [Scope, 0 | 1 | 2];
type RawScopeDefProvider = () => RawScopeDefinition;
type ScopeLike = RawScopeDefinition | RawScopeDefProvider;

const define =
  (
    ...input: (Array<ScopeLike> | (() => RawScopeDefProvider[]))[]
  ): (() => RawScopeDefProvider[]) =>
  () =>
    input
      .map((x) => (typeof x == "function" ? x() : x))
      .flatMap((scopes) =>
        scopes.map((x) => (typeof x == "function" ? x : () => x))
      );

export const hasScope = (userType: UserType, scope: RawScopeDefinition) =>
  scope[1] === 2
    ? hasScope(userType, [scope[0], 1]) && hasScope(userType, [scope[0], 0])
    : ROLE_MATRIX[userType]()
        .flat()
        .some(
          (scopeCandidate) =>
            scopeCandidate()[0] === scope[0] &&
            (scopeCandidate()[1] === scope[1] || scopeCandidate()[1] === 2)
        );

export const ROLE_MATRIX: Record<UserType, () => RawScopeDefProvider[]> = {
  USER: define([
    scope("partner").R,
    scope("partners").R,
    scope("subscriptionPackageOrder").RW,
  ]),
  PARTNER_ADMIN: define(inherit("USER"), [
    scope("partner").W,
    scope("subscriptionPackageUpdate").RW,
  ]),
  ADMIN: define([
    scope("partner").RW,
    scope("users").RW,
    scope("partners").RW,
    scope("dataRequests").RW,
    scope("auditLog").RW,
    scope("subscriptionPackages").RW,
    scope("changelog").R,
  ]),
  SUPER_ADMIN: define(inherit("ADMIN"), [scope("changelog").RW]),
};

const hierarchy = [UserType.USER, UserType.PARTNER_ADMIN, UserType.ADMIN, UserType.SUPER_ADMIN]

export const getTypesInHierarchicalOrder = (max: UserType) => hierarchy.slice(0, hierarchy.indexOf(max) + 1)
export const argon2Opts = {
  memory: 3145728,
  iterations: 2,
  parallelism: 64,
  salt_length: 16,
  key_length: 32,
};

export const passwordRegex =
  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d\w\W]{8,}$/;

export const PartnerStatus = {
  ACTIVE: "ACTIVE",
  PENDING: "PENDING",
  NONE: "NONE",
} as const;
export type PartnerStatus = keyof typeof PartnerStatus;

export const partnerStatusOptions = Object.keys(PartnerStatus).map((key) => ({
  value: key,
  label: key[0] + key.slice(1).toLowerCase(),
}));

export const UserStatus = {
  DELETED: "DELETED",
  ACTIVE: "ACTIVE",
  SUSPENDED: "SUSPENDED",
  PENDING: "PENDING",
} as const;
export type UserStatus = keyof typeof UserStatus;

export const UserType = {
  ADMIN: "ADMIN",
  SUPER_ADMIN: "SUPER_ADMIN",
  USER: "USER",
  PARTNER_ADMIN: "PARTNER_ADMIN",
} as const;
export type UserType = keyof typeof UserType;

export enum AuditLogEventType {
  PARTNER_SUB_ASSIGN = "partner.subscription.assign",
  PARTNER_SUB_UNASSIGN = "partner.subscription.unassign",
  USER_LEAD_CREATE = "user.lead.create",
  USER_LEAD_DELETE = "user.lead.delete",
  USER_CONTRACT_CREATE = "user.contract.create",
  USER_DATA_UPDATE = "user.data.update",
  PARTNER_CREATE = "partner.create",
  SUB_PACKAGE_CREATE = "subscription.package.create",
  SUB_PACKAGE_DELETE = "subscription.package.delete",
}

export const AnnouncementType = {
  RELEASE_NOTE: "RELEASE_NOTE",
  FIX: "FIX",
  GENERAL_NOTICE: "GENERAL_NOTICE",
  MAINTENANCE: "MAINTENANCE",
} as const;
export type AnnouncementType =
  (typeof AnnouncementType)[keyof typeof AnnouncementType];

export const AnnouncementTypeLabel = [
  { label: "Release Note", value: AnnouncementType.RELEASE_NOTE },
  { label: "Fix", value: AnnouncementType.FIX },
  { label: "Notice", value: AnnouncementType.GENERAL_NOTICE },
  { label: "Maintenance", value: AnnouncementType.MAINTENANCE },
];

export const ContractStatus = {
  REJECTED: "REJECTED",
  ACCEPTED: "ACCEPTED",
  PENDING: "PENDING",
} as const;
export type ContractStatus = keyof typeof ContractStatus;

export const ContractType = {
  NEW_ORDER: "NEW_ORDER",
  UPDATE: "UPDATE",
} as const;
export type ContractType = keyof typeof ContractType;

export const FormType = {
  REGISTRATION: "Registration",
  ORDER: "Order",
  UPDATE: "Update",
  CANCEL: "Cancel",
  ACTIVATE_CONTRACT: "Activate",
  REJECT_CONTRACT: "Reject",
  USER_ADD: "Add",
  USER_REMOVE: "Remove",
} as const;
export type FormType = keyof typeof FormType;

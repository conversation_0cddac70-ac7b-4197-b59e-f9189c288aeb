import { CC_TO, MAIL_FROM, PUBLIC_URL } from "#root/src/server/config";
import { prisma } from "#root/src/server/db/prisma";
import type { User } from "@prisma/client";
import type { KyResponse } from "ky";
import ky from "ky";
import { format } from "prettier";
import { EMAIL_ATTACHMENTS } from "./db/email/createSharedEmailTemplate";

//@ts-expect-error: for some reason 0 is ok here instead of "0"
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;


interface PasswordReset {
  token: string;
}

function renderEmailTemplate(
  email: string,
  variables: {
    user?: User;
    passwordReset?: PasswordReset;
    name?: string;
    htmlContent?: string;
  }
) {
  const mapping: { [key: string]: string | number | undefined } = {
    userId: variables.user?.id,
    userEmail: variables.user?.email,
    userFirstName: variables.user?.firstName,
    passwordResetLink: `${PUBLIC_URL}/signin?token=${variables.passwordReset?.token}`,
    name: variables.name,
    htmlContent: variables.htmlContent,
  };

  for (const [key, value] of Object.entries(mapping)) {
    email = email.replace(
      new RegExp(`{{${key}}}`, "g"),
      value !== undefined ? String(value) : ""
    );
  }

  return format(email, { parser: "html" });
}

async function sendMail({
  to,
  subject,
  html,
  cc = [],
  batch = false,
}: {
  to: string[];
  subject: string;
  html: string;
  cc?: string[];
  batch?: boolean;
}) {
  console.log("Sending mail", {
    to,
    cc,
    subject,
  });
  const toAddresses = to.map((t) => t.replace(/\+.*@/, "@").toLowerCase());
  const ccAddresses = cc.map((a) => a.replace(/\+.*@/, "@").toLowerCase());
  let mailApiUrl = "https://api.zeptomail.eu/v1.1/email";

  if (batch) {
    mailApiUrl = mailApiUrl + "/batch";
  }
  try {
    const jsonPayload: any = {
      from: {
        address: MAIL_FROM,
        name: "Hupx Data",
      },
      to: toAddresses.map((rec) => ({
        email_address: {
          address: rec,
        },
      })),
      subject: subject,
      htmlbody: html,
      inline_images: EMAIL_ATTACHMENTS,
    };

    // Only add cc addresses if not in batch mode
    if (!batch && ccAddresses.length > 0) {
      jsonPayload.cc = ccAddresses.map((rec) => ({
        email_address: {
          address: rec,
        },
      }));
    }

    await ky.post(mailApiUrl, {
      headers: {
        Accept: "application/json",
        Authorization: `Zoho-enczapikey ${process.env.ZEPTOMAIL_API_KEY}`,
      },
      json: jsonPayload,
    });
  } catch (error: any) {
    console.error(
      "Failed to send email:",
      await (error.response as KyResponse).text()
    );
    throw error;
  }
}

async function sendPasswordResetEmail(
  user: User,
  passwordReset: PasswordReset
) {
  const template = await prisma.emailTemplate.findFirstOrThrow({
    where: {
      id: "password_reset",
    },
  });

  const emailContent = renderEmailTemplate(template.content, {
    user,
    passwordReset,
  });

  await sendMail({
    to: [user.email],
    subject: template.subject,
    html: emailContent,
  });
}

async function sendAnnouncementEmail(
  htmlContent: string,
  to: string[],
  title: string
) {
  const template = await prisma.emailTemplate.findFirstOrThrow({
    where: {
      id: "announcement",
    },
  });
  const emailContent = renderEmailTemplate(template.content, {
    htmlContent,
  });
  await sendMail({
    to,
    subject: `HUPX Labs - ${title}`,
    html: emailContent,
    batch: true,
  });
}

async function sendSignupEmail(user: User, passwordReset: PasswordReset) {
  const template = await prisma.emailTemplate.findFirstOrThrow({
    where: {
      id: "signup",
    },
  });

  const emailContent = renderEmailTemplate(template.content, {
    user,
    passwordReset,
  });

  await sendMail({
    to: [user.email],
    subject: template.subject,
    html: emailContent,
  });
}

async function sendDataRequestSuccessfulEmail(name: string, email: string) {
  const template = await prisma.emailTemplate.findFirstOrThrow({
    where: {
      id: "data_request_success",
    },
  });

  const emailContent = renderEmailTemplate(template.content, {
    name,
  });

  const isProd = process.env.NODE_ENV === "production";
  const cc = isProd ? [CC_TO] : [];

  await sendMail({
    to: [email],
    subject: template.subject,
    html: emailContent,
    cc,
  });
}

export const emailService = {
  sendPasswordResetEmail,
  sendSignupEmail,
  sendDataRequestSuccessfulEmail,
  sendAnnouncementEmail,
};

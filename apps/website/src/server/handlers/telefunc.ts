import type { Request, Response } from "express";
import { telefunc, config } from "telefunc";

config.disableNamingConvention = true;
// config.shield = { dev: true };

/**
 * Helper function to extract error messages from various error types
 */
export const getErrorMessage = async (err: unknown): Promise<string> => {
  if (err instanceof Error) {
    return err.message;
  }
  return "Unknown error";
};

/**
 * Express middleware handler for telefunc requests
 */
export const handleTelefuncRequest = (
  req: Request,
  res: Response,
  next: Function
): void => {
  telefunc({
    url: req.originalUrl,
    method: req.method,
    body: req.body,
  })
    .then((httpResponse) => {
      const { body, statusCode, contentType, err } = httpResponse;

      if (err) {
        res
          .status(200)
          .type("text/plain")
          .send({
            ret: { error: getErrorMessage(err) },
          });
        return;
      }

      res.status(statusCode).type(contentType).send(body);
    })
    .catch((error) => {
      console.error("Error in telefunc handler:", error);
      res.status(500).send({ error: "Internal Server Error" });
    });
};

import { createProxyMiddleware } from "http-proxy-middleware";
import type { Request, Response } from "express";
import ky from "ky";
import { stringify } from "devalue";
import { INTERNAL_API_KEY, INTERNAL_API_URL } from "../config";
import { getSubsciptionsFromCurrentUserOrEmbeddingRequest } from "../../api/subscriptions/subscriptions.service";
import { parseDef } from "../../openapi/openapi";

/**
 * Proxy middleware for forwarding data requests to the internal API
 */
export const createApiProxy = () => {
  return createProxyMiddleware({
    target: INTERNAL_API_URL,
    pathRewrite: async (path, req) => {
      const subscriptions =
        await getSubsciptionsFromCurrentUserOrEmbeddingRequest();
      req.headers["x-subscriptions"] = JSON.stringify(
        subscriptions.map((s) => s.subscription)
      );
      req.headers["x-internal-api-key"] = INTERNAL_API_KEY;
      return path.replace("/data", "");
    },
  });
};

/**
 * Handler for OpenAPI schema requests
 */
export const handleOpenApiRequest = async (
  _: Request,
  res: Response
): Promise<void> => {
  try {
    const r = await ky
      .get(INTERNAL_API_URL + "/openapi.json", {
        headers: {
          "x-internal-api-key": INTERNAL_API_KEY,
        },
      })
      .json();
    const parsed = await parseDef(r);
    res.send(stringify(parsed));
  } catch (error) {
    console.error("Error fetching OpenAPI schema:", error);
    res.status(500).send({ error: "Failed to fetch OpenAPI schema" });
  }
};

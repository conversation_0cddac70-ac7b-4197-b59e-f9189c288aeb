export type { WsUpdateData, WsAnnouncementPayload };
export { emitter, handleHupxUpdateWebSocket };

import type { WebSocket } from "ws";
import {
  authService,
  setUserLastOnline,
} from "#root/src/server/auth/auth-service";
import EventEmitter from "node:events";

type WsUpdateData =
  | {
      type: "announcement";
      payload: WsAnnouncementPayload;
    }
  | {
      type: "handshake";
    } | {
    type: "quarterlyCleanup",
}

type WsAnnouncementPayload = {
  announcementDsLink: { dataset: string }[];
  title: string;
};

// need a stable export for hmr
const emitter =
  globalThis.__hupx_update_emitter ||
  (globalThis.__hupx_update_emitter = new EventEmitter());

const handleHupxUpdateWebSocket = (ws: WebSocket): void => {
  const user = authService.getCurrentUser();
  if (!user) {
    ws.close(1008, "Unauthorized");
    return;
  }
  setUserLastOnline(user.id);
  ws.send(JSON.stringify({ type: "handshake" } satisfies WsUpdateData));
  const handleUpdate = (data: WsUpdateData) => {
    ws.send(JSON.stringify(data));
  };

  emitter.addListener("message", handleUpdate);
  ws.on("close", (c, r) => {
    emitter.removeListener("message", handleUpdate);
    console.log("Socket closed", c, r.toString());
    setUserLastOnline(user.id);
  });
};

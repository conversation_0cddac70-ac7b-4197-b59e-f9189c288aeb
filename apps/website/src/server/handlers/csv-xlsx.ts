import { getSubscriptionsForCurrentUser } from "#root/src/api/subscriptions/subscriptions.service";
import ky from "ky";
import { INTERNAL_API_KEY, INTERNAL_API_URL } from "../config";
import { Readable } from "node:stream";

export const handleFileDownload = async (
  fileType: "csv" | "xlsx",
  req: any,
  res: any
) => {
  const subscriptions = await getSubscriptionsForCurrentUser();

  // Process URL based on file type
  let targetUrl: string;
  if (fileType === "csv") {
    targetUrl = INTERNAL_API_URL + req.url.substring(4);
  } else {
    // xlsx
    targetUrl = INTERNAL_API_URL + req.url.substring(5).replace("/xlsx/", "/");
  }

  const response = await ky.get(targetUrl, {
    headers: {
      "x-subscriptions": JSON.stringify(subscriptions),
      "x-internal-api-key": INTERNAL_API_KEY,
    },
  });

  // Forward headers
  for (const [name, value] of response.headers) {
    res.header(name, value);
  }

  // Stream response
  Readable.fromWeb(response.body as any).pipe(res);
};

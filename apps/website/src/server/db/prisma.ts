import { patchPrismaTx } from "@myfunc/prisma-transactional";
import { PrismaClient } from "@prisma/client";
import type { WsUpdateData } from "../handlers/ws";
import { emitter } from "../handlers/ws";
import { assert } from "#root/src/shared/assert";
import { authService } from "#root/src/server/auth/auth-service.ts";
import { createEventOfType } from "#root/src/server/event.ts";
import { AuditLogEventType } from "#root/src/shared/constants.ts";

export const prisma = patchPrismaTx(
  //@ts-expect-error: type is not ready yet
  new PrismaClient().$extends({
    query: {
      subscriptionPackage: {
        create({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(AuditLogEventType.SUB_PACKAGE_CREATE, args, user);
            return e;
          });
        },
        delete({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(AuditLogEventType.SUB_PACKAGE_DELETE, args, user);
            return e;
          });
        },
      },
      subscriptionPackageAssignment: {
        create({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(AuditLogEventType.PARTNER_SUB_ASSIGN, args, user);
            return e;
          });
        },
      },
      user: {
        create({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(
              AuditLogEventType.USER_CONTRACT_CREATE,
              { ...args.data, password: "HIDDEN" },
              user
            );
            return e;
          });
        },
        createMany({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          const entries = Array.isArray(args.data) ? args.data : [args.data];
          return query(args).then((e) => {
            entries.forEach((u) =>
              createEventOfType(
                AuditLogEventType.USER_CONTRACT_CREATE,
                { ...u, password: "HIDDEN" },
                user
              )
            );
            return e;
          });
        },
        update({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(AuditLogEventType.USER_DATA_UPDATE, args, user);
            return e;
          });
        },
      },
      partner: {
        create({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(
              AuditLogEventType.PARTNER_CREATE,
              args.data,
              user
            );
            return e;
          });
        },
        createMany({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          const entries = Array.isArray(args.data) ? args.data : [args.data];
          return query(args).then((e) => {
            entries.forEach((u) =>
              createEventOfType(
                AuditLogEventType.PARTNER_CREATE,
                { ...u, password: "HIDDEN" },
                user
              )
            );
            return e;
          });
        },
      },
      registration: {
        create({ args, query }) {
          return query(args).then((e) => {
            // TODO: throws errors, should be fixed before reintroduction
            // createEventOfType(AuditLogEventType.USER_LEAD_CREATE, args.data, -1);
            return e;
          });
        },
        delete({ args, query }) {
          const user = authService.getCurrentUser()?.id;
          return query(args).then((e) => {
            createEventOfType(
              AuditLogEventType.USER_LEAD_DELETE,
              args.where,
              user
            );
            return e;
          });
        },
      },
      announcement: {
        async create({ args, query }) {
          args.include ??= {};
          args.include.announcementDsLink = true;
          const result = await query(args);
          assert(result.announcementDsLink);
          emitter.emit("message", {
            type: "announcement",
            payload: {
              announcementDsLink: result.announcementDsLink.map((x) => {
                return {
                  dataset: x.dataset || "",
                };
              }),
              title: result.title || "",
            },
          } satisfies WsUpdateData);
          return result;
        },
      },
    },
  })
);

import {
  argon2Opts,
  PartnerStatus,
  UserStatus,
  UserType,
} from "../../shared/constants";
import { hash } from "@node-rs/argon2";
import { createSharedEmailTemplate } from "./email/createSharedEmailTemplate";
import { prisma } from "./prisma";

export { seedDb };

async function seedDb() {
  const users = await prisma.user.findMany();

  const sharedTemplate = createSharedEmailTemplate();

  const dataRequestSuccessContent = sharedTemplate.replace(
    "{{htmlContent}}",
    `Dear {{name}},
    <br>
    <p>Thank you for your data request!</p>
    <p>Our colleagues will come back to you shortly.</p>
    <p>Kind regards,<br/>HUPX Labs Team</p>`
  );

  const signupContent = sharedTemplate.replace(
    "{{htmlContent}}",
    `Dear {{userFirstName}},
    <br><br>
    <p>Thank you for signing up to the HUPX Labs platform. Please note that the platform is still in its test phase.</p>

    <p>To set up your password, please click on the following button:</p>

    <p style="text-align: center; margin: 25px 0;">
      <a href="{{passwordResetLink}}" style="display: inline-block; background-color: #52808e; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 2px; font-weight: normal; min-width: 200px; text-align: center;">Set Your Password</a>
    </p>

    <p style="font-size: 12px; color: #666666; text-align: center;">
      If the button above doesn't work, copy and paste this URL into your browser:<br>
      <a href="{{passwordResetLink}}" style="color: #52808e; word-break: break-all;">{{passwordResetLink}}</a>
    </p>

    <p>If you have any question, we kindly ask you to fill in the data request form on the platform or contact the HUPX Labs team via <a href="mailto:<EMAIL>" style="color: #52808e; text-decoration: none;"><EMAIL></a>.</p>

    <p>Best regards,<br>HUPX Labs Team</p>`
  );

  const passwordResetContent = sharedTemplate.replace(
    "{{htmlContent}}",
    `Dear {{userFirstName}},
    <br><br>
    <p>Please click the button below to set a new password:</p>

    <p style="text-align: center; margin: 25px 0;">
      <a href="{{passwordResetLink}}" style="display: inline-block; background-color: #52808e; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 2px; font-weight: normal; min-width: 200px; text-align: center;">Reset Password</a>
    </p>

    <p style="font-size: 12px; color: #666666; text-align: center;">
      If the button above doesn't work, copy and paste this URL into your browser:<br>
      <a href="{{passwordResetLink}}" style="color: #52808e; word-break: break-all;">{{passwordResetLink}}</a>
    </p>

    <p>If you did not initiate this password reset request, please disregard this email. <b>For security reasons, we kindly ask you to contact the HUPX Labs team at <a href="mailto:<EMAIL>" style="color: #52808e; text-decoration: none;"><EMAIL></a>.</b></p>

    <p>Best regards,<br>HUPX Labs Team</p>`
  );

  const announcementContent = sharedTemplate;

  await prisma.emailTemplate.upsert({
    where: { id: "data_request_success" },
    create: {
      id: "data_request_success",
      name: "HUPX Labs - Successful Data Request",
      subject: "HUPX Labs - Successful Data Request",
      content: dataRequestSuccessContent,
    },
    update: {
      content: dataRequestSuccessContent,
    },
  });

  await prisma.emailTemplate.upsert({
    where: { id: "password_reset" },
    create: {
      id: "password_reset",
      name: "HUPX Labs - Password Reset",
      subject: "HUPX Labs - Password Reset",
      content: passwordResetContent,
    },
    update: {
      content: passwordResetContent,
    },
  });

  await prisma.emailTemplate.upsert({
    where: { id: "signup" },
    create: {
      id: "signup",
      name: "HUPX Labs - Sign up",
      subject: "HUPX Labs - Sign up",
      content: signupContent,
    },
    update: {
      content: signupContent,
    },
  });

  await prisma.emailTemplate.upsert({
    where: { id: "announcement" },
    create: {
      id: "announcement",
      name: "HUPX Labs - New Announcement",
      subject: "HUPX Labs - New Announcement",
      content: announcementContent,
    },
    update: {
      content: announcementContent,
    },
  });

  if (users.length > 0) {
    return;
  }

  await prisma.partner.create({
    data: {
      name: "Default Public Partner",
      status: "ACTIVE",
    },
  });

  await prisma.partner.create({
    data: {
      name: "HUPX/HUDEX",
      status: "PENDING",
    },
  });

  if (process.env.NODE_ENV === "development") {
    const testPartner1 = await prisma.partner.create({
      data: {
        name: "Teszt Partner 1",
        status: PartnerStatus.NONE,
      },
    });
    const testPartner2 = await prisma.partner.create({
      data: {
        name: "Teszt Partner 2",
        status: PartnerStatus.NONE,
      },
    });
    const testPartner3 = await prisma.partner.create({
      data: {
        name: "Teszt Partner 3",
        status: PartnerStatus.NONE,
      },
    });

    await prisma.user.createMany({
      data: [
        {
          email: "<EMAIL>",
          phoneNumber: "+36209103628",
          firstName: "Éva",
          lastName: "Kovács",
          password: await hash("teszt1234", argon2Opts),
          status: UserStatus.ACTIVE,
          jobTitle: "Teszter",
          lastOnline: new Date(),
          type: UserType.SUPER_ADMIN,
          partnerId: testPartner1.id,
        },
      ],
    });
    await prisma.user.createMany({
      data: [
        {
          email: "<EMAIL>",
          phoneNumber: "+36305058418",
          firstName: "Lilla",
          lastName: "Bányai",
          password: await hash("teszt1234", argon2Opts),
          status: UserStatus.ACTIVE,
          jobTitle: "Teszter",
          lastOnline: new Date(),
          type: UserType.SUPER_ADMIN,
          partnerId: testPartner2.id,
        },
      ],
    });
    await prisma.user.createMany({
      data: [
        {
          email: "<EMAIL>",
          phoneNumber: "+36303419242",
          firstName: "Csilla",
          lastName: "Hajdu",
          password: await hash("teszt1234", argon2Opts),
          status: UserStatus.ACTIVE,
          jobTitle: "Teszter",
          lastOnline: new Date(),
          type: UserType.SUPER_ADMIN,
          partnerId: testPartner3.id,
        },
      ],
    });

    const registration = await prisma.registration.create({
      data: {
        companyName: "Partner 1",
        firstName: "Partner",
        lastName: "Partner",
        email: "<EMAIL>",
        country: "Hungary",
        phoneNumber: "+36 30 765 5607",
        jobTitle: "Bossman",
      },
    });
    await prisma.registration.create({
      data: {
        companyName: "Partner 2",
        firstName: "PartnerKetto",
        lastName: "PartnerKetto",
        email: "<EMAIL>",
        country: "Hungary",
        phoneNumber: "+36 20 987 1965",
        jobTitle: "Bossman",
      },
    });
    const partner1 = await prisma.partner.create({
      data: {
        name: "Partner 1",
        status: "NONE",
      },
    });

    await prisma.user.createMany({
      data: [
        {
          email: "<EMAIL>",
          type: UserType.ADMIN,
          status: UserStatus.ACTIVE,
          firstName: "Admin",
          lastName: "Admin",
          phoneNumber: "123",
          jobTitle: "123",
          lastOnline: new Date(),
          password: await hash("admin", argon2Opts),
        },
        {
          email: "<EMAIL>",
          type: UserType.SUPER_ADMIN,
          status: UserStatus.ACTIVE,
          firstName: "SuperAdmin",
          lastName: "SuperAdmin",
          phoneNumber: "123",
          jobTitle: "123",
          lastOnline: new Date(),
          password: await hash("admin", argon2Opts),
        },
        {
          email: "<EMAIL>",
          type: UserType.USER,
          status: UserStatus.ACTIVE,
          firstName: "User",
          lastName: "User",
          partnerId: partner1.id,
          phoneNumber: "123",
          jobTitle: "123",
          lastOnline: new Date(),
          password: await hash("admin", argon2Opts),
        },

        {
          email: "<EMAIL>",
          type: UserType.PARTNER_ADMIN,
          status: UserStatus.ACTIVE,
          firstName: "padmin",
          lastName: "padmin",
          partnerId: partner1.id,
          phoneNumber: "123",
          jobTitle: "123",
          lastOnline: new Date(),
          password: await hash("admin", argon2Opts),
        },
      ],
    });
    const { id, ...regData } = registration;
    await prisma.mainPartnerContact.create({
      data: {
        ...regData,
        // @ts-ignore
        contractUserId: undefined,
        userId: 3,
        partnerId: partner1.id,
      },
    });

    await prisma.registration.update({
      data: {
        contractUserId: 3,
      },
      where: {
        email: "<EMAIL>",
      },
    });

    await prisma.announcement.createMany({
      data: [
        {
          id: "cm6zkn0a60000mdo6t7r6m2iu",
          type: "GENERAL_NOTICE",
          apiEndpoint: null,
          message: `
            <p>Dear HUPX Members,</p>
            <p>We are writing to inform you about a significant planned maintenance event at one of our major power generation facilities. This temporary adjustment to our power generation capacity will require careful market management to ensure stability.</p>
            <p>Our team is implementing comprehensive measures to maintain market equilibrium and secure alternative power sources during this period. We appreciate your understanding as we work to minimize any potential market impact.</p>
            <p>Best regards,<br/>HUPX Management Team</p>
          `,
          title: "Important: Power Generation Facility Maintenance Notice",
          createdBy: 2,
          createdAt: new Date("2025-01-15T09:00:00.000Z"),
          displayId: "Test 1",
        },
        {
          id: "cm6rg8yit000012rgoi5o7oyh",
          type: "GENERAL_NOTICE",
          apiEndpoint: null,
          message: `
            <p>Dear HUPX Community,</p>
            <p>Following our previous communication regarding the regional power generation facility maintenance, we want to provide you with an updated timeline. The maintenance period will be extended to ensure all necessary safety protocols are thoroughly implemented.</p>
            <p>We continue to maintain robust alternative supply arrangements and are closely monitoring market stability. Regular updates will be provided as the situation progresses.</p>
            <p>Thank you for your continued cooperation.</p>
            <p>Best regards,<br/>HUPX Operations Team</p>
          `,
          title:
            "Update: Extended Maintenance Period for Regional Power Facility",
          createdAt: new Date("2025-02-05T05:09:42.725Z"),
          createdBy: 2,
          displayId: "Test 2",
        },
        {
          id: "cm6rgfcpn000312rgvprhpq2x",
          type: "RELEASE_NOTE",
          apiEndpoint: "DAM_IC_Flows_v1",
          message: `
            <h2>Exciting Platform Enhancement: Quarter-Hourly Renewable Energy Production Reports</h2>
            <p>We are pleased to announce a significant enhancement to our trading platform. Starting tomorrow at midnight, all registered HUPX members will have access to our new Quarter-Hourly Renewable Energy Production reports.</p>
            <h3>Key Benefits</h3>
            <p>This new feature will provide:</p>
            <ul>
              <li>Enhanced visibility into renewable energy production patterns</li>
              <li>Improved trading decision support</li>
              <li>Better integration of sustainable energy sources</li>
            </ul>
            <hr/>
            <p>This development represents our ongoing commitment to supporting Hungary's transition to sustainable energy sources while maintaining market efficiency.</p>
            <p>For detailed documentation and usage guidelines, please visit the HUPX member portal.</p>
          `,
          title:
            "New Feature: Quarter-Hourly Renewable Energy Production Reports",
          createdAt: new Date("2025-02-05T05:14:41.051Z"),
          createdBy: 2,
          displayId: "Test 3",
        },
      ],
      skipDuplicates: true,
    });

    await prisma.announcementDsLink.createMany({
      data: [
        {
          id: "cm6rg8yit000112rgi8ztq74z",
          announcementId: "cm6rg8yit000012rgoi5o7oyh",
          dataset: "DAM_IC_Flows_v1",
        },
        {
          id: "cm6rg8yit000112rgi8ztq75a",
          announcementId: "cm6rg8yit000012rgoi5o7oyh",
          dataset: "WILDCARD",
        },
        {
          id: "cm6rg8yit000212rgsco4tpgo",
          announcementId: "cm6rg8yit000012rgoi5o7oyh",
          dataset: "DAM_Aggregated_Trading_Data_v1",
        },
        {
          id: "cm6rgfcpn000412rgk301thpy",
          announcementId: "cm6rgfcpn000312rgvprhpq2x",
          dataset: "IDA_MC_Flows_v1",
        },
        {
          id: "cm6rgfcpn000512rg0xvgqxqf",
          announcementId: "cm6rgfcpn000312rgvprhpq2x",
          dataset: "IDA_Aggregated_curve_v1",
        },
        {
          id: "cm6rg8yit000112rgi8ztq75b",
          announcementId: "cm6rgfcpn000312rgvprhpq2x",
          dataset: "WILDCARD",
        },
        {
          id: "cm6rg8yit000112rgi8ztq45b",
          announcementId: "cm6zkn0a60000mdo6t7r6m2iu",
          dataset: "WILDCARD",
        },
      ],
      skipDuplicates: true,
    });
  }
  return prisma;
}

import { prisma } from "#root/src/server/db/prisma.ts";
import { <PERSON>ronJob } from "cron";
import { emitter } from "#root/src/server/handlers/ws.ts";

export const runTask = async () => {
  await prisma.subscriptionPackageAssignment.updateMany({
    where: {
      deleteNextCycle: true
    },
    data: {
      deletedAt: new Date()
    }
  });
  emitter.emit("message", { type: "quarterlyCleanup" });
}



export const job = new CronJob("0 0 1 1,4,7,10 *", runTask)

import { SESSION_SECRET } from "#root/src/server/config";
import { emailService } from "#root/src/server/mailer";
import {
  argon2Opts,
  passwordRegex,
  UserStatus,
  UserType,
} from "#root/src/shared/constants";
import { hasScope, type RawScopeDefinition } from "#root/src/shared/rbac.ts";
import { hash, verify } from "@node-rs/argon2";
import type { PrismaClient, User } from "@prisma/client";
import session from "cookie-session";
import { randomBytes } from "crypto";
import dayjs from "dayjs";
import type { Request } from "express";
import type { AsyncReturnType } from "type-fest";
import { getRequestContext } from "../cls";
import { prisma } from "../db/prisma";

export { authService };
export type { CurrentUserType };

type CurrentUserType = AsyncReturnType<(typeof authService)["getUser"]>;

const authService = {
  login,
  getUser,
  getCurrentUser,
  authMiddleware,
  logout,
  getPasswordReset,
  sendPasswordResetEmail,
  updatePassword,
  getDummyPassword,
  createUser,
  createRegistration,
  getPendingRegistrations: getPendingRegistrations,
  deleteRegistration,
};

function logout() {
  const ctx = getRequestContext();
  ctx.req.session = null;
}

async function authMiddleware(req: Request, res, next) {
  session({
    name: "session",
    keys: [SESSION_SECRET!],
    maxAge: 24 * 60 * 60 * 1000,
  })(req, res, async () => {
    if (req.session) {
      const { userId, email } = req.session;
      if (userId || email) {
        const foundUser = await authService.getUser(userId, email);
        if (foundUser) {
          req.user = foundUser;
        } else {
          req.session = null;
        }
      }
    }

    next();
  });
}

function getCurrentUser() {
  const ctx = getRequestContext();
  return ctx?.req?.user;
}

async function getUser(id: number, email: string) {
  if (!id || !email) {
    return;
  }
  const user = await (prisma.user as PrismaClient["user"]).findFirst({
    where: {
      id,
      email,
    },
    include: {
      apiKey: true,
      partner: {
        include: {
          subscriptionPackageAssignment: {
            include: {
              subscriptionPackage: {
                include: {
                  subscriptions: {
                    include: {
                      subscription: true,
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  });

  if (!user) {
    return;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { password, ...rest } = user;
  return rest as typeof rest & {
    type: UserType;
    status: UserStatus;
  };
}

async function login({
  email,
  password,
  newPassword,
  token,
}: {
  email: string;
  password?: string;
  newPassword?: string;
  token?: string | null;
}) {
  const user = await prisma.user.findFirst({
    where: {
      email,
      status: {
        in: [
          UserStatus.ACTIVE,
          UserStatus.PENDING,
        ]
      }
    },
  });
  if (!user) {
    return {
      status: "USER_INACTIVE_ERROR",
    } as const;
  }

  const needsPasswordUpdate = dayjs(user.passwordUpdatedAt).isBefore(
    dayjs().subtract(90, "days")
  );
  if (token && newPassword) {
    const foundToken = await prisma.passwordReset.findFirst({
      where: {
        token,
        email,
        usedAt: null,
      },
    });
    if (!foundToken) {
      return {
        status: "WRONG_CREDENTIALS_ERROR",
      } as const;
    }
  }

  if (
    !(token && newPassword) &&
    (!password || !(await verify(user.password, password, argon2Opts)))
  ) {
    return {
      status: "WRONG_CREDENTIALS_ERROR",
    } as const;
  }

  if (needsPasswordUpdate || token) {
    if (newPassword) {
      if (!passwordRegex.test(newPassword)) {
        return {
          status: "INVALID_PASSWORD_ERROR",
        } as const;
      }
      try {
        if (await verify(user.password, newPassword, argon2Opts)) {
          return {
            status: "SAME_PASSWORD_ERROR",
          } as const;
        }
      } catch (error) {
        if (String(error).includes("password hash string missing field")) {
          // The dummy pasword is just a random string, not compatible with verify
        } else {
          throw error;
        }
      }

      await prisma.user.update({
        where: {
          id: user.id,
        },
        data: {
          password: await hash(newPassword, argon2Opts),
          passwordUpdatedAt: new Date(),
        },
      });
      if (token) {
        await prisma.passwordReset.update({
          where: {
            token,
          },
          data: {
            usedAt: new Date(),
          },
        });
      }
    } else if (needsPasswordUpdate) {
      return {
        status: "PASSWORD_UPDATE_REQUIRED",
      } as const;
    }
  }

  return {
    status: "OK",
    userId: user.id,
  } as const;
}

async function updatePassword({
  email,
  password,
  newPassword,
}: {
  email: string;
  password: string;
  newPassword: string;
}) {
  const user = await prisma.user.findFirst({
    where: { email },
  });
  if (!user) {
    throw new Error("WRONG_CREDENTIALS_ERROR");
  }

  if (!(await verify(user.password, password, argon2Opts))) {
    throw new Error("WRONG_CREDENTIALS_ERROR");
  }
  if (!passwordRegex.test(newPassword)) {
    throw new Error("INVALID_PASSWORD_ERROR");
  }

  if (await verify(user.password, newPassword, argon2Opts)) {
    throw new Error("SAME_PASSWORD_ERROR");
  }

  await prisma.user.update({
    where: {
      id: user.id,
    },
    data: {
      password: await hash(newPassword, argon2Opts),
      passwordUpdatedAt: new Date(),
    },
  });
}

async function createPasswordReset(user: User) {
  const token = randomBytes(32).toString("base64url");
  const passwordReset = await prisma.passwordReset.create({
    data: {
      token,
      email: user.email,
      user: {
        connect: {
          id: user.id,
        },
      },
    },
  });

  return passwordReset;
}

async function getPasswordReset(token: string) {
  const passwordReset = await prisma.passwordReset.findFirst({
    where: {
      token,
      usedAt: null,
    },
    select: {
      id: true,
      email: true,
    },
  });

  return passwordReset;
}

async function sendPasswordResetEmail({ email }: { email: string }) {
  const user = await prisma.user.findFirst({
    where: { email },
  });

  if (!user) {
    throw new Error("User not found");
  }
  const passwordReset = await createPasswordReset(user);
  await emailService.sendPasswordResetEmail(user, passwordReset);
}

async function sendSignupEmail({ email }: { email: string }) {
  const user = await prisma.user.findFirst({
    where: { email },
  });

  if (!user) {
    throw new Error("User not found");
  }
  const passwordReset = await createPasswordReset(user);
  await emailService.sendSignupEmail(user, passwordReset);
}

function getDummyPassword() {
  return randomBytes(32).toString("base64url");
}

export async function getPendingRegistrations() {
  return prisma.registration.findMany({ where: { contractUserId: null } });
}

export async function createRegistration({
  companyName,
  firstName,
  lastName,
  email,
  jobTitle,
  phoneNumber,
  country,
  enableNotifications,
}: {
  companyName: string;
  firstName: string;
  lastName: string;
  email: string;
  jobTitle?: string | null;
  phoneNumber?: string | null;
  country?: string | null;
  enableNotifications: boolean;
}) {
  try {
    const registration = await prisma.registration.create({
      data: {
        companyName,
        firstName,
        lastName,
        email,
        jobTitle,
        phoneNumber,
        country,
        enableNotifications,
      },
    });

    // Create partner and user in one transaction
    const { partner, user } = await prisma.$transaction(async (tx) => {
      const partner = await tx.partner.create({
        data: {
          name: companyName,
          country,
        },
      });

      const user = await tx.user.create({
        data: {
          password: getDummyPassword(),
          lastOnline: new Date(),
          status: UserStatus.PENDING,
          type: UserType.USER,
          email,
          firstName,
          lastName,
          phoneNumber,
          jobTitle,
          enableNotifications,
          partnerId: partner.id,
        },
      });

      await tx.mainPartnerContact.create({
        data: {
          firstName,
          lastName,
          companyName,
          partnerId: partner.id,
          email,
          phoneNumber,
          jobTitle,
          country,
          enableNotifications,
          userId: user.id,
        },
      });

      return { partner, user };
    });

    await sendSignupEmail({ email });

    return { partner, registration, user };
  } catch (error) {
    console.error("Registration error:", error);
    throw error;
  }
}

export async function deleteRegistration({ id }: { id: number }) {
  await prisma.registration.delete({
    where: {
      id,
    },
  });
}

async function createUser({
  firstName,
  lastName,
  email,
  partnerId,
  phoneNumber,
  jobTitle,
  enableNotifications,
}: {
  firstName: string;
  lastName: string;
  email: string;
  partnerId: number;
  phoneNumber: string | null;
  jobTitle: string | null;
  enableNotifications: boolean;
}) {
  const user = await prisma.user.create({
    data: {
      lastOnline: new Date(),
      firstName,
      lastName,
      partnerId,
      email,
      type: UserType.USER,
      status: UserStatus.ACTIVE,
      phoneNumber,
      jobTitle,
      // We send a reset email in the next step.
      password: getDummyPassword(),
      enableNotifications,
    },
  });

  await sendSignupEmail(user);

  return user;
}

export const setUserLastOnline = async (userId: number) =>
  await prisma.user.update({
    data: {
      lastOnline: new Date(),
    },
    where: {
      id: userId,
    },
  });

export const enforce = (scope: RawScopeDefinition) => {
  const user = authService.getCurrentUser();
  if (!user || !hasScope(user.type, scope)) {
    console.log(`REJECTED USER ${user?.email} for ACTION ${scope[0]}`);
    throw new Error("Unauthorized");
  }
  console.log(`AUTHORIZED USER ${user.email} for ACTION ${scope[0]}`);
  return user!;
};

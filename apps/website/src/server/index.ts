import { authService } from "#root/src/server/auth/auth-service";
import { microsoftAuth } from "#root/src/server/auth/oauth";
import cookie from "cookie-parser";
import express from "express";
import { apply } from "vike-server/express";
import { serve } from "vike-server/express/serve";
import { requestContextMiddleware } from "./cls";
import { seedDb } from "./db/db";
import { createApiProxy, handleOpenApiRequest } from "./handlers/api";
import { handleFileDownload } from "./handlers/csv-xlsx";
import { handleTelefuncRequest } from "./handlers/telefunc";
import { handleHupxUpdateWebSocket } from "./handlers/ws";
import expressWs from "express-ws";
import http from "http";
import * as process from "node:process";
import { cronJobs } from "#root/src/server/cron/executor.ts";


// Create API proxy middleware
const proxy = createApiProxy();

bootstrap();

async function bootstrap() {
  await seedDb();
  const { app, getWss } = expressWs(express());
  app.get("/healthcheck", (_, res) => {
    res.sendStatus(200);
  });

  // make request/response available globally by getRequestContext
  app.use(requestContextMiddleware);
  app.use(cookie());
  app.use(authService.authMiddleware);
  // websocket route for updates
  app.ws("/hupx-update", handleHupxUpdateWebSocket);
  // Internal API proxy section
  app.get("/data/**", proxy);
  app.get("/openapi.json", handleOpenApiRequest);
  app.get("/csv/*", (req, res) => handleFileDownload("csv", req, res));
  app.get("/xlsx/*", (req, res) => handleFileDownload("xlsx", req, res));

  // express.text needed for telefunc
  app.use(express.text({ limit: "10mb" }));
  app.use("/_telefunc", handleTelefuncRequest);

  app.get("/auth/link", (req: any, res) =>
    microsoftAuth.initiateLogin(req, res)
  );
  // express.urlencoded needed for oauth post redirect
  app.use(express.urlencoded({ extended: true }));
  app.post("/auth/redirect", (req: any, res) =>
    microsoftAuth.handleCallback(req, res)
  );
  if (process.env.NODE_ENV === "development") {
    for (const [name, cronJob] of Object.entries(cronJobs)) {
      app.get(`/cron/${name}/execute`, async (req, res) => {
        await cronJob.runTask()
        res.send("OK")
      })
    }
  }


  // vike and vite hmr handler
  apply(app);

  const server = http.createServer(app);
  server.on("upgrade", (req, socket, head) => {
    // Prevent our app from trying to handle vite hmr connection..
    // It's alredy handled by "apply(app);"
    if (req.url?.includes("__vite_hmr")) return;
    getWss().handleUpgrade(req, socket, head, (socket) => {
      getWss().emit("connection", socket, req);
    });
  });




  const port = +(process.env.PORT || 3000);
  serve(app, {
    port,
    createServer: () => server,
  });
}

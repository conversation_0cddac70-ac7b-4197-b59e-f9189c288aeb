import { authService } from "#root/src/server/auth/auth-service.ts";
import ky from "ky";
import { getContractInternal } from "../api/contract/contract.service";
import { assert } from "../shared/assert";
import { FormType } from "../shared/constants";

interface CrmConfig {
  registrationEndpoint: string;
  orderEndpoint: string;
  updateEndpoint: string;
  cancelEndpoint: string;
  statusChangeEndpoint: string;
  userAddRemoveEndpoint: string;
  apiKey: string;
  timeout: number;
  retries: number;
}

interface CrmContractStatusChangeData {
  contractId: string;
  formType: string;
  partnerId: number;
}

interface CrmUserAddRemoveData {
  formType: string;
  partnerId: number;
  email: string;
  firstName: string;
  lastName: string;
}

interface CrmRegistrationData {
  email: string;
  firstName: string;
  lastName: string;
  signupForNewsletter: boolean;
  companyName: string;
  phoneNumber?: string;
  country?: string;
  jobTitle?: string;
}

export interface CrmUpdatePartner {
  id: number;
  name?: string;
  country?: string;
}

export interface CrmPackagePayload {
  contractId: string;
  billingIsSameAsCompanyAddress: boolean;
  company: {
    zipCode: string;
    country: string;
    address: string;
    city: string;
    name: string;
    taxNumber: string;
    additionalRepName: string | null;
    authorizedRepName: string;
    commencementDate: string;
    invoiceEmail: string;
    registrationNumber: string;
  };
  billing?: {
    zipCode: string;
    country: string;
    address: string;
    city: string;
    name: string;
  };
  packages: {
    quantity: number;
    price: number;
    id: string;
    name: string;
    historicalData: { name: string }[];
  }[];

  contacts: {
    main: {
      phone?: string;
      firstName?: string;
      lastName?: string;
      position?: string;
      email?: string;
    };
    secondary: {
      phone?: string;
      firstName?: string;
      lastName?: string;
      position?: string;
      email?: string;
    }[];
  };
  affiliates: {
    zipCode: string;
    country: string;
    address: string;
    city: string;
    name: string;
    taxNumber: string;
    registrationNumber: string;
  }[];
}

// Interface for updating a specific subscription package assignment
export interface CrmUpdateSubscriptionPackageAssignment {
  id: string;
  packagePrice?: number;
  numberOfUsers?: number;
  currency?: string;
  validUntil?: Date;
}

function mapContractToCrmPayload(
  contract: NonNullable<Awaited<ReturnType<typeof getContractInternal>>>
): CrmPackagePayload {
  function getBilling() {
    if (!contract.billingIsSameAsCompanyAddress) {
      assert(contract.billingCity);
      assert(contract.billingCountry);
      assert(contract.billingName);
      assert(contract.billingZipCode);
      return {
        zipCode: contract.billingZipCode,
        country: contract.billingCountry,
        address: formatAddress(
          contract.billingStreetName || "",
          contract.billingStreetType || "",
          contract.billingHouseNumber || "",
          contract.billingFloorDoor || ""
        ),
        city: contract.billingCity,
        name: contract.billingName,
      };
    }
    return {
      zipCode: contract.companyZipCode,
      country: contract.companyCountry,
      address: formatAddress(
        contract.companyStreetName || "",
        contract.companyStreetType || "",
        contract.companyHouseNumber || "",
        contract.companyFloorDoor || ""
      ),
      city: contract.companyCity,
      name: contract.companyName,
    };
  }
  return {
    contractId: contract.id,
    billingIsSameAsCompanyAddress: contract.billingIsSameAsCompanyAddress,
    company: {
      zipCode: contract.companyZipCode,
      country: contract.companyCountry,
      address: formatAddress(
        contract.companyStreetName || "",
        contract.companyStreetType || "",
        contract.companyHouseNumber || "",
        contract.companyFloorDoor || ""
      ),
      city: contract.companyCity,
      name: contract.companyName,
      taxNumber: contract.companyTaxNumber,
      additionalRepName: contract.companyAdditionalRepName,
      authorizedRepName: contract.companyAuthorizedRepName,
      commencementDate: contract.companyCommencementDate,
      invoiceEmail: contract.companyInvoiceEmail,
      registrationNumber: contract.companyRegistrationNumber,
    },
    billing: getBilling(),
    packages: contract.packages.map((pkg) => ({
      quantity: pkg.quantity,
      price: pkg.price,
      id: pkg.packageId,
      name: pkg.package.name,
      historicalData: pkg.historicalData.map((hd) => {
        return {
          name: hd,
        };
      }),
    })),
    contacts: {
      main: {
        phone: contract.mainContactPhone || "",
        firstName: contract.mainContactFirstName || "",
        lastName: contract.mainContactLastName || "",
        position: contract.mainContactPosition || "",
        email: contract.mainContactEmail || "",
      },
      secondary: contract.secondaryContacts.map((contact) => ({
        phone: contact.phone || "",
        firstName: contact.firstName || "",
        lastName: contact.lastName || "",
        position: contact.position || "",
        email: contact.email || "",
      })),
    },
    affiliates: contract.affiliates.map((aff) => ({
      zipCode: aff.zipCode,
      country: aff.country,
      address: aff.address,
      city: aff.city,
      name: aff.name,
      taxNumber: aff.taxNumber,
      registrationNumber: aff.companyRegistration,
      customersHoldingPercentage: aff.customersHoldingPercentage,
    })),
  };
}

export class CrmClient {
  private config: CrmConfig;

  constructor(config: CrmConfig) {
    this.config = config;
  }

  async sendRegistrationData(
    partnerId: number,
    userData: CrmRegistrationData
  ): Promise<any> {
    try {
      const jsonData = {
        PartnerID: partnerId,
        apiKey: this.config.apiKey,
        Email: userData.email,
        First_name: userData.firstName,
        Last_name: userData.lastName,
        Signup_for_newsletter: userData.signupForNewsletter.toString(),
        Phone: userData.phoneNumber,
        Company_name: userData.companyName,
        Country: userData.country,
        Job_title: userData.jobTitle,
      };
      console.log(
        "sending registration data to CRM:",
        JSON.stringify(jsonData)
      );
      const response = await ky.post(this.config.registrationEndpoint, {
        json: jsonData,
        timeout: this.config.timeout,
        retry: this.config.retries,
      });

      if (!response.ok) {
        throw new Error(`response from CRM is not OK: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to send user data to CRM:", error);
      throw new Error(`Failed to send user data to CRM: ${error}`);
    }
  }

  async orderPackage(contractId: string) {
    try {
      const user = authService.getCurrentUser();
      assert(user);

      const contract = await getContractInternal(contractId);
      assert(contract);

      const mapped = mapContractToCrmPayload(contract);
      const jsonData = {
        ...mapped,
        formType: FormType.ORDER,
        partnerId: user.partnerId,
      };

      console.log("sending order data to CRM:", JSON.stringify(jsonData));
      const response = await ky.post(this.config.orderEndpoint, {
        json: jsonData,
        timeout: this.config.timeout,
        retry: this.config.retries,
      });

      if (!response.ok) {
        throw new Error(`response from CRM is not OK: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to send order data to CRM:", error);
      throw new Error(`Failed to send order data to CRM: ${error}`);
    }
  }

  async cancelPackage(contractId: string): Promise<any> {
    try {
      const user = authService.getCurrentUser();
      assert(user);

      const contract = await getContractInternal(contractId);
      assert(contract);
      assert(
        contract.packages.length === 0,
        "there are packages in the cancellation order"
      );

      const mapped = mapContractToCrmPayload(contract);
      const jsonData = {
        ...mapped,
        formType: FormType.CANCEL,
        partnerId: user.partnerId,
      };

      console.log(
        "sending cancel contract data to CRM:",
        JSON.stringify(jsonData)
      );
      const response = await ky.post(this.config.cancelEndpoint, {
        json: jsonData,
        timeout: this.config.timeout,
        retry: this.config.retries,
      });

      if (!response.ok) {
        throw new Error(`response from CRM is not OK: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to send cancel data to CRM:", error);
      throw new Error(`Failed to send cancel data to CRM: ${error}`);
    }
  }

  async updatePackage(contractId: string): Promise<any> {
    try {
      const user = authService.getCurrentUser();
      assert(user);

      const contract = await getContractInternal(contractId);
      assert(contract);

      const mapped = mapContractToCrmPayload(contract);
      const jsonData = {
        ...mapped,
        formType: FormType.UPDATE,
        partnerId: user.partnerId,
      };

      console.log(
        "sending update contract data to CRM:",
        JSON.stringify(jsonData)
      );
      const response = await ky.post(this.config.updateEndpoint, {
        json: jsonData,
        timeout: this.config.timeout,
        retry: this.config.retries,
      });

      if (!response.ok) {
        throw new Error(`response from CRM is not OK: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to send update data to CRM:", error);
      throw new Error(`Failed to send update data to CRM: ${error}`);
    }
  }

  async contractStatusChange(
    payload: CrmContractStatusChangeData
  ): Promise<any> {
    // we don't want to send REJECT for now as the CRM doesn't care about it
    if (payload.formType === FormType.REJECT_CONTRACT) {
      console.log(
        `received ${payload.formType} status change with payload, not sending to CRM`
      );
      return;
    }

    try {
      const user = authService.getCurrentUser();
      assert(user);

      console.log("sending contract status change to CRM", payload);

      const response = await ky.post(this.config.statusChangeEndpoint, {
        json: payload,
        timeout: this.config.timeout,
        retry: this.config.retries,
      });

      if (!response.ok) {
        throw new Error(`response from CRM is not OK: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to send accept data to CRM:", error);
      throw new Error(`Failed to send accept data to CRM: ${error}`);
    }
  }

  async addRemoveUser(payload: CrmUserAddRemoveData) {
    console.log(
      "Sending add/remove user data to CRM:",
      JSON.stringify(payload)
    );

    try {
      const response = await ky.post(this.config.userAddRemoveEndpoint, {
        json: payload,
        timeout: this.config.timeout,
        retry: this.config.retries,
      });

      if (!response.ok) {
        throw new Error(`response from CRM is not OK: ${response.status}`);
      }
    } catch (error) {
      console.error("Failed to send add/remove user data to CRM:", error);
      throw new Error(`Failed to send add/remove user data to CRM: ${error}`);
    }
  }
}

const formatAddress = (
  streetName: string,
  streetType: string,
  houseNumber: string,
  floorDoor?: string
) => {
  let address = `${streetName} ${streetType} ${houseNumber}`;
  if (floorDoor) address += `, ${floorDoor}`;
  return address;
};

// Based on how the other endpoint look we should move the base to env variable and the path to the function (maybe)
const crmRegistrationEndpoint =
  "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.675c354bcace9f8e08c5cdf332a65e9e.f824c70d6726611ccdc3f7befe03053a&isdebug=false";
const crmOrderEndpoint =
  "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.b15aefcd76ed34477f69353908442bdf.3c8b300f5337be83187f5acd6cb95dfc&isdebug=false";
const crmUpdateEndpoint =
  "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.b15aefcd76ed34477f69353908442bdf.3c8b300f5337be83187f5acd6cb95dfc&isdebug=false";
// "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.dff91c42322edcaed9fc6012af5f8233.0b96119466167b828e8c11798c6c70dc&isdebug=false";
const crmCancelEndpoint =
  "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.60243e1bf94adc10ba59e578cc55f129.56dddafd80c9cf94f00847b75dfeef6f&isdebug=false";
const crmContractStatusChangeEndpoint =
  "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.8a900026f8d739c9b35e2b2f6e231d76.a63795a0a74b089f14965adb483c2c54&isdebug=false";
const crmUserAddRemoveEndpoint =
  "https://flow.zoho.eu/20095931933/flow/webhook/incoming?zapikey=1001.94db73ff00913f0615a2038081640387.85121762fdc9f2ee82aeb00e059e72bc&isdebug=false";

const crmClient = new CrmClient({
  registrationEndpoint: crmRegistrationEndpoint,
  orderEndpoint: crmOrderEndpoint,
  updateEndpoint: crmUpdateEndpoint,
  cancelEndpoint: crmCancelEndpoint,
  statusChangeEndpoint: crmContractStatusChangeEndpoint,
  userAddRemoveEndpoint: crmUserAddRemoveEndpoint,
  apiKey: process.env.CRM_API_KEY!,
  timeout: 10000,
  retries: 3,
});

export { crmClient };

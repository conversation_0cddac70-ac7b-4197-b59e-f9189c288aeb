import "i18next";

// Import your primary translation JSON file.
// Adjust the path to where your main/default translation file is located.
// For example, if you have public/locales/en/translation.json:
import type enTranslations from "./shared/i18n/en.json"; // Adjust this path!

declare module "i18next" {
  // Extend CustomTypeOptions
  interface CustomTypeOptions {
    // Default namespace used if not explicitly specified in t() calls.
    // You can set this to the name of your primary namespace if you use them,
    // or a common default. If you don't use namespaces explicitly,
    // you might have a default one (often 'translation').
    defaultNS: "translation"; // Or your actual default namespace

    // Define the shape of your resources
    resources: {
      // Define your namespace(s) here.
      // 'translation' is a common default namespace.
      translation: typeof enTranslations & Record<string, string>;
      // If you had another namespace, e.g., 'userActions':
      // userActions: typeof import('../public/locales/en/userActions.json').default;
    };
    // You can also define other i18next options if needed
  }
}

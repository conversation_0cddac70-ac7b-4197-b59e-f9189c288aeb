import {authService, enforce} from "#root/src/server/auth/auth-service";
import { prisma } from "#root/src/server/db/prisma";
import {scope} from "#root/src/shared/rbac.ts";

export const getAllSubsciptions = async () => {
  enforce(scope("subscriptionPackages").R)
  return prisma.subscription.findMany({
    orderBy: {
      dataSetName: "asc",
    },
  });
};

export const getSubscriptionPackageAssignements = async () => {
  const user = authService.getCurrentUser();
  if (!user || !user.partnerId) {
    return [];
  }

  return prisma.subscriptionPackageAssignment.findMany({
    where: {
      partnerId: user?.partnerId,
      deletedAt: null
    },
    include: {
      subscriptionPackage: {
        include: {
          subscriptions: {
            include: {
              subscription: true,
            },
          },
        },
      },
    },
  });
};

export const getSubscriptionPackageAssignement = async (id: string) => {
  return prisma.subscriptionPackageAssignment.findUnique({
    where: {
      id,
      deletedAt: null
    },
    include: {
      subscriptionPackage: {
        include: {
          subscriptions: {
            include: {
              subscription: true,
            },
          },
        },
      },
    },
  });
};

import { authService } from "#root/src/server/auth/auth-service";
import { getRequestContext } from "#root/src/server/cls";
import { prisma } from "#root/src/server/db/prisma";
import { UserType } from "#root/src/shared/constants";
import type { User } from "@prisma/client";
import { getEmbedding } from "../embeddings/embeddings.telefunc";

const getPartner = async () => {
  const user = authService.getCurrentUser();
  if (!user) {
    const partner = await prisma.partner.findFirst({
      where: {
        name: "Default Public Partner",
      },
      include: {
        subscriptionPackageAssignment: {
          include: {
            subscriptionPackage: {
              include: {
                subscriptions: {
                  include: {
                    subscription: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    return partner!;
  }

  return user.partner;
};

const getPublicSubscriptions = async () => {
  const publicSubscriptions = await prisma.subscription.findMany({
    where: {
      subscriptionName: "Public",
    },
  });
  return publicSubscriptions.map((s) => {
    return { subscription: s };
  });
};

export const getSubscriptionsForUser = async (user?: Pick<User, "type">) => {
  const publicSubscriptions = await getPublicSubscriptions();
  // if user is not logged in, let's find all the public subscription
  // and attach them directly
  if (!user) {
    return publicSubscriptions;
  }

  if (user.type === UserType.ADMIN || user.type === UserType.SUPER_ADMIN) {
    return [
      {
        subscription: {
          id: "*",
          dataSetName: "*",
          subscriptionName: "*",
        },
      },
    ];
  }

  const partner = await getPartner();
  if (!partner) {
    return [];
  }

  const subsFromBoughtPackages = partner.subscriptionPackageAssignment
    .filter((spa) => spa.validUntil > new Date())
    .flatMap((spa) => spa.subscriptionPackage)
    .flatMap((sp) => sp.subscriptions);
  return [...subsFromBoughtPackages, ...publicSubscriptions];
};

export const getSubscriptionsForCurrentUser = () => {
  const user = authService.getCurrentUser();
  return getSubscriptionsForUser(user);
};

export const getSubscriptionsForEmbedding = async (embeddingId: string) => {
  const embedding = await getEmbedding(embeddingId);
  return getSubscriptionsForUser(
    embedding ? { type: embedding.user.type } : undefined
  );
};

export const getSubsciptionsFromCurrentUserOrEmbeddingRequest = () => {
  const req = getRequestContext().req;
  const embeddingId = req.headers["x-embedding-id"];
  if (embeddingId && typeof embeddingId === "string") {
    return getSubscriptionsForEmbedding(embeddingId);
  }
  return getSubscriptionsForCurrentUser();
};

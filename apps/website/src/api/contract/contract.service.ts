import {
  authService,
  type CurrentUserType,
} from "#root/src/server/auth/auth-service";
import { prisma } from "#root/src/server/db/prisma";
import {
  ContractStatus,
  PartnerStatus,
  type ContractType,
} from "#root/src/shared/constants";
import type { Prisma } from "@prisma/client";
import assert from "assert";

export type CreateContractFormDTO = Pick<
  Prisma.ContractCreateInput,
  | "companyName"
  | "companyTaxNumber"
  | "companyRegistrationNumber"
  | "companyStreetName"
  | "companyStreetType"
  | "companyHouseNumber"
  | "companyFloorDoor"
  | "companyCity"
  | "companyZipCode"
  | "companyCountry"
  | "companyAdditionalRepName"
  | "companyCommencementDate"
  | "companyAuthorizedRepName"
  | "companyInvoiceEmail"
  | "billingIsSameAsCompanyAddress"
  | "billingName"
  | "billingStreetName"
  | "billingStreetType"
  | "billingHouseNumber"
  | "billingFloorDoor"
  | "billingCity"
  | "billingZipCode"
  | "billingCountry"
  | "mainContactFirstName"
  | "mainContactLastName"
  | "mainContactPosition"
  | "mainContactPhone"
  | "mainContactEmail"
> & {
  packages: Pick<
    Prisma.ContractPackageUncheckedCreateWithoutContractInput,
    "price" | "packageId" | "quantity" | "historicalData"
  >[];
  secondaryContacts: Pick<
    Prisma.ContractSecondaryContactCreateWithoutContractInput,
    "firstName" | "lastName" | "position" | "phone" | "email"
  >[];
  affiliates: Pick<
    Prisma.ContractAffiliatedCompanyCreateWithoutContractInput,
    | "name"
    | "taxNumber"
    | "companyRegistration"
    | "address"
    | "city"
    | "zipCode"
    | "country"
    | "customersHoldingPercentage"
  >[];
};

export async function createContractFromDTO(
  formData: CreateContractFormDTO,
  type: ContractType
) {
  const user = authService.getCurrentUser();
  assert(user);
  const created = await prisma.contract.create({
    data: {
      ...formData,
      partnerId: user.partnerId!,
      status: ContractStatus.PENDING,
      type,
      createdById: user.id,
      packages: {
        create: formData.packages,
      },
      secondaryContacts: {
        create: formData.secondaryContacts,
      },
      affiliates: {
        create: formData.affiliates,
      },
    },
    include: {
      packages: true,
      secondaryContacts: true,
      affiliates: true,
    },
  });

  return created;
}

export function getContractInternal(id: string) {
  return prisma.contract.findUnique({
    where: {
      id,
    },
    include: {
      packages: {
        include: {
          package: true,
        },
      },
      secondaryContacts: true,
      affiliates: true,
    },
  });
}

export const setPartnerStatusOnFirstOrder = async (user: CurrentUserType) => {
  if (user == null || user.partner == null || user.partnerId == null) return;
  if (user.partner.subscriptionPackageAssignment.length === 0)
    await prisma.partner.update({
      where: {
        id: user.partnerId,
      },
      data: {
        status: PartnerStatus.PENDING,
      },
    });
};

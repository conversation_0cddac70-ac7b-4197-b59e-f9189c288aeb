import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  acceptContract,
  createContract,
  getAllContracts,
  getContract,
  getContractsForPartner,
  rejectContract,
  updateOrCancelContract,
} from "./contract.telefunc";

export const useContract = (id: string) => {
  return useSuspenseQuery({
    queryKey: ["contracts", id],
    queryFn: () => getContract(id),
  });
};

export const useContractsForPartner = (partnerId: number) => {
  return useSuspenseQuery({
    queryKey: ["contracts", "partnerId", partnerId],
    queryFn: () => getContractsForPartner(partnerId),
  });
};

export const useAllContracts = () => {
  return useSuspenseQuery({
    queryKey: ["contracts", "all"],
    queryFn: () => getAllContracts(),
  });
};

export const useCreateContract = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createContract,
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });
};

export const useUpdateOrCancelContract = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateOrCancelContract,
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });
};

export const useRejectContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: rejectContract,
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });
};

export const useAcceptContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: acceptContract,
    onSuccess: () => {
      queryClient.invalidateQueries();
    },
  });
};

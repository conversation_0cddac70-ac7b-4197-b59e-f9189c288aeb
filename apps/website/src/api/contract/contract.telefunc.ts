import { authService, enforce } from "#root/src/server/auth/auth-service";
import { crmClient } from "#root/src/server/crm";
import { prisma } from "#root/src/server/db/prisma";
import { assert } from "#root/src/shared/assert";
import {
  ContractStatus,
  ContractType,
  FormType,
  PartnerStatus,
  UserType,
} from "#root/src/shared/constants";
import { scope } from "#root/src/shared/rbac";
import { PrismaTransactional } from "@myfunc/prisma-transactional";
import {
  createContractFromDTO,
  setPartnerStatusOnFirstOrder,
  type CreateContractFormDTO as CreateContractFormDTO,
} from "./contract.service";

export const getContract = async (id: string) => {
  const user = authService.getCurrentUser();
  if (!user) {
    return null;
  }

  const where = {
    id,
  };

  if (user.type !== UserType.ADMIN && user.type !== UserType.SUPER_ADMIN) {
    assert(user.partnerId);
    where["partnerId"] = user.partnerId;
  }

  return await prisma.contract.findFirst({
    where,
    include: {
      affiliates: true,
      packages: {
        include: {
          package: true,
        },
      },
      secondaryContacts: true,
    },
  });
};

export const getContractsForPartner = async (partnerId: number) => {
  const user = authService.getCurrentUser();

  if (
    !user ||
    (user.type !== UserType.ADMIN &&
      user.type !== UserType.SUPER_ADMIN &&
      user.partnerId !== partnerId)
  ) {
    return [];
  }

  const contracts = await prisma.contract.findMany({
    where: {
      partnerId,
    },
    include: {
      affiliates: true,
      packages: true,
      secondaryContacts: true,
    },
    orderBy: {
      id: "desc",
    },
  });

  return contracts;
};

/**
 * Get all contracts for the currently logged in user.
 * Admin users will see all contracts, others will only see contracts for their partner.
 */
export const getAllContracts = async () => {
  const user = authService.getCurrentUser();
  if (!user) {
    return [];
  }

  const where: any = {};

  if (user.type !== UserType.ADMIN && user.type !== UserType.SUPER_ADMIN) {
    if (!user.partnerId) {
      return [];
    }
    where.partnerId = user.partnerId;
  }

  const contracts = await prisma.contract.findMany({
    where,
    include: {
      partner: true,
      packages: {
        include: {
          package: true,
        },
      },
      createdBy: {
        select: {
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  });

  return contracts;
};

export const createContract = (formData: CreateContractFormDTO) =>
  PrismaTransactional.execute(async () => {
    const user = enforce(scope("subscriptionPackageOrder").W);
    assert(user);
    const created = await createContractFromDTO(
      formData,
      ContractType.NEW_ORDER
    );
    await setPartnerStatusOnFirstOrder(user);
    await crmClient.orderPackage(created.id);

    return created;
  });

export const updateOrCancelContract = (formData: CreateContractFormDTO) =>
  PrismaTransactional.execute(async () => {
    const user = enforce(scope("subscriptionPackageUpdate").W);
    assert(user);

    const created = await createContractFromDTO(formData, ContractType.UPDATE);
    if (created.packages.length === 0) {
      await crmClient.cancelPackage(created.id);
    } else {
      await crmClient.updatePackage(created.id);
    }

    return created;
  });

/**
 * Reject a contract by setting its status to REJECTED
 */
export const rejectContract = async (id: string) => {
  const user = authService.getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  // Only admins can reject contracts
  if (user.type !== UserType.ADMIN && user.type !== UserType.SUPER_ADMIN) {
    throw new Error("Only admins can reject contracts");
  }

  return await PrismaTransactional.execute(async () => {
    const contract = await prisma.contract.findUnique({
      where: { id },
      include: {
        packages: true,
        partner: true,
      },
    });

    if (!contract) {
      throw new Error("Contract not found");
    }

    if (contract.status !== ContractStatus.PENDING) {
      throw new Error("Only pending contracts can be accepted");
    }

    if (contract.partner.status === "PENDING") {
      await prisma.partner.update({
        where: { id: contract.partnerId },
        data: {
          status: PartnerStatus.NONE,
          updatedAt: new Date(),
        },
      });
    }

    // Update the contract status
    const updated = await prisma.contract.update({
      where: { id },
      data: {
        status: ContractStatus.REJECTED,
        updatedAt: new Date(),
      },
    });

    await crmClient.contractStatusChange({
      contractId: updated.id,
      formType: FormType.REJECT_CONTRACT,
      partnerId: contract.partnerId,
    });

    return updated;
  });
};

export const acceptContract = async (id: string) => {
  const user = authService.getCurrentUser();
  if (!user) {
    throw new Error("Unauthorized");
  }

  if (user.type !== UserType.ADMIN && user.type !== UserType.SUPER_ADMIN) {
    throw new Error("Only admins can accept contracts");
  }

  return await PrismaTransactional.execute(async () => {
    const contract = await prisma.contract.findUnique({
      where: { id },
      include: {
        packages: true,
        partner: true,
        createdBy: true,
      },
    });

    if (!contract) {
      throw new Error("Contract not found");
    }

    if (contract.status !== ContractStatus.PENDING) {
      throw new Error("Only pending contracts can be accepted");
    }

    // 1. Update the Partner model if needed
    await prisma.partner.update({
      where: { id: contract.partnerId },
      data: {
        name: contract.companyName,
        country: contract.companyCountry,
        updatedAt: new Date(),
      },
    });

    // 2. + 3.:
    // Go through all the existing subscription package assignments and also the
    // the ones in the contract and do the following:
    // - If an SPA is in both of them continue
    // - If an SPA is just in the exiting ones then update the existing ones "deleteNextCycle" to true
    // - If an SPA is just in the contact then add it to the SPAs of the partner
    const existingSPAs = await prisma.subscriptionPackageAssignment.findMany({
      where: { partnerId: contract.partnerId },
    });

    const contractPackageIds = contract.packages.map((pkg) => pkg.packageId);
    const existingPackageIds = existingSPAs.map(
      (spa) => spa.subscriptionPackageId
    );

    const inADistantFutureInAGalaxyFarFarAway = new Date();
    inADistantFutureInAGalaxyFarFarAway.setFullYear(
      inADistantFutureInAGalaxyFarFarAway.getFullYear() + 999
    );

    for (const existingSPA of existingSPAs) {
      if (!contractPackageIds.includes(existingSPA.subscriptionPackageId)) {
        await prisma.subscriptionPackageAssignment.update({
          where: { id: existingSPA.id },
          data: { deleteNextCycle: true },
        });
      }
    }

    for (const pkg of contract.packages) {
      if (!existingPackageIds.includes(pkg.packageId)) {
        await prisma.subscriptionPackageAssignment.create({
          data: {
            partnerId: contract.partnerId,
            subscriptionPackageId: pkg.packageId,
            validUntil: inADistantFutureInAGalaxyFarFarAway,
          },
        });
      } else {
        // check whether the package "deleteNextCycle" is set to true and set it to false, because the package is being renewed
        const existing = existingSPAs.find(
          (exSpa) => exSpa.subscriptionPackageId === pkg.packageId
        );
        await prisma.subscriptionPackageAssignment.update({
          where: { id: existing?.id },
          data: { deleteNextCycle: false },
        });
      }
    }

    if (contract.partner.status === "PENDING") {
      await prisma.partner.update({
        where: { id: contract.partnerId },
        data: {
          status: "ACTIVE",
          updatedAt: new Date(),
        },
      });
    }

    // 4. Update the contract status to ACCEPTED
    const updated = await prisma.contract.update({
      where: { id },
      data: {
        status: ContractStatus.ACCEPTED,
        updatedAt: new Date(),
      },
    });

    if (contract.type === ContractType.NEW_ORDER) {
      await prisma.user.update({
        where: { id: contract.createdById },
        data: {
          type: UserType.PARTNER_ADMIN,
        },
      });
    }

    // notify CRM that the contract has been accepted
    await crmClient.contractStatusChange({
      contractId: contract.id,
      formType: FormType.ACTIVATE_CONTRACT,
      partnerId: contract.partnerId,
    });

    return updated;
  });
};

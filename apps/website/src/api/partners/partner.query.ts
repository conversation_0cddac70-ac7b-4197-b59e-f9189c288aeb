import { useAuth } from "#root/src/api/auth/auth.query.ts";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  deletePartner,
  disablePartner,
  getPartner,
  getPartnerMainContact,
  getPartners,
  getPartnerWithContracts,
  updatePartner,
} from "./partners.telefunc";

export const usePartner = (id: number) => {
  return useSuspenseQuery({
    queryKey: ["partner", id],
    queryFn: () => getPartner(id),
  });
};

export const usePartners = () => {
  return useSuspenseQuery({
    queryKey: ["partners"],
    queryFn: () => getPartners(),
  });
};

export const useDisablePartner = (partnerId: number) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: () => disablePartner(partnerId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
    },
  });
};

export const useUpdatePartner = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updatePartner,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
    },
  });
};

export const usePartnerMainContact = (id?: number) => {
  const auth = useAuth();
  return useSuspenseQuery({
    queryKey: ["partnerMainContact"],
    queryFn: () => {
      if (!auth?.profile?.partnerId && !id) throw new Error("No partner id");
      return getPartnerMainContact(id ?? auth.profile!.partnerId!);
    },
  });
};

export const usePartnerWithContracts = (id: number) => {
  return useSuspenseQuery({
    queryKey: ["partners-with-contracts", id],
    queryFn: () => getPartnerWithContracts(id),
  });
};

export const useDeletePartner = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deletePartner,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      queryClient.invalidateQueries({ queryKey: ["adminPartners"] });
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["adminUsers"] });
      queryClient.invalidateQueries({ queryKey: ["registrations"] });
      queryClient.invalidateQueries({ queryKey: ["contracts"] });
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
    },
  });
};

import { authService, enforce } from "#root/src/server/auth/auth-service";
import { prisma } from "#root/src/server/db/prisma";
import type { FormatType } from "#root/src/api/tabular-formats/encode";
import { encodeDataAsTabularDataString } from "#root/src/api/tabular-formats/encode";
import { scope } from "#root/src/shared/rbac.ts";

const ADMIN_EMAIL = "<EMAIL>";

export const getPartner = async (id: number) => {
  const user = authService.getCurrentUser();
  enforce(scope(user?.partnerId === id ? "partner" : "partners").R);

  return prisma.partner.findUnique({
    where: {
      id,
    },
    include: {
      subscriptionPackageAssignment: {
        include: {
          subscriptionPackage: true,
        },
      },
    },
  });
};

export async function getPartners() {
  enforce(scope("partners").R);
  return prisma.partner.findMany({
    include: {
      subscriptionPackageAssignment: {
        include: {
          subscriptionPackage: {
            include: {
              subscriptions: true,
            },
          },
        },
      },
    },
  });
}

export async function getPartnersAsTabularData(type: FormatType) {
  enforce(scope("partners").R);
  return encodeDataAsTabularDataString(
    (await getPartners()).map((x) => ({
      ...x,
      subs: x.subscriptionPackageAssignment.length,
    })),
    {
      id: "Id",
      name: "Name",
      subs: "Assigned subscription packages",
    },
    type
  );
}

export async function disablePartner(id: number) {
  const user = authService.getCurrentUser();
  enforce(scope(user?.partnerId === id ? "partner" : "partners").W);
  prisma.partner.update({
    where: {
      id,
    },
    data: {
      status: "disabled",
    },
  });
}

export const updatePartner = ({
  id,
  name,
  country,
}: {
  id: number;
  name?: string;
  country?: string;
}) => {
  const user = authService.getCurrentUser();
  enforce(scope(user?.partnerId === id ? "partner" : "partners").W);

  return prisma.partner.update({
    where: {
      id,
    },
    data: {
      name,
      country,
    },
  });
};

export async function getPartnerMainContact(id: number) {
  const user = authService.getCurrentUser();
  enforce(scope(user?.partnerId === id ? "partner" : "partners").R);
  return await prisma.mainPartnerContact.findFirstOrThrow({
    where: {
      partnerId: id,
    },
  });
}

export async function getPartnerWithContracts(id: number) {
  const user = authService.getCurrentUser();
  enforce(scope(user?.partnerId === id ? "partner" : "partners").R);

  const result = await prisma.partner.findFirstOrThrow({
    where: {
      id,
    },
    include: {
      contracts: true,
    },
  });

  return result;
}

// this function is just for the development environment
// for the tester to be able to make partners and registrations
// disappear
export async function deletePartner(partnerId: number) {
  const user = authService.getCurrentUser();
  if (!user || user.email !== ADMIN_EMAIL) {
    throw new Error("Unauthorized: Only <EMAIL> can delete partners");
  }

  return prisma.$transaction(async (tx) => {
    const partnerUsers = await tx.user.findMany({
      where: { partnerId },
      select: { id: true, email: true },
    });

    const userIds = partnerUsers.map((u) => u.id);
    const userEmails = partnerUsers.map((u) => u.email);

    if (userEmails.length > 0) {
      await tx.registration.deleteMany({
        where: {
          email: { in: userEmails },
        },
      });
    }

    const contractsToDelete = await tx.contract.findMany({
      where: {
        OR: [{ partnerId }, { createdById: { in: userIds } }],
      },
      select: { id: true },
    });

    const contractIds = contractsToDelete.map((c) => c.id);

    if (contractIds.length > 0) {
      await tx.contractAffiliatedCompany.deleteMany({
        where: { contractId: { in: contractIds } },
      });

      await tx.contractSecondaryContact.deleteMany({
        where: { contractId: { in: contractIds } },
      });

      await tx.contractPackage.deleteMany({
        where: { contractId: { in: contractIds } },
      });

      await tx.contract.deleteMany({
        where: { id: { in: contractIds } },
      });
    }

    if (userIds.length > 0) {
      await tx.announcementReadReceipt.deleteMany({
        where: { userId: { in: userIds } },
      });

      await tx.auditLogEvent.deleteMany({
        where: { userId: { in: userIds } },
      });

      await tx.passwordReset.deleteMany({
        where: { userId: { in: userIds } },
      });

      await tx.apiKey.deleteMany({
        where: { userId: { in: userIds } },
      });

      const embeddings = await tx.embedding.findMany({
        where: { userId: { in: userIds } },
        select: { id: true },
      });

      if (embeddings.length > 0) {
        await tx.embeddingsAllowedOrigin.deleteMany({
          where: { embeddingId: { in: embeddings.map((e) => e.id) } },
        });

        await tx.embedding.deleteMany({
          where: { userId: { in: userIds } },
        });
      }

      const announcements = await tx.announcement.findMany({
        where: { createdBy: { in: userIds } },
        select: { id: true },
      });

      if (announcements.length > 0) {
        await tx.announcementDsLink.deleteMany({
          where: { announcementId: { in: announcements.map((a) => a.id) } },
        });

        await tx.announcement.deleteMany({
          where: { createdBy: { in: userIds } },
        });
      }
    }

    await tx.mainPartnerContact.deleteMany({
      where: { partnerId },
    });

    await tx.subscriptionPackageAssignment.deleteMany({
      where: { partnerId },
    });

    await tx.user.deleteMany({
      where: { partnerId },
    });

    await tx.partner.delete({
      where: { id: partnerId },
    });
  });
}

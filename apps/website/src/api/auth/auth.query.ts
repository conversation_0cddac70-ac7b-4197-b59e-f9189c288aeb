/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  createRegistration,
  deleteRegistration,
  getPasswordReset,
  getPendingRegistrations,
  getProfile,
  getRegistrationsAsTabularData,
  login,
  logout,
  sendPasswordReset,
  updatePassword,
} from "./auth.telefunc";

import type { MutationOptions } from "#root/src/server/types";
import {
  type RawScopeDefinition,
  getTypesInHierarchicalOrder,
  hasScope as hasScopeRaw,
} from "#root/src/shared/rbac.ts";
export const useProfile = () => {
  const methods = useSuspenseQuery({
    queryKey: ["getProfile"],
    queryFn: () => getProfile(),
  });

  return methods;
};

export const useLogin = (options?: MutationOptions<typeof login>) => {
  const queryClient = useQueryClient();
  const methods = useMutation({
    mutationFn: login,
    ...options,
    onSettled: () => {
      queryClient.invalidateQueries();
    },
  });

  return methods;
};

export const useLogout = () => {
  const queryClient = useQueryClient();
  const methods = useMutation({
    mutationFn: logout,
    onSettled: () => {
      queryClient.invalidateQueries();
    },
  });

  return methods;
};

export const useAuth = (options?: {
  onLogin?: () => void;
  onUpdatePassword?: () => void;
}) => {
  const profile = useProfile();
  const login = useLogin({
    onSuccess(data, variables, context) {
      options?.onLogin?.();
    },
  });
  const logout = useLogout();
  const sendPasswordReset = useSendPasswordReset();
  const updatePassword = useUpdatePassword({
    onSuccess(data, variables, context) {
      options?.onUpdatePassword?.();
    },
  });

  const hasScope = (scope: RawScopeDefinition) =>
    profile.data?.type ? hasScopeRaw(profile.data.type, scope) : false;

  return {
    login,
    sendPasswordReset,
    updatePassword,
    logout,
    profile: profile.data,
    refetch: profile.refetch,
    hasScope,
    hierarchyBelow: getTypesInHierarchicalOrder(profile.data?.type ?? "USER"),
  };
};

export const useCreateRegistration = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createRegistration,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["registrations"] });
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

export const usePendingRegistrations = () => {
  return useSuspenseQuery({
    queryKey: ["registrations"],
    queryFn: () => getPendingRegistrations(),
  });
};

export const useRegistrationsCsv = () => {
  return useQuery({
    queryKey: ["registrationsCsv"],
    queryFn: () => getRegistrationsAsTabularData("csv"),
  });
};

export const useRegistrationsXlsx = () => {
  return useQuery({
    queryKey: ["registrationsXlsx"],
    queryFn: () => getRegistrationsAsTabularData("xlsx"),
  });
};

export const useDeleteRegistration = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteRegistration,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      queryClient.invalidateQueries({ queryKey: ["registrations"] });
    },
  });
};
export const useSendPasswordReset = () => {
  const methods = useMutation({
    mutationFn: sendPasswordReset,
  });

  return methods;
};

export const usePasswordReset = (token: string) => {
  const methods = useSuspenseQuery({
    queryKey: ["passwordReset", token],
    queryFn: () =>
      token ? getPasswordReset({ token }) : ({ valid: false } as const),
  });

  return methods;
};

export const useUpdatePassword = (
  options?: MutationOptions<typeof updatePassword>
) => {
  const queryClient = useQueryClient();
  const methods = useMutation({
    mutationFn: updatePassword,
    ...options,
    onSettled: () => {
      queryClient.invalidateQueries();
    },
  });

  return methods;
};

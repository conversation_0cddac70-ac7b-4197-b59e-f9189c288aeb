import { authService } from "#root/src/server/auth/auth-service";
import { getRequestContext } from "#root/src/server/cls";
import type { FormatType } from "#root/src/api/tabular-formats/encode";
import { encodeDataAsTabularDataString } from "#root/src/api/tabular-formats/encode";
import { crmClient } from "#root/src/server/crm";

export async function login({
  email,
  password,
  newPassword,
  token,
}: {
  email: string;
  password?: string;
  newPassword?: string;
  token?: string;
}) {
  const ctx = getRequestContext();
  const result = await authService.login({
    email,
    password,
    newPassword,
    token,
  });
  if (result.status === "OK") {
    ctx.req.session = { userId: result.userId, email };
  }
  return result;
}

export async function logout() {
  const ctx = getRequestContext();
  ctx.req.session = null;
}

export async function getProfile() {
  const ctx = getRequestContext();
  return ctx.req.user ?? null;
}

export async function createRegistration(values: {
  companyName: string;
  firstName: string;
  lastName: string;
  email: string;
  jobTitle?: string;
  phoneNumber?: string;
  country?: string;
  enableNotifications: boolean;
}) {
  const { partner, user, registration } = await authService.createRegistration(
    values
  );

  /*  We only send this data on first signin, when the user is activated
      I leave it here for convenience if we somehow reintroduce this,
      but delete it if you see that the commit is a month old

  await crmClient.sendRegistrationData(partner.id, {
    email: values.email,
    firstName: values.firstName,
    lastName: values.lastName,
    jobTitle: values.jobTitle,
    phoneNumber: values.phoneNumber,
    country: values.country,
    signupForNewsletter: values.enableNotifications,
    companyName: values.companyName,
  });
  */

  return {
    partner,
    user,
    registration,
  };
}

export async function getPendingRegistrations() {
  return authService.getPendingRegistrations();
}

export async function getRegistrationsAsTabularData(type: FormatType) {
  return encodeDataAsTabularDataString(
    await authService.getPendingRegistrations(),
    { id: "Id", companyName: "Company name" },
    type
  );
}

export async function deleteRegistration({ id }: { id: number }) {
  return authService.deleteRegistration({ id });
}
export async function getPasswordReset({ token }: { token: string }) {
  const passwordReset = await authService.getPasswordReset(token);
  if (!passwordReset) {
    return { valid: false } as const;
  }

  return { ...passwordReset, valid: true } as const;
}

export async function sendPasswordReset({ email }: { email: string }) {
  authService
    .sendPasswordResetEmail({
      email,
    })
    .catch((err) => {
      console.log(err);
    });
}

export async function updatePassword({
  password,
  newPassword,
}: {
  password: string;
  newPassword: string;
}) {
  const ctx = getRequestContext();
  if (!ctx.req.user) {
    return { status: "NOT_LOGGED_IN" };
  }
  try {
    await authService.updatePassword({
      email: ctx.req.user.email,
      password,
      newPassword,
    });
  } catch (error: any) {
    return { status: error.message };
  }

  return { status: "OK" };
}

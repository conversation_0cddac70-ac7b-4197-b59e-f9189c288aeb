/* eslint-disable sonarjs/no-duplicate-string */
import {
  assignPackageToPartner,
  createNewSubscriptionPackage,
  getAllSubscriptionPackages,
  getSubscriptionPackageById,
  removePackageFromPartner,
} from "#root/src/api/subscription-packages/subscription-packages.telefunc";
import {
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  getSubscriptionPackageAssignement,
  getSubscriptionPackageAssignements,
} from "../subscriptions/subscriptions.telefunc";

export const useCreateSubscriptionPackage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createNewSubscriptionPackage,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["subscription-package"] });
    },
  });
};

export const useSubscriptionPackages = (partnerId?: number) => {
  return useSuspenseQuery({
    queryKey: ["subscription-package"],
    queryFn: () => getAllSubscriptionPackages(partnerId),
  });
};

export const useSubscriptionPackage = (id: string) => {
  return useQuery({
    queryKey: ["subscription-package", id],
    queryFn: () => getSubscriptionPackageById(id),
  });
};

export const useSubscriptionPackageAssignements = () => {
  return useQuery({
    queryKey: ["subscription-package-assignement"],
    queryFn: () => getSubscriptionPackageAssignements(),
  });
};

export const useSubscriptionPackageAssignement = (id: string) => {
  return useQuery({
    queryKey: ["subscription-package-assignement", id],
    queryFn: () => getSubscriptionPackageAssignement(id),
  });
};

export const useAssignSubscriptionPackageToPartner = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: assignPackageToPartner,
    onSettled() {
      queryClient.invalidateQueries({
        queryKey: ["subscription-package"],
      });
      queryClient.invalidateQueries({
        queryKey: ["subscription-package-assignement"],
      });
      queryClient.invalidateQueries({
        queryKey: ["partner"],
      });
    },
  });
};

export const useUnassignSubscriptionPackage = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: removePackageFromPartner,
    onSettled() {
      queryClient.invalidateQueries({
        queryKey: ["subscription-package"],
      });
      queryClient.invalidateQueries({
        queryKey: ["subscription-package-assignement"],
      });
      queryClient.invalidateQueries({
        queryKey: ["partner"],
      });
    },
  });
};

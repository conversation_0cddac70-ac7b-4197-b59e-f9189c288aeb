import { enforce } from "#root/src/server/auth/auth-service.ts";
import { prisma } from "#root/src/server/db/prisma";
import { scope } from "#root/src/shared/rbac.ts";

export const createNewSubscriptionPackage = async ({
  name,
  subscriptions,
  priceEur,
}: {
  name: string;
  subscriptions: string[];
  priceEur: number;
}) => {
  enforce(scope("subscriptionPackages").W);
  return prisma.subscriptionPackage.create({
    data: {
      name,
      priceEur,
      subscriptions: {
        createMany: {
          data: subscriptions.map((s) => {
            return {
              subscriptionId: s,
            };
          }),
        },
      },
    },
  });
};

export const getAllSubscriptionPackages = async (partnerId?: number) => {
  return prisma.subscriptionPackage.findMany({
    include: {
      subscriptions: {
        include: {
          subscription: true,
        },
        orderBy: {
          subscription: {
            dataSetName: "asc",
          },
        },
      },
    },
    orderBy: {
      name: "asc",
    },
    ...(partnerId
      ? {
        where: {
          SubscriptionPackageAssignment: {
            some: {
              partnerId,
            },
            every: {
              deletedAt: null,
            },
          },
        },
      }
      : {}),
  });
};

export const getSubscriptionPackageById = async (id: string) => {
  return prisma.subscriptionPackage.findUnique({
    where: { id },
  });
};

export const assignPackageToPartner = async ({
  partnerId,
  subscriptionPackageId,
  validUntil,
}: {
  partnerId: number;
  subscriptionPackageId: string;
  validUntil: Date;
}) => {
  return prisma.subscriptionPackageAssignment.create({
    data: {
      partnerId,
      subscriptionPackageId,
      validUntil,
    },
  });
};

export const removePackageFromPartner = async (id: string, now?: boolean) => {
  if (now) {
    return prisma.subscriptionPackageAssignment.delete({
      where: {
        id,
      },
    });
  }
  prisma.subscriptionPackageAssignment.update({
    where: { id },
    data: {
      deleteNextCycle: true,
    },
  });
};

export const getSubscriptionPackageAssignment = async (id: string) => {
  return prisma.subscriptionPackageAssignment.findFirst({
    where: {
      id,
    },
    include: {
      subscriptionPackage: {
        include: {
          subscriptions: {
            include: {
              subscription: true,
            },
            orderBy: {
              subscription: {
                dataSetName: "asc",
              },
            },
          },
        },
      },
      partner: true,
    },
  });
};

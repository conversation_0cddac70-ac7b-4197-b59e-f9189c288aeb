import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  createEmbedding,
  deleteEmbedding,
  getEmbedding,
  getEmbeddings,
  updateEmbedding,
} from "./embeddings.telefunc";

export const useEmbeddings = () => {
  return useSuspenseQuery({
    queryKey: ["embeddings"],
    queryFn: async () => getEmbeddings(),
  });
};

export const useEmbedding = (id: string) => {
  return useSuspenseQuery({
    queryKey: ["embedding", id],
    queryFn: () => getEmbedding(id),
  });
};

export const useCreateEmbedding = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createEmbedding,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["embeddings"] });
    },
  });
};

export const useDeleteEmbedding = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => deleteEmbedding(id),
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["embeddings"] });
    },
  });
};

export const useUpdateEmbedding = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateEmbedding,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["embeddings"] });
    },
  });
};

import { authService } from "#root/src/server/auth/auth-service";
import { prisma } from "#root/src/server/db/prisma";
import { UserType } from "#root/src/shared/constants";
import { v4 as uuid } from "uuid";

export async function getEmbeddings() {
  const user = authService.getCurrentUser();
  if (!user) return [];

  // don't add where clause for admins
  if (user?.type === UserType.ADMIN || user?.type === UserType.SUPER_ADMIN) {
    return await prisma.embedding.findMany({
      include: {
        EmbeddingsAllowedOrigin: true,
      },
    });
  }

  const embeddings = await prisma.embedding.findMany({
    where: {
      user: {
        partnerId: user.partnerId,
      },
    },
    include: {
      EmbeddingsAllowedOrigin: true,
    },
  });

  return embeddings;
}

export async function createEmbedding({
  userId,
  name,
  dataSetOperationId,
  apiParams,
  includeTable,
  includeChart,
  allowedOrigins,
}: {
  userId: number;
  name: string;
  apiParams: string;
  includeTable: boolean;
  includeChart: boolean;
  allowedOrigins: string[];
  dataSetOperationId: string;
}) {
  return await prisma.$transaction(async (tx) => {
    const data = {
      userId,
      includeChart,
      includeTable,
      name,
      apiParams,
      dataSetOperationId,
    };

    const embedding = await tx.embedding.create({
      data: {
        ...data,
        dataSet: "",
        token: uuid(),
        EmbeddingsAllowedOrigin: {
          createMany: {
            data: allowedOrigins.map((ao) => {
              return {
                enabled: true,
                origin: ao,
              };
            }),
          },
        },
      },
    });

    return embedding;
  });
}

export const getEmbedding = async (id: string) => {
  const embedding = await prisma.embedding.findUnique({
    where: {
      id,
    },
    include: {
      EmbeddingsAllowedOrigin: true,
      user: {
        select: {
          type: true,
        },
      },
    },
  });

  if (!embedding) {
    return null;
  }

  return embedding;
};

export const deleteEmbedding = async (id: string) => {
  return await prisma.$transaction(async (tx) => {
    // First delete all allowed origins for this embedding
    await tx.embeddingsAllowedOrigin.deleteMany({
      where: { embeddingId: id },
    });

    // Then delete the embedding itself
    return await tx.embedding.delete({
      where: { id },
    });
  });
};

export const updateEmbedding = async (args: {
  id: string;
  name: string;
  apiParams: string;
  includeTable: boolean;
  includeChart: boolean;
  allowedOrigins: {
    origin: string;
  }[];
}) => {
  await prisma.embeddingsAllowedOrigin.deleteMany({
    where: {
      embeddingId: args.id,
    },
  });
  const response = await prisma.embedding.update({
    where: {
      id: args.id,
    },
    data: {
      name: args.name,
      apiParams: args.apiParams,
      includeTable: args.includeTable,
      includeChart: args.includeChart,
      EmbeddingsAllowedOrigin: {
        createMany: {
          data: args.allowedOrigins.map((ao) => {
            return {
              enabled: true,
              origin: ao.origin,
            };
          }),
        },
      },
    },
  });
  return response;
};

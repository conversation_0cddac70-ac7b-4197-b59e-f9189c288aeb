import type { FormatType } from "#root/src/api/tabular-formats/encode";
import { encodeDataAsTabularDataString } from "#root/src/api/tabular-formats/encode";
import {
  authService,
  type CurrentUserType,
  enforce,
} from "#root/src/server/auth/auth-service";
import { prisma } from "#root/src/server/db/prisma";
import { FormType, UserStatus, UserType } from "#root/src/shared/constants";
import { hasScope, scope } from "#root/src/shared/rbac.ts";
import { PrismaTransactional } from "@myfunc/prisma-transactional";
import { getSubscriptionsForCurrentUser } from "../subscriptions/subscriptions.service";
import { crmClient } from "#root/src/server/crm";

export type AssertedCurrentUserType = NonNullable<CurrentUserType>;

const ADMIN_EMAIL = "<EMAIL>";

export async function getUsers(partnerId?: number) {
  const user = authService.getCurrentUser();
  if (
    !user ||
    !hasScope(user.type, scope("partner").R) ||
    !hasScope(user.type, scope("partners").R)
  ) {
    throw new Error("Unauthorized");
  }
  const hasPartnersReadPerm = hasScope(user.type, scope("partners").R);

  return prisma.user.findMany({
    ...(!hasPartnersReadPerm && {
      where: {
        partnerId: user?.partnerId,
      },
    }),
    ...(hasPartnersReadPerm &&
      partnerId && {
        where: {
          partnerId: partnerId,
        },
      }),
    orderBy: {
      id: "asc",
    },
    include: {
      partner: {
        select: {
          name: true,
        },
      },
    },
  });
}

export async function getUsersAsTabularData(format: FormatType) {
  return encodeDataAsTabularDataString(
    (await getUsers()).map((x) => ({
      ...x,
      // @ts-expect-error - Adding partnerName property to user object for export
      partnerName: x.partner?.name ?? "",
    })),
    {
      id: "Database Id",
      firstName: "First Name",
      lastName: "Last Name",
      partnerName: "Partner Name",
      email: "Email",
      status: "User Status",
      type: "User type",
    },
    format
  );
}

export async function createUser({
  firstName,
  lastName,
  email,
  partnerId,
  phoneNumber,
  jobTitle,
  enableNotifications,
}: {
  firstName: string;
  lastName: string;
  email: string;
  partnerId: number;
  phoneNumber: string | null;
  jobTitle: string | null;
  enableNotifications: boolean;
}) {
  const user = authService.getCurrentUser();
  if (!user) {
    return null;
  }

  const hasPartnerAssignPerm = hasScope(user.type, scope("partners").R);

  return authService.createUser({
    firstName,
    lastName,
    email,
    partnerId,
    phoneNumber,
    jobTitle,
    enableNotifications,
    ...(!hasPartnerAssignPerm && {
      partnerId: user!.partnerId as number,
    }),
  });
}

export const activateUser = async (userId: number) => {
  const user = await prisma.user.findUnique({
    where: {
      id: userId,
    },
    include: {
      partner: true,
    },
  });

  if (!user) {
    throw new Error("User not found");
  }

  const activated = await prisma.user.update({
    where: {
      id: userId,
    },
    data: {
      status: UserStatus.ACTIVE,
    },
  });

  await crmClient.sendRegistrationData(activated.partnerId!, {
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    signupForNewsletter: user.enableNotifications,
    companyName: user.partner?.name || "",
    phoneNumber: user.phoneNumber || "",
    country: user.partner?.country || "",
    jobTitle: user.jobTitle || "",
  });

  return activated;
};

export const provisionUser = async ({
  email,
  firstName,
  lastName,
}: {
  email: string;
  firstName: string;
  lastName: string;
}) => {
  const user = enforce(scope("partner").W)!;
  const createdUser = await createUser({
    firstName,
    lastName,
    email,
    partnerId: user.partnerId!,
    enableNotifications: false,
    jobTitle: "",
    phoneNumber: "",
  });

  await crmClient.addRemoveUser({
    email,
    firstName,
    lastName,
    formType: FormType.USER_ADD,
    partnerId: user.partnerId!,
  });

  return createdUser;
};

export async function updateUser({
  id,
  type,
  status,
  email,
  partnerId,
  firstName,
  lastName,
  phoneNumber,
  jobTitle,
  enableNotifications,
}: {
  id: number;
  type: UserType;
  status: UserStatus;
  email: string;
  partnerId: string | undefined;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  jobTitle: string;
  enableNotifications: boolean;
}) {
  enforce(scope("users").W);

  return PrismaTransactional.execute(async () => {
    await prisma.user.update({
      data: {
        type: type,
        status: status,
        email: email,
        partnerId: partnerId === undefined ? undefined : parseInt(partnerId),
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        jobTitle: jobTitle,
        enableNotifications: enableNotifications,
      },
      where: {
        id,
      },
    });
  });
}

export async function onGetSubscriptions() {
  const subscritions = await getSubscriptionsForCurrentUser();
  return subscritions;
}

//shield(updateUserBaseProperty, {[shield.type.string]: shield.type.any})
export async function updateUserBaseProperty({
  property,
  value,
}: {
  property: string;
  value: any;
}) {
  const user = authService.getCurrentUser();
  if (!user) return;
  await prisma.user.update({
    data: { [property]: value },
    where: { id: user!.id },
  });
}

export async function updateUserProperties(
  updates: Record<string, any>,
  userId?: number
) {
  const currentUser = authService.getCurrentUser();
  if (!currentUser) return;

  if (!userId) {
    userId = currentUser.id;
  }

  if (
    userId === currentUser.id ||
    currentUser.type === UserType.ADMIN ||
    currentUser.type === UserType.SUPER_ADMIN
  ) {
    await prisma.user.update({
      data: updates,
      where: { id: userId },
    });
    return;
  }

  if (currentUser.type === UserType.PARTNER_ADMIN) {
    const targetUser = await prisma.user.findUnique({
      where: { id: userId },
      select: { partnerId: true },
    });

    if (targetUser && targetUser.partnerId === currentUser.partnerId) {
      await prisma.user.update({
        data: updates,
        where: { id: userId },
      });
      return;
    }
  }

  throw new Error("Unauthorized: no permission to update this user");
}

export const getUser = (id: number) => {
  enforce(scope("users").R);
  return prisma.user.findFirst({
    where: { id },
    include: {
      partner: {
        include: {
          subscriptionPackageAssignment: true,
        },
      },
    },
  });
};

export async function getAllUsersForAdmin() {
  const user = authService.getCurrentUser();
  if (!user || user.email !== ADMIN_EMAIL) {
    throw new Error(
      "Unauthorized: Only <EMAIL> can access all users"
    );
  }

  return prisma.user.findMany({
    orderBy: {
      id: "asc",
    },
    include: {
      partner: {
        select: {
          name: true,
        },
      },
    },
  });
}

export async function getAllPartnersForAdmin() {
  const user = authService.getCurrentUser();
  if (!user || user.email !== ADMIN_EMAIL) {
    throw new Error(
      "Unauthorized: Only <EMAIL> can access all partners"
    );
  }

  return prisma.partner.findMany({
    orderBy: {
      id: "asc",
    },
    include: {
      subscriptionPackageAssignment: {
        include: {
          subscriptionPackage: {
            include: {
              subscriptions: true,
            },
          },
        },
      },
    },
  });
}

export async function deleteUser(userId: number) {
  const user = authService.getCurrentUser();
  if (!user || user.email !== ADMIN_EMAIL) {
    throw new Error("Unauthorized: Only <EMAIL> can delete users");
  }

  return PrismaTransactional.execute(async () => {
    const userToDelete = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    if (!userToDelete) {
      throw new Error("User not found");
    }

    await prisma.registration.deleteMany({
      where: {
        email: userToDelete.email,
      },
    });

    await prisma.user.delete({
      where: {
        id: userId,
      },
    });
  });
}

import type { MutationFunction } from "@tanstack/react-query";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import type { AsyncReturnType } from "type-fest";
import {
  type AssertedCurrentUserType,
  createUser,
  deleteUser,
  getAllUsersForAdmin,
  getAllPartnersForAdmin,
  getUser,
  getUsers,
  onGetSubscriptions,
  provisionUser,
  updateUser,
  updateUserBaseProperty,
  updateUserProperties,
  activateUser,
} from "./users.telefunc";

export const useUsers = (partnerId?: number) => {
  return useSuspenseQuery({
    queryKey: ["users", `partnerId_${partnerId}`],
    queryFn: () => getUsers(partnerId),
  });
};

export const useUser = (userId: number) => {
  return useSuspenseQuery({
    queryKey: ["user", userId],
    queryFn: () => getUser(userId)!,
  });
};

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createUser,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

export const useActivateUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: activateUser,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
}

export const useProvisionUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: provisionUser,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateUser,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["users", "user"] });
    },
  });
};

export const useUsersSubscriptions = (userId: number) => {
  return useSuspenseQuery({
    queryFn: () => onGetSubscriptions(),
    queryKey: ["subscriptions", userId],
  });
};

export const useUpdateUserBaseProperty = () => {
  const queryClient = useQueryClient();

  const query = useMutation({
    mutationFn: updateUserBaseProperty as MutationFunction<
      AsyncReturnType<typeof updateUserBaseProperty>,
      {
        property: keyof AssertedCurrentUserType;
        value: AssertedCurrentUserType[keyof AssertedCurrentUserType];
      }
    >,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["getProfile"] });
    },
  });

  return <T extends keyof AssertedCurrentUserType>(
    k: T,
    value: AssertedCurrentUserType[T]
  ) => query.mutate({ property: k, value });
};

export const useUpdateUserProperties = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: { updates: Record<string, any>; userId?: number }) =>
      updateUserProperties(params.updates, params.userId),
    onSettled(_, __, variables) {
      // Always invalidate the profile
      queryClient.invalidateQueries({ queryKey: ["getProfile"] });

      // If updating another user, invalidate that user's data and users list
      if (variables?.userId) {
        queryClient.invalidateQueries({ queryKey: ["user", variables.userId] });
        queryClient.invalidateQueries({ queryKey: ["users"] });
      }
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteUser,
    onSettled() {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["adminUsers"] });
      queryClient.invalidateQueries({ queryKey: ["registrations"] });
    },
  });
};

export const useAdminUsers = () => {
  return useSuspenseQuery({
    queryKey: ["adminUsers"],
    queryFn: () => getAllUsersForAdmin(),
  });
};

export const useAdminPartners = () => {
  return useSuspenseQuery({
    queryKey: ["adminPartners"],
    queryFn: () => getAllPartnersForAdmin(),
  });
};

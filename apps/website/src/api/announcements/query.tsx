import {
  useMutation,
  useQueryClient,
  useSuspenseInfiniteQuery,
  useSuspenseQuery,
} from "@tanstack/react-query";
import type { AnnouncementsFilter } from "#root/src/api/announcements/announcements.telefunc";
import {
  createAnnouncement,
  getAnnouncements,
  getUnreadAnnouncementCount,
  markAllAnnouncementAsRead,
  markAnnouncementAsRead,
} from "#root/src/api/announcements/announcements.telefunc";
import { useEffect, useMemo } from "react";
import { notifications } from "@mantine/notifications";
import { useAppStore } from "#root/src/pages/store";
import type { WsUpdateData } from "#root/src/server/handlers/ws";
import { useWebsocketInstanceStore } from "#root/src/pages/socket.ts";

export const useAnnouncements = (
  operationId?: string,
  overrideFilter?: AnnouncementsFilter
) => {
  const { filter } = useAppStore((s) => s.announcements);
  const queryClient = useQueryClient();

  const _filter = useMemo(() => {
    const baseFilter = overrideFilter || filter;
    return {
      ...baseFilter,
      ...((operationId && { dataSources: [operationId] }) || {}),
    };
  }, [operationId, filter, overrideFilter]);

  const query = useSuspenseInfiniteQuery({
    queryKey: ["announcements", _filter],
    queryFn: ({ pageParam }) => getAnnouncements(pageParam || null, _filter),
    getNextPageParam: (lastPage, pages) => lastPage[lastPage.length - 1]?.id,
    initialPageParam: "",
  });

  return {
    query,
    purge: () => queryClient.refetchQueries({ queryKey: ["announcements"] }),
  };
};

export const useCreateAnnouncement = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createAnnouncement,
    onSettled(data, error, variables, context) {
      queryClient.invalidateQueries({ queryKey: ["announcements"] });
    },
  });
};

export const useSocketSubscription = () => {
  const queryClient = useQueryClient();
  const { socket: websocket } = useWebsocketInstanceStore();
  useEffect(() => {
    websocket.onopen = () => {
      console.log("HUPX Live update socket opened");
    };
    websocket.onerror = (error) =>
      console.error("HUPX Live update error", error);
    websocket.onmessage = (event) => {
      const data = JSON.parse(event.data) as WsUpdateData;
      switch (data.type) {
        case "announcement": {
          const payload = data.payload;
          notifications.show({
            message: `Datasets: ${payload.announcementDsLink
              .slice(0, 3)
              .map((x) => x.dataset)
              .join(", ")}`,
            title: payload.title,
            position: "top-right",
          });
          queryClient.refetchQueries({ queryKey: ["announcements"] });
          queryClient.invalidateQueries({ queryKey: ["unread-announcements"] });
          break;
        }
        default:
          break;
      }
    };
  }, [queryClient, websocket]);
};

export const useUnreadAnnouncements = () => {
  const query = useSuspenseQuery({
    queryKey: ["unread-announcements"],
    queryFn: () => getUnreadAnnouncementCount().then((c) => c.count),
  });

  return {
    query,
  };
};

export const useMarkAsRead = () => {
  const queryClient = useQueryClient();
  const markAsRead = useMutation({
    mutationFn: markAnnouncementAsRead,
    onSettled(data, error, variables, context) {
      queryClient.invalidateQueries({ queryKey: ["unread-announcements"] });
      queryClient.refetchQueries({ queryKey: ["announcements"] });
    },
  });
  const markAllAsRead = useMutation({
    mutationFn: markAllAnnouncementAsRead,
    onSettled(data, error, variables, context) {
      queryClient.invalidateQueries({ queryKey: ["unread-announcements"] });
      queryClient.refetchQueries({ queryKey: ["announcements"] });
    },
  });

  return {
    markAsRead,
    markAllAsRead,
  };
};

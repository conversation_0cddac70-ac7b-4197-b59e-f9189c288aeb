import { authService, enforce } from "#root/src/server/auth/auth-service";
import { prisma } from "#root/src/server/db/prisma";
import type { AnnouncementType } from "#root/src/shared/constants";
import sanitizeHtml from "sanitize-html";
import type { AsyncReturnType } from "type-fest";
import { scope } from "#root/src/shared/rbac.ts";
import { emailService } from "#root/src/server/mailer";
import { PrismaTransactional } from "@myfunc/prisma-transactional";

export type AnnouncementsFilter = Partial<{
  textContent: string;
  dataSources: string[];
  apiEndpoint: string;
  dateRange: [string | null, string | null];
  type: AnnouncementType[] | null;
}>;

export type Announcement = AsyncReturnType<typeof getAnnouncements>;

export async function getAnnouncements(
  cursor: string | null = null,
  filter: AnnouncementsFilter = {}
) {
  if (filter.dataSources?.length === 0) filter.dataSources = undefined;
  const user = authService.getCurrentUser();
  const fullDataSourceFilter = filter.dataSources || ["WILDCARD"];

  return prisma.announcement
    .findMany({
      orderBy: { createdAt: "desc" },
      where: {
        ...(filter.dateRange?.every((x) => !!x)
          ? {
              createdAt: {
                gte: new Date(filter.dateRange[0]!),
                lte: new Date(filter.dateRange[1]!),
              },
            }
          : {}),
        ...(filter.type && filter.type?.length !== 0
          ? { type: { in: filter.type } }
          : {}),
        ...(filter.apiEndpoint ? { endpoint: filter.apiEndpoint } : {}),
        ...(filter.textContent
          ? {
              OR: [
                {
                  message: {
                    contains: filter.textContent,
                    mode: "insensitive",
                  },
                },
                {
                  title: {
                    contains: filter.textContent,
                    mode: "insensitive",
                  },
                },
              ],
            }
          : {}),
        announcementDsLink: {
          some: { OR: fullDataSourceFilter.map((x) => ({ dataset: x })) },
        },
      },
      include: {
        user: true,
        announcementDsLink: true,
        announcementReadReceipt: { where: { userId: user?.id } },
      },
      take: 50,
      ...(cursor && {
        skip: 1,
        cursor: {
          id: cursor,
        },
      }),
    })
    .then((r) =>
      r.map((a) => ({
        ...a,
        announcementDsLink: a.announcementDsLink.filter(
          (ds) => ds.dataset !== "WILDCARD"
        ),
      }))
    );
}

export const createAnnouncement = async ({
  type,
  title,
  message,
  datasets,
  apiEndpoint,
}: {
  title: string;
  type: AnnouncementType;
  message: string;
  datasets: string[];
  apiEndpoint: string | undefined;
}) => {
  const user = enforce(scope("changelog").W)!;
  await PrismaTransactional.execute(async () => {
    const currentCountByTypeYear = await prisma.announcement.count({
      where: {
        type,
        createdAt: {
          gte: new Date(`${new Date().getFullYear()}-01-01`),
        },
      },
    });

    const displayId = `${type} - ${new Date().getFullYear()}/${
      currentCountByTypeYear + 1
    }`;
    const message_ = sanitizeHtml(message);
    const title_ = title.slice(0, 250);
    const announcement = await prisma.announcement.create({
      data: {
        apiEndpoint,
        type,
        title: title_,
        message: message_,
        user: { connect: { id: user.id } },
        displayId,
        announcementDsLink: {
          createMany: {
            data: [...datasets, "WILDCARD"].map((ds) => ({
              dataset: ds,
            })),
          },
        },
      },
    });

    await markAnnouncementAsRead({ announcementIds: [announcement.id] });

    const usersToNotify = await prisma.user.findMany({
      where: {
        enableNotifications: true,
      },
      select: {
        id: true,
        email: true,
      },
    });
    const emails = usersToNotify.map((u) => u.email);

    await emailService.sendAnnouncementEmail(message, emails, title_);
  });
};

export const markAnnouncementAsRead = async ({
  announcementIds,
}: {
  announcementIds: string[];
}) => {
  const user = authService.getCurrentUser();
  if (!user) return;

  // TODO: 1 query
  for (const announcementId of announcementIds) {
    await prisma.announcementReadReceipt.create({
      data: {
        user: {
          connect: {
            id: user!.id,
          },
        },
        announcement: {
          connect: {
            id: announcementId,
          },
        },
      },
    });
  }
};

export const markAllAnnouncementAsRead = async () => {
  const user = authService.getCurrentUser();
  if (!user) return;
  const allAnnouncements = await prisma.announcement.findMany({});

  // TODO: 1 query
  for (const { id } of allAnnouncements) {
    await prisma.announcementReadReceipt.create({
      data: {
        user: {
          connect: {
            id: user!.id,
          },
        },
        announcement: {
          connect: {
            id: id,
          },
        },
      },
    });
  }
};

export const getUnreadAnnouncementCount = async () => {
  const user = authService.getCurrentUser();
  if (!user) return { count: 0 };

  const actualCount = await prisma.announcement.count({
    where: {
      createdBy: { not: user.id },
      announcementReadReceipt: {
        none: { userId: user.id },
      },
    },
  });

  return { count: actualCount };
};

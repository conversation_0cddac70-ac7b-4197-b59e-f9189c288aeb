import dayjs from "dayjs";

/**
 * Template format for relative dates:
 * {-86400000} for 1 day ago (in milliseconds)
 * {+3600000} for 1 hour in the future (in milliseconds)
 * {0} for the current time
 * 
 * All time offsets are in milliseconds for consistency.
 */

const DATE_FORMAT = 'YYYY-MM-DD';
const DATETIME_FORMAT = 'YYYY-MM-DDTHH:mm:ss';

/**
 * Parses a filter string and extracts individual filters
 */
export function parseFilterString(filterString: string): string[] {
  if (!filterString) {
    return [];
  }
  return filterString.split(",").filter(Boolean);
}

/**
 * Checks if a filter is a date filter
 */
export function isDateFilter(filter: string): boolean {
  // Match both date (YYYY-MM-DD) and datetime (YYYY-MM-DDTHH:mm:ss) formats
  const dateRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__(\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2})?)/;
  return dateRegex.test(filter);
}

/**
 * Checks if a filter contains a datetime with time component
 */
export function hasTimeComponent(filter: string): boolean {
  const datetimeRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/;
  return datetimeRegex.test(filter);
}

/**
 * Checks if a filter is using a relative date template
 */
export function isRelativeDateFilter(filter: string): boolean {
  // Match patterns like {-123456}, {+123456}, {0}
  const templateRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__\{([+-]?\d+)\}/;
  return templateRegex.test(filter);
}

/**
 * Converts a single date filter to a template format
 */
export function convertFilterToTemplate(filter: string): string {
  if (!isDateFilter(filter)) {
    return filter;
  }

  const dateRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__(\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2})?)/;
  const match = filter.match(dateRegex);

  if (match) {
    const [, field, operator, dateStr] = match;
    
    // Convert the date string to a Date object
    const date = dayjs(dateStr);
    
    // Calculate the difference from now in milliseconds
    const now = dayjs();
    
    // Special case: if the date is exactly the same (including time if present), use {0}
    const hasTime = hasTimeComponent(filter);
    const format = hasTime ? DATETIME_FORMAT : DATE_FORMAT;
    
    if (date.format(format) === now.format(format)) {
      return `${field}__${operator}__{0}`;
    }
    
    // Calculate the difference in milliseconds
    const diffMs = now.diff(date);
    
    // Create the template string with millisecond offset
    if (diffMs > 0) {
      return `${field}__${operator}__{-${diffMs}}`;
    } else {
      return `${field}__${operator}__{+${-diffMs}}`;
    }
  }
  
  return filter;
}

/**
 * Converts a single template filter to an absolute date
 */
export function convertTemplateToAbsolute(filter: string): string {
  if (!isRelativeDateFilter(filter)) {
    return filter;
  }

  const templateRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__\{([+-]?\d+)\}/;
  const match = filter.match(templateRegex);

  if (match) {
    const [, field, operator, offsetStr] = match;
    
    // Parse the offset
    let offsetMs = 0;
    if (offsetStr !== '0') {
      offsetMs = parseInt(offsetStr, 10);
    }
    
    // Get the current time and apply the offset
    const now = dayjs();
    let date = now;
    
    if (offsetMs > 0 && offsetStr.startsWith('+')) {
      // Future date
      date = now.add(offsetMs, 'millisecond');
    } else if (offsetMs < 0 || (offsetMs > 0 && offsetStr.startsWith('-'))) {
      // Past date
      const absValue = Math.abs(offsetMs);
      date = now.subtract(absValue, 'millisecond');
    }
    
    // Determine if we should include time component based on the operator
    let format = DATE_FORMAT;
    
    // For operators like gte and lt, we often want to include time for precise ranges
    if (operator === "gte" || operator === "lt") {
      format = DATETIME_FORMAT;
    }
    
    // Format the date
    const formattedDate = date.format(format);
    
    return `${field}__${operator}__${formattedDate}`;
  }
  
  return filter;
}

/**
 * Processes a filter string, replacing any date templates with actual dates
 */
export function processDateTemplates(filterString: string): string {
  if (!filterString) {
    return filterString;
  }

  // Split the filter string into individual filters
  const filters = parseFilterString(filterString);

  // Process each filter
  const processedFilters = filters.map(convertTemplateToAbsolute);

  return processedFilters.join(",");
}

/**
 * Extracts the field name from a filter string
 */
export function getFieldFromFilter(filter: string): string {
  const parts = filter.split("__");
  return parts[0];
}

/**
 * Gets a human-readable description of a date filter
 */
export function getDateFilterDescription(filter: string): string {
  if (isRelativeDateFilter(filter)) {
    const templateRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__\{([+-]?\d+)\}/;
    const match = filter.match(templateRegex);
    
    if (match) {
      const [, field, operator, offsetStr] = match;
      
      let description = `${field} `;
      
      switch (operator) {
        case "gte": description += "≥ "; break;
        case "lt": description += "< "; break;
        case "gt": description += "> "; break;
        case "lte": description += "≤ "; break;
        case "eq": description += "= "; break;
        case "neq": description += "≠ "; break;
      }
      
      if (offsetStr === '0') {
        description += "Current time";
      } else {
        const offsetMs = parseInt(offsetStr, 10);
        const absMs = Math.abs(offsetMs);
        
        // Convert to a more readable format for display only
        const days = Math.round(absMs / (1000 * 60 * 60 * 24));
        
        if (offsetStr.startsWith('+') || (offsetMs > 0 && !offsetStr.startsWith('-'))) {
          description += `Current time + ${days} days`;
        } else {
          description += `Current time - ${days} days`;
        }
      }
      
      return description;
    }
  } else if (isDateFilter(filter)) {
    const dateRegex = /^(.+)__(gte|lt|gt|lte|eq|neq)__(\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2})?)/;
    const match = filter.match(dateRegex);
    
    if (match) {
      const [, field, operator, dateStr] = match;
      // Use the appropriate format based on whether the date has a time component
      const format = dateStr.includes('T') ? DATETIME_FORMAT : DATE_FORMAT;
      const date = dayjs(dateStr).format(format);
      
      let description = `${field} `;
      
      switch (operator) {
        case "gte": description += "≥ "; break;
        case "lt": description += "< "; break;
        case "gt": description += "> "; break;
        case "lte": description += "≤ "; break;
        case "eq": description += "= "; break;
        case "neq": description += "≠ "; break;
      }
      
      description += date;
      return description;
    }
  }
  
  return filter;
}

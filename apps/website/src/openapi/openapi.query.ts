import {
  generateCSharpStub,
  generateCurlStub,
  generateJavaStub,
  generatePhpStub,
  generatePythonStub,
  generateStubJavaScriptCodeFromUrl,
} from "#root/src/openapi/codegen";
import type { UseQueryResult } from "@tanstack/react-query";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import type { OpenAPIV3_1 } from "openapi-types";
import type { Operation, ParseDefReturnType } from "./openapi";
import { useMemo } from "react";
import ky from "ky";
import { parse } from "devalue";
import { getPublicGlobalConfig } from "../shared/globalConfig";
import { usePageContext } from "vike-react/usePageContext";

export const getApiData = async (url: string, embeddingId?: string) => {
  const headers = {
    ...(embeddingId !== undefined && { "x-embedding-id": embeddingId }),
  };
  const res = await ky.get("/data" + url, { headers });
  const json = await res.json<any>();
  json.data = json.data || [];
  return json;
};

export const useOpenApi = () => {
  const methods = useSuspenseQuery({
    queryKey: ["getOpenapiSchema"],
    queryFn: async () => {
      const res = await ky.get("/openapi.json").text();
      return parse(res) as ParseDefReturnType;
    },
  });
  return methods;
};

export const useOperation = ({ operationId }: { operationId: string }) => {
  const openapi = useOpenApi();
  const operation = openapi.data.operations.find(
    (item) => item.operationId === operationId
  );
  return operation as Operation;
};

export const useApiData = <
  T extends {
    data: any[];
  }
>({
  operationId,
  params,
}: {
  operationId: string;
  params?: any;
}) => {
  if (import.meta.env.SSR) {
    throw new Error(
      "useApiData should not be used on the server because it returns huge amounts of data and we don't want to SSR that"
    );
  }

  const operation = useOperation({ operationId });
  const url = constructUrl(operation, params);

  const ctx = usePageContext();
  const embedId = ctx.routeParams!["embeddingId"];

  const data = useSuspenseQuery<T>({
    queryKey: ["getApiData", url],
    queryFn: () => getApiData(url, embedId),
  });

  return data;
};

export const constructUrlForCsv = (operation: Operation, params: any) => {
  const url = constructUrl(operation, params, true);
  return `/csv${url}`;
};

export const constructUrlForXlsx = (operation: Operation, params: any) => {
  const url = constructUrl(operation, params, false, true);
  return `/xlsx${url}`;
};

export const useNonSuspenseApiData = ({
  operationId,
  params,
  generateCode,
}: {
  operationId: string;
  params?: any;
  generateCode: boolean;
}): [
  UseQueryResult<{ data: any[] }, Error>,
  Record<string, [string, string]>
] => {
  const operation = useOperation({ operationId });
  const url = constructUrl(operation, params);
  const apiBase = getPublicGlobalConfig().PUBLIC_URL + "/api";

  const query = useQuery({
    queryKey: ["getApiDataNonSuspense", url],
    queryFn: () => getApiData(url),
    gcTime: 0,
    enabled: false,
  });
  const code = useMemo(
    () =>
      generateCode
        ? {
            JavaScript: [
              generateStubJavaScriptCodeFromUrl(url, apiBase),
              "javascript",
            ] as [string, string],
            Python: [generatePythonStub(url, apiBase), "python"] as [
              string,
              string
            ],
            CSharp: [generateCSharpStub(url, apiBase), "csharp"] as [
              string,
              string
            ],
            Java: [generateJavaStub(url, apiBase), "java"] as [string, string],
            cURL: [generateCurlStub(url, apiBase), "bash"] as [string, string],
            PHP: [generatePhpStub(url, apiBase), "php"] as [string, string],
          }
        : ({} as Record<string, [string, string]>),
    [params, operation, url, generateCode]
  );

  return [query, code] as const;
};

export const useOperations = () => {
  const api = useOpenApi();
  const operations = api.data.operations.map((item) => ({
    id: item.operationId!,
  }));
  return operations;
};

export const constructUrl = (
  operation: Operation,
  params: {
    [key: string]: string | number | string[] | number[] | Date[];
  } = {},
  isCsv = false,
  isXlsx = false
) => {
  let _url = operation.url;
  const requiredParams = new Set(operation.requiredParams);
  for (const pathParam of operation.pathParams) {
    const param = pathParam as OpenAPIV3_1.ParameterObject;
    if (param.name in params) {
      let encoded = params[param.name];
      if (Array.isArray(encoded)) {
        encoded = encoded.join(",");
      }

      _url = _url.replace(`{${param.name}}`, encodeURIComponent(encoded));

      requiredParams.delete(param.name);
    }
  }

  let _query = "";

  for (const queryParam of operation.queryParams) {
    const param = queryParam as OpenAPIV3_1.ParameterObject;
    if (param.name in params) {
      let encoded = params[param.name];
      if (Array.isArray(encoded)) {
        encoded = encoded.join(",");
      }

      _query += `${_query ? "&" : ""}${param.name}=${encoded}`;
      requiredParams.delete(param.name);
    }
  }

  if (requiredParams.size) {
    throw new Error("Missing required params");
  }

  if (isCsv) {
    _url += "/csv";
  }

  if (isXlsx) {
    _url += "/xlsx";
  }

  if (_query) {
    _url += `?${_query}`;
  }

  return _url;
};

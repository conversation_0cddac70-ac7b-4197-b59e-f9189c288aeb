import { uniq, uniqBy } from "lodash-es";
import SwaggerParser from "@apidevtools/swagger-parser";
import * as OpenAPISampler from "openapi-sampler";
import type { OpenAPIV3_1 } from "openapi-types";
import type { AsyncReturnType } from "type-fest";
import { json2xml } from "xml-js";

export type Operation = AsyncReturnType<typeof parseDef>["operations"][0];
export type ParseDefReturnType = AsyncReturnType<typeof parseDef>;
type ColumnExtensions = {
  unit: string;
};

export const parseDef = async (def: any) => {
  const api = await SwaggerParser.validate(def);
  const paths = Object.entries(api.paths!).map(
    ([url, path]: [string, OpenAPIV3_1.PathItemObject]) => {
      const operations = Object.entries(path).map(
        //@ts-ignore
        ([method, operation]: [string, OpenAPIV3_1.OperationObject]) => {
          const responses = Object.entries(
            operation.responses as OpenAPIV3_1.ResponsesObject
          ).map(([statusCode, res]) => {
            const jsonResponseSchema = (res as OpenAPIV3_1.ResponseObject)
              ?.content?.["application/json"]?.schema;
            const xmlResponseSchema = (res as OpenAPIV3_1.ResponseObject)
              ?.content?.["application/json"]?.schema;

            const openApiJsonSample =
              jsonResponseSchema &&
              (OpenAPISampler.sample(jsonResponseSchema as any) as any);

            const sampled =
              jsonResponseSchema && JSON.stringify(openApiJsonSample, null, 2);

            const openApiXmlSample =
              xmlResponseSchema &&
              (OpenAPISampler.sample(xmlResponseSchema as any) as any);
            const jsonForXmlJs = xmlResponseSchema && {
              elements: [
                {
                  type: "element",
                  name: "root",
                  elements: [
                    {
                      type: "element",
                      name: "data",
                      elements: openApiXmlSample.data.map((item) => ({
                        type: "element",
                        name: "item",
                        elements: Object.entries(item).map(([key, value]) => ({
                          type: "element",
                          name: key,
                          elements: [{ type: "text", text: String(value) }],
                        })),
                      })),
                    },
                    {
                      type: "element",
                      name: "message",
                      elements: [
                        { type: "text", text: openApiXmlSample.message },
                      ],
                    },
                  ],
                },
              ],
            };

            const xmlSample =
              xmlResponseSchema &&
              // @ts-expect-error this doesnt need a string
              json2xml(jsonForXmlJs, { compact: false, spaces: 4 });

            return {
              ...res,
              statusCode,
              ok: statusCode === "200",
              sample: sampled,
              xmlSample,
            };
          });
          const requiredParams = new Set(
            Object.values(operation.parameters || [])
              .filter(
                //@ts-ignore
                (param) => param.required
              )
              .map(
                //@ts-ignore
                (param) => param.name
              )
          );

          const pathParams = Object.values(operation.parameters || []).filter(
            //@ts-ignore
            (param) => param.in === "path"
          ) as OpenAPIV3_1.ParameterObject[];
          const queryParams = Object.values(operation.parameters || []).filter(
            //@ts-ignore
            (param) => param.in === "query"
          ) as OpenAPIV3_1.ParameterObject[];

          const hasParams = !!operation.parameters?.length;

          const hasDateRangeParam = ["from_epoch", "to_epoch"].every((param) =>
            pathParams.find(
              //@ts-ignore
              (p) => p.name === param
            )
          );

          const hasColumnsParam = !!queryParams.find(
            //@ts-ignore
            (p) => p.name === "columns"
          );

          const hasOffsetParam = !!queryParams.find(
            //@ts-ignore
            (p) => p.name === "offset"
          );

          const hasLimitParam = !!queryParams.find(
            //@ts-ignore
            (p) => p.name === "limit"
          );

          const parameters =
            operation.parameters as OpenAPIV3_1.ParameterObject[];

          const OKresponse = responses.find(
            //@ts-ignore
            (res) => res.statusCode === "200"
          ) as OpenAPIV3_1.ResponseObject;

          const xPlot: {
            group?: string;
            type?: string;
            separate?: boolean;
            x?: string;
            y?: string;
            titles?: { [key: string]: string };
          } =
            //@ts-ignore
            // OKresponse?.content?.["application/json"]?.schema?.properties?.data
            //   ?.items["x-plot"] || {};
            operation["x-plot"] || {};

          const type = xPlot?.type;
          const separate = xPlot?.separate;

          const xPlotNames = [xPlot.group, xPlot.x, xPlot.y].filter(
            (i) => !!i
          ) as string[];

          const OkResponseItems =
            //@ts-expect-error shut it
            OKresponse?.content?.["application/json"]?.schema?.properties?.data
              ?.items.properties as { [key: string]: OpenAPIV3_1.SchemaObject };

          const columns = OkResponseItems
            ? Object.entries(OkResponseItems)
                .map(([key, value]) => {
                  const a = {
                    name: key,
                    title: xPlot.titles?.[key] || key,
                    ...value,
                  };

                  return a as typeof a & ColumnExtensions;
                })
                //@ts-ignore
                .sort((a, b) => a.order - b.order)
            : [];

          const allowedFilters = operation["x-data-filters"] || {};
          const filterOperators = ["eq", "neq", "gt", "gte", "lt", "lte"];
          const filters =
            columns.flatMap((value) => {
              const filtersAllowedForColumn = allowedFilters[value.name] || [];
              return filtersAllowedForColumn.map((op: string) => {
                return {
                  name: `${value.name}__${op}`,
                  title: value.title,
                  op,
                  column: value.name,
                };
              });
            }) || [];

          const plotItems = columns.filter((value) => {
            return xPlotNames.includes(value.name);
          });

          const plotCols = plotItems.map((value) => {
            return value.name;
          });

          const x = xPlot.x;

          const y = xPlot.y?.split(",");

          const groupBy = plotItems.find((value) => {
            return value.name === xPlot.group;
          });

          const plotConfig = plotItems.length
            ? {
                x,
                y,
                groupBy,
                plotItems,
                type,
                separate,
              }
            : null;

          const dataViewUrlName = operation["x-data-view-url-name"] as
            | string
            | undefined;

          const name = operation["x-name"] as string | undefined;
          const hasPublicSubscription = operation[
            "x-has-public-subscription"
          ] as boolean | undefined;

          return {
            ...operation,
            name,
            hasPublicSubscription,
            requiredParams,
            plotConfig,
            plotCols,
            columns,
            parameters,
            queryParams,
            pathParams,
            // constructUrl,
            responses,
            method,
            url,
            hasDateRangeParam,
            hasColumnsParam,
            hasOffsetParam,
            hasLimitParam,
            hasParams,
            OkResponseItems,
            filterOperators,
            filters,
            dataViewUrlName,
          };
        }
      );

      return {
        url,
        operations,
        path,
      };
    }
  );

  const operations = paths.flatMap((path) => path.operations);

  // TODO: move this hardcode sorting to config..
  const tagsOrder = [
    {
      tag: "Prices and indices",
      operations: [],
    },
    {
      tag: "Day-Ahead Market",
      operations: ["Aggregated trading data", "Aggregated curves", "MC flows"],
    },
    {
      tag: "Intraday Continuous Market",
      operations: [
        "Aggregated trading data - H",
        "Aggregated trading data - QH",
        "Trades",
      ],
    },
    {
      tag: "Intraday Auction Market",
      operations: ["Aggregated trading data"],
    },
    {
      tag: "Inside information platform",
      operations: ["Publications"],
    },
  ];

  const tags = uniq(
    operations.flatMap((op) => op.tags).filter((t) => !!t)
  ).sort((a, b) => a!.localeCompare(b!));

  // Sort tags based on tagsOrder
  const sortedTags = tags.sort((a, b) => {
    const aIndex = tagsOrder.findIndex((t) => t.tag === a);
    const bIndex = tagsOrder.findIndex((t) => t.tag === b);

    // If both tags are in tagsOrder, sort by their order
    if (aIndex !== -1 && bIndex !== -1) {
      return aIndex - bIndex;
    }
    // If only one tag is in tagsOrder, prioritize it
    if (aIndex !== -1) return -1;
    if (bIndex !== -1) return 1;
    // For tags not in tagsOrder, sort alphabetically
    return a!.localeCompare(b!);
  });

  const groupedOperations: {
    group: (typeof sortedTags)[0];
    operations: typeof operations;
  }[] = [];

  for (const tag of sortedTags) {
    const tagConfig = tagsOrder.find((t) => t.tag === tag);
    const groupOps = operations.filter((op) => op.tags?.includes(tag!));

    if (tagConfig) {
      // Sort operations according to tagsOrder configuration
      groupOps.sort((a, b) => {
        const aIndex = tagConfig.operations.indexOf(a.summary || "");
        const bIndex = tagConfig.operations.indexOf(b.summary || "");

        if (aIndex !== -1 && bIndex !== -1) {
          return aIndex - bIndex;
        }
        if (aIndex !== -1) return -1;
        if (bIndex !== -1) return 1;
        return (a.summary || "").localeCompare(b.summary || "");
      });
    }

    groupedOperations.push({ group: tag, operations: groupOps });
  }

  const uniqueOperations = uniqBy(
    uniqBy(operations, (op) => op.operationId),
    (op) => op.summary
  );

  return {
    api,
    paths,
    operations: uniqueOperations,
    groupedOperations,
  };
};

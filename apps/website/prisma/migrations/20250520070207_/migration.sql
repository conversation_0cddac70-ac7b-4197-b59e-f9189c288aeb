/*
  Warnings:

  - You are about to drop the `SubscriptionAssignment` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `dataSetOperationId` to the `Embedding` table without a default value. This is not possible if the table is not empty.
  - Added the required column `token` to the `Embedding` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "SubscriptionAssignment" DROP CONSTRAINT "SubscriptionAssignment_partnerId_fkey";

-- DropForeignKey
ALTER TABLE "SubscriptionAssignment" DROP CONSTRAINT "SubscriptionAssignment_subscriptionId_fkey";

-- AlterTable
ALTER TABLE "Embedding" ADD COLUMN     "dataSetOperationId" TEXT NOT NULL,
ADD COLUMN     "token" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "Partner" ALTER COLUMN "status" SET DEFAULT 'NONE';

-- AlterTable
ALTER TABLE "Registration" ADD COLUMN     "contractUserId" INTEGER;

-- DropTable
DROP TABLE "SubscriptionAssignment";

-- CreateTable
CREATE TABLE "MainPartnerContact" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "partnerId" INTEGER NOT NULL,
    "userId" INTEGER,
    "companyName" TEXT NOT NULL,
    "enableNotifications" BOOLEAN NOT NULL DEFAULT true,
    "firstName" TEXT NOT NULL,
    "lastName" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "country" TEXT,
    "phoneNumber" TEXT,
    "jobTitle" TEXT,

    CONSTRAINT "MainPartnerContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionPackageAssignment" (
    "id" TEXT NOT NULL,
    "partnerId" INTEGER NOT NULL,
    "subscriptionPackageId" TEXT NOT NULL,
    "validUntil" TIMESTAMP(3) NOT NULL,
    "deleteNextCycle" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "SubscriptionPackageAssignment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionPackage" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "priceEur" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SubscriptionPackage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "SubscriptionToPackage" (
    "id" TEXT NOT NULL,
    "subscriptionId" TEXT NOT NULL,
    "subscriptionPackageId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "SubscriptionToPackage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Contract" (
    "id" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "type" TEXT NOT NULL DEFAULT 'NEW_ORDER',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "partnerId" INTEGER NOT NULL,
    "createdById" INTEGER NOT NULL,
    "companyName" TEXT NOT NULL,
    "companyTaxNumber" TEXT NOT NULL,
    "companyRegistrationNumber" TEXT NOT NULL,
    "companyAddress" TEXT NOT NULL,
    "companyCity" TEXT NOT NULL,
    "companyZipCode" TEXT NOT NULL,
    "companyCountry" TEXT NOT NULL,
    "companyAuthorizedRepName" TEXT NOT NULL,
    "companyAdditionalRepName" TEXT,
    "companyCommencementDate" TEXT NOT NULL,
    "companyInvoiceEmail" TEXT NOT NULL,
    "billingIsSameAsCompanyAddress" BOOLEAN NOT NULL DEFAULT true,
    "billingName" TEXT,
    "billingAddress" TEXT,
    "billingCity" TEXT,
    "billingZipCode" TEXT,
    "billingCountry" TEXT,
    "mainContactFirstName" TEXT,
    "mainContactLastName" TEXT,
    "mainContactPosition" TEXT,
    "mainContactPhone" TEXT,
    "mainContactEmail" TEXT,

    CONSTRAINT "Contract_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContractPackage" (
    "id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "price" DOUBLE PRECISION NOT NULL,
    "name" TEXT NOT NULL,
    "packageId" TEXT NOT NULL,
    "contractId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ContractPackage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContractSecondaryContact" (
    "id" TEXT NOT NULL,
    "firstName" TEXT,
    "lastName" TEXT,
    "position" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "contractId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ContractSecondaryContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ContractAffiliatedCompany" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "taxNumber" TEXT NOT NULL,
    "companyRegistration" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "city" TEXT NOT NULL,
    "zipCode" TEXT NOT NULL,
    "country" TEXT NOT NULL,
    "contractId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ContractAffiliatedCompany_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MainPartnerContact_email_key" ON "MainPartnerContact"("email");

-- CreateIndex
CREATE UNIQUE INDEX "SubscriptionToPackage_subscriptionId_subscriptionPackageId_key" ON "SubscriptionToPackage"("subscriptionId", "subscriptionPackageId");

-- AddForeignKey
ALTER TABLE "Registration" ADD CONSTRAINT "Registration_contractUserId_fkey" FOREIGN KEY ("contractUserId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MainPartnerContact" ADD CONSTRAINT "MainPartnerContact_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "MainPartnerContact" ADD CONSTRAINT "MainPartnerContact_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionPackageAssignment" ADD CONSTRAINT "SubscriptionPackageAssignment_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionPackageAssignment" ADD CONSTRAINT "SubscriptionPackageAssignment_subscriptionPackageId_fkey" FOREIGN KEY ("subscriptionPackageId") REFERENCES "SubscriptionPackage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionToPackage" ADD CONSTRAINT "SubscriptionToPackage_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SubscriptionToPackage" ADD CONSTRAINT "SubscriptionToPackage_subscriptionPackageId_fkey" FOREIGN KEY ("subscriptionPackageId") REFERENCES "SubscriptionPackage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contract" ADD CONSTRAINT "Contract_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contract" ADD CONSTRAINT "Contract_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContractPackage" ADD CONSTRAINT "ContractPackage_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "Contract"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContractSecondaryContact" ADD CONSTRAINT "ContractSecondaryContact_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "Contract"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContractAffiliatedCompany" ADD CONSTRAINT "ContractAffiliatedCompany_contractId_fkey" FOREIGN KEY ("contractId") REFERENCES "Contract"("id") ON DELETE CASCADE ON UPDATE CASCADE;

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
}

model User {
  id                      Int                       @id @default(autoincrement())
  createdAt               DateTime                  @default(now())
  updatedAt               DateTime                  @default(now()) @updatedAt
  firstName               String
  lastName                String
  email                   String                    @unique
  status                  String
  type                    String
  partner                 Partner?                  @relation(fields: [partnerId], references: [id])
  enableNotifications     Boolean                   @default(true)
  partnerId               Int?
  apiKey                  ApiKey[]
  Embedding               Embedding[]
  phoneNumber             String?
  jobTitle                String?
  password                String
  passwordUpdatedAt       DateTime                  @default(now())
  lastOnline              DateTime
  passwordReset           PasswordReset[]
  Announcement            Announcement[]
  AnnouncementReadReceipt AnnouncementReadReceipt[]
  AuditLogEvent           AuditLogEvent[]

  MainPartnerContact MainPartnerContact[]

  Registration     Registration[]
  createdContracts Contract[]
}

model Partner {
  id                            Int                             @id @default(autoincrement())
  createdAt                     DateTime                        @default(now())
  updatedAt                     DateTime                        @default(now()) @updatedAt
  country                       String?
  users                         User[]
  name                          String
  status                        String                          @default("NONE")
  subscriptionPackageAssignment SubscriptionPackageAssignment[]
  contracts                     Contract[]

  MainPartnerContact MainPartnerContact[]
}

model Registration {
  id                  Int      @id @default(autoincrement())
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now()) @updatedAt
  companyName         String
  enableNotifications Boolean  @default(true)
  firstName           String
  lastName            String
  email               String   @unique
  country             String?
  phoneNumber         String?
  jobTitle            String?
  contractUserId      Int?
  user                User?    @relation(fields: [contractUserId], references: [id])
}

model MainPartnerContact {
  id                  Int      @id @default(autoincrement())
  createdAt           DateTime @default(now())
  updatedAt           DateTime @default(now()) @updatedAt
  partnerId           Int
  userId              Int?
  companyName         String
  enableNotifications Boolean  @default(true)
  firstName           String
  lastName            String
  email               String   @unique
  country             String?
  phoneNumber         String?
  jobTitle            String?
  partner             Partner  @relation(fields: [partnerId], references: [id])
  user                User?    @relation(fields: [userId], references: [id])
}

model PasswordReset {
  id        String    @id @default(cuid())
  email     String
  token     String    @unique
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  usedAt    DateTime?
  user      User?     @relation(fields: [userId], references: [id])
  userId    Int?
}

model ApiKey {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  expiresAt DateTime
  name      String
  value     String
  user      User     @relation(fields: [userId], references: [id])
  userId    Int
}

model Embedding {
  id                      String                    @id @default(cuid())
  user                    User                      @relation(fields: [userId], references: [id])
  userId                  Int
  createdAt               DateTime                  @default(now())
  name                    String
  dataSet                 String
  dataSetOperationId      String
  apiParams               String?
  dateRange               String?
  includeTable            Boolean
  includeChart            Boolean
  EmbeddingsAllowedOrigin EmbeddingsAllowedOrigin[]
  token                   String
}

model EmbeddingsAllowedOrigin {
  id          String    @id @default(cuid())
  embeddingId String
  embedding   Embedding @relation(fields: [embeddingId], references: [id])
  origin      String
  enabled     Boolean
  createdAt   DateTime  @default(now())
}

model EmailTemplate {
  id        String   @id @default(cuid())
  name      String
  subject   String
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model DataRequest {
  id          String   @id @default(cuid())
  name        String
  email       String
  phone       String?
  companyName String
  message     String
  createdAt   DateTime @default(now())
  datasetType String
  datasetName String?
}

model Subscription {
  id               String                  @id
  dataSetName      String
  subscriptionName String
  createdAt        DateTime
  packages         SubscriptionToPackage[]
}

model SubscriptionPackageAssignment {
  id                    String              @id @default(cuid())
  partner               Partner             @relation(fields: [partnerId], references: [id])
  partnerId             Int
  subscriptionPackage   SubscriptionPackage @relation(fields: [subscriptionPackageId], references: [id])
  subscriptionPackageId String
  validUntil            DateTime
  deleteNextCycle       Boolean             @default(false)
  deletedAt             DateTime?
}

model SubscriptionPackage {
  id                            String                          @id @default(uuid())
  name                          String
  priceEur                      Int
  createdAt                     DateTime                        @default(now())
  updatedAt                     DateTime                        @default(now())
  SubscriptionPackageAssignment SubscriptionPackageAssignment[]
  subscriptions                 SubscriptionToPackage[]
  ContractPackage               ContractPackage[]
}

model SubscriptionToPackage {
  id                    String              @id @default(uuid())
  subscription          Subscription        @relation(fields: [subscriptionId], references: [id])
  subscriptionId        String
  subscriptionPackage   SubscriptionPackage @relation(fields: [subscriptionPackageId], references: [id])
  subscriptionPackageId String
  createdAt             DateTime            @default(now())

  @@unique([subscriptionId, subscriptionPackageId])
}

model Announcement {
  id                      String                    @id @default(cuid())
  displayId               String
  type                    String
  apiEndpoint             String?
  message                 String
  title                   String
  createdAt               DateTime                  @default(now())
  createdBy               Int
  user                    User                      @relation(fields: [createdBy], references: [id])
  announcementDsLink      AnnouncementDsLink[]
  announcementReadReceipt AnnouncementReadReceipt[]
}

model AnnouncementDsLink {
  id             String @id @default(cuid())
  announcementId String
  dataset        String

  announcement Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)
}

model AnnouncementReadReceipt {
  id             String       @id @default(cuid())
  userId         Int
  announcementId String
  timestamp      DateTime     @default(now())
  user           User         @relation(fields: [userId], references: [id])
  announcement   Announcement @relation(fields: [announcementId], references: [id], onDelete: Cascade)

  @@unique([announcementId, userId, id])
}

model AuditLogEvent {
  id             String   @id @default(cuid())
  eventType      String
  createdAt      DateTime @default(now())
  userId         Int?
  extraEventData Json
  user           User?    @relation(fields: [userId], references: [id])
}

model Contract {
  id          String   @id @default(cuid())
  status      String   @default("PENDING")
  type        String   @default("NEW_ORDER")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  partnerId   Int
  partner     Partner  @relation(fields: [partnerId], references: [id])
  createdById Int
  createdBy   User     @relation(fields: [createdById], references: [id])

  // Company information (directly in this table)
  companyName               String
  companyTaxNumber          String
  companyRegistrationNumber String
  companyStreetName         String
  companyStreetType         String
  companyHouseNumber        String
  companyFloorDoor          String?
  companyCity               String
  companyZipCode            String
  companyCountry            String
  companyAuthorizedRepName  String
  companyAdditionalRepName  String?
  companyCommencementDate   String
  companyInvoiceEmail       String

  // Billing information
  billingIsSameAsCompanyAddress Boolean @default(true)
  billingName                   String?
  billingStreetName             String?
  billingStreetType             String?
  billingHouseNumber            String?
  billingFloorDoor              String?
  billingCity                   String?
  billingZipCode                String?
  billingCountry                String?

  // Main contact fields (directly in this table)
  mainContactFirstName String?
  mainContactLastName  String?
  mainContactPosition  String?
  mainContactPhone     String?
  mainContactEmail     String?

  // Relations to other tables that need to be separate
  packages          ContractPackage[]
  secondaryContacts ContractSecondaryContact[]
  affiliates        ContractAffiliatedCompany[]
}

model ContractPackage {
  id             String              @id @default(cuid())
  createdAt      DateTime            @default(now())
  price          Float
  packageId      String
  quantity       Int                 @default(1)
  historicalData String[]            @default([])
  package        SubscriptionPackage @relation(fields: [packageId], references: [id])
  contractId     String
  contract       Contract            @relation(fields: [contractId], references: [id], onDelete: Cascade)
}

model ContractSecondaryContact {
  id        String  @id @default(cuid())
  firstName String?
  lastName  String?
  position  String? // Job title/position
  phone     String?
  email     String?

  contractId String
  contract   Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

model ContractAffiliatedCompany {
  id                         String @id @default(cuid())
  name                       String
  taxNumber                  String
  companyRegistration        String
  address                    String
  city                       String
  zipCode                    String
  country                    String
  customersHoldingPercentage Int

  contractId String
  contract   Contract @relation(fields: [contractId], references: [id], onDelete: Cascade)
  createdAt  DateTime @default(now())
}

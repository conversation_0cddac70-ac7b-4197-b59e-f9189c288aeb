#!/bin/bash

# Define key and file
ENV_FILE=".env"
KEY="PUBLIC_ENV__COMMIT_HASH"
COMMIT_HASH=$(git rev-parse HEAD)

# 1. Create .env if it doesn't exist
if [ ! -f "$ENV_FILE" ]; then
  echo "$ENV_FILE not found. Creating..."
  touch "$ENV_FILE"
fi

# 2. Check if key exists, replace or append
if grep -q "^$KEY=" "$ENV_FILE"; then
  # 3. Replace the line with the new commit hash
  echo "Updating existing $KEY..."
  sed -i.bak "s/^$KEY=.*/$KEY=$COMMIT_HASH/" "$ENV_FILE"
else
  # Add the key
  echo "Adding $KEY..."
  echo "$KEY=$COMMIT_HASH" >> "$ENV_FILE"
fi

echo "✅ $KEY set to $COMMIT_HASH"
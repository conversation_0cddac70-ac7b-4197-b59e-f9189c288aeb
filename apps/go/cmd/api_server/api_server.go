package main

import (
	"context"
	"fmt"
	"os"

	_ "github.com/jackc/pgx/v5/stdlib"
	"gitlab.com/lexunit/hupx-labs/pkg/api_server"
	"gitlab.com/lexunit/hupx-labs/pkg/configuration"
	"gitlab.com/lexunit/hupx-labs/pkg/db"
)

func getConfigDir() string {
	configPath := os.Getenv("DATASET_CONFIG_PATH")
	if configPath == "" {
		configPath = "./dsconfigs/"
	}
	return configPath
}

func main() {
	hupxMirrorConn, err := db.CreateHupxMirrorPsqlDbConnectionFromEnv()
	if err != nil {
		panic(err)
	}
	websiteConn, err := db.CreateWebsitePsqlDbConnectionFromEnv()
	if err != nil {
		panic(err)
	}

	configDirPath := getConfigDir()
	datasetConfig, err := configuration.ParseConfigDir(configDirPath)
	if err != nil {
		panic(err)
	}

	// Insert subscriptions from config into database
	ctx := context.Background()
	if err := configuration.InsertSubscriptions(ctx, websiteConn, datasetConfig); err != nil {
		panic(fmt.Errorf("failed to insert subscriptions: %w", err))
	}
	if err := configuration.InsertSubscriptionPackages(ctx, websiteConn); err != nil {
		panic(fmt.Errorf("failed to insert subscription packages: %w", err))
	}

	// Initialize authorizer with website database
	authDb := api_server.NewAuthorizerPSQL(websiteConn)
	authorizer := api_server.NewAuthorizer(authDb, datasetConfig)

	// Server configuration
	port := os.Getenv("PORT")
	serverConfig := api_server.ServerConfig{
		Host: "0.0.0.0",
		Port: port,
	}

	// Initialize and start server
	server := api_server.NewServer(serverConfig, datasetConfig, hupxMirrorConn, authorizer)
	server.Start()

}

package api_server

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/labstack/echo/v4"
	"gitlab.com/lexunit/hupx-labs/pkg/configuration"
)

const (
	headerApiKey         = "X-API-KEY"
	headerInternalApiKey = "X-INTERNAL-API-KEY"
	headerSubscriptions  = "X-SUBSCRIPTIONS"
	envVarInternalApiKey = "INTERNAL_API_KEY"
)

var (
	ErrApiKeyInvalid    = errors.New("invalid api key")
	ErrApiKeyExpired    = errors.New("expired api key")
	ErrApiKeyMissing    = errors.New("missing api key")
	ErrInvalidApiKey    = errors.New("invalid internal API key")
	ErrUnauthorized     = errors.New("unauthorized access")
	ErrOriginNotAllowed = errors.New("origin not allowed")
)

type contextKeyApiServer string

var (
	subscriptionsKey = contextKeyApiServer("subscriptions")
)

type Subscription struct {
	ID               string `json:"id"`
	DataSetName      string `json:"dataSetName"`
	SubscriptionName string `json:"subscriptionName"`
}

type ApiKeyData struct {
	Value         string
	UserId        int
	ExpiresAt     string
	UserType      string
	Subscriptions []Subscription
}

func getSubscriptionsFromContext(ctx context.Context) []Subscription {
	value := ctx.Value(subscriptionsKey)
	subscriptions, ok := value.([]Subscription)
	if !ok {
		return []Subscription{}
	}
	return subscriptions
}

func setSubscriptionsToContext(ctx context.Context, subscriptions []Subscription) context.Context {
	return context.WithValue(ctx, subscriptionsKey, subscriptions)
}

func setSubscriptionsInEchoContext(c echo.Context, subscriptions []Subscription) error {
	r := c.Request().WithContext(setSubscriptionsToContext(c.Request().Context(), subscriptions))
	if r == nil {
		return fmt.Errorf("could not create new request with subscriptions context")
	}
	c.SetRequest(r)
	return nil
}

type AuthorizerDB interface {
	GetApiKeyData(ctx context.Context, apiKey string) (*ApiKeyData, error)
}

type AuthorizerPSQL struct {
	db *sql.DB
}

func NewAuthorizerPSQL(db *sql.DB) *AuthorizerPSQL {
	return &AuthorizerPSQL{db: db}
}

func (a *AuthorizerPSQL) GetApiKeyData(ctx context.Context, apiKey string) (*ApiKeyData, error) {
	query := `WITH user_subscriptions AS (
	    SELECT DISTINCT
	        subscriptions.id,
	        subscriptions."dataSetName",
	        subscriptions."subscriptionName"
	    FROM "User" u
	    JOIN "Partner" p ON u."partnerId" = p.id
	    JOIN "ApiKey" ak ON ak."userId" = u.id
	    LEFT JOIN (
	        SELECT spa."partnerId", s.id, s."dataSetName", s."subscriptionName"
	        FROM "SubscriptionPackageAssignment" spa
	        JOIN "SubscriptionPackage" sp ON spa."subscriptionPackageId" = sp.id
	        JOIN "SubscriptionToPackage" stp ON sp.id = stp."subscriptionPackageId"
	        JOIN "Subscription" s ON stp."subscriptionId" = s.id
	    ) subscriptions ON subscriptions."partnerId" = p.id
	    WHERE ak.value = $1
	)
	SELECT
	    ak.value,
	    ak."userId",
	    ak."expiresAt",
	    u.type as "userType",
	    COALESCE(json_agg(
	        json_build_object(
	            'id', us.id,
	            'dataSetName', us."dataSetName",
	            'subscriptionName', us."subscriptionName"
	        )
	    ) FILTER (WHERE us.id IS NOT NULL), '[]') as subscriptions
	FROM "ApiKey" ak
	JOIN "User" u ON u.id = ak."userId"
	LEFT JOIN user_subscriptions us ON true
	WHERE ak.value = $1
	GROUP BY ak.value, ak."userId", ak."expiresAt", u.type`

	var apiKeyData ApiKeyData
	var subscriptionsJson string
	err := a.db.QueryRowContext(ctx, query, apiKey).Scan(
		&apiKeyData.Value,
		&apiKeyData.UserId,
		&apiKeyData.ExpiresAt,
		&apiKeyData.UserType,
		&subscriptionsJson,
	)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrApiKeyInvalid
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	err = json.Unmarshal([]byte(subscriptionsJson), &apiKeyData.Subscriptions)
	if err != nil {
		return nil, fmt.Errorf("error parsing subscriptions: %w", err)
	}

	// If user is ADMIN, add admin subscription
	if apiKeyData.UserType == "ADMIN" || apiKeyData.UserType == "SUPER_ADMIN" {
		apiKeyData.Subscriptions = append(apiKeyData.Subscriptions, Subscription{
			ID:               "*",
			DataSetName:      "*",
			SubscriptionName: "*",
		})
	}

	return &apiKeyData, nil
}

type Authorizer struct {
	datasetConfig *configuration.ConfigResolved
	db            AuthorizerDB
}

func NewAuthorizer(db AuthorizerDB, datasetConfig *configuration.ConfigResolved) *Authorizer {
	return &Authorizer{
		db:            db,
		datasetConfig: datasetConfig,
	}
}

func (a *Authorizer) getInternalApiKey() string {
	return os.Getenv(envVarInternalApiKey)
}

func (a *Authorizer) parseSubscriptions(headerContent string) ([]Subscription, error) {
	var subscriptions []Subscription
	err := json.Unmarshal([]byte(headerContent), &subscriptions)
	if err != nil {
		return nil, fmt.Errorf("invalid subscriptions format: %w", err)
	}
	return subscriptions, nil
}

func (a *Authorizer) addSubscriptionsToEchoContext(c echo.Context) error {
	internalApiKey := c.Request().Header.Get(headerInternalApiKey)
	apiKey := c.Request().Header.Get(headerApiKey)
	// there is only internal api key present
	if internalApiKey != "" && apiKey == "" {
		if internalApiKey != a.getInternalApiKey() {
			return ErrInvalidApiKey
		}

		// Allow OpenAPI endpoint without subscriptions header
		if c.Request().URL.Path == "/openapi.json" {
			return setSubscriptionsInEchoContext(c, []Subscription{})
		}

		subscriptionsHeader := c.Request().Header.Get(headerSubscriptions)
		if subscriptionsHeader == "" {
			return fmt.Errorf("missing subscriptions header")
		}

		subscriptions, err := a.parseSubscriptions(subscriptionsHeader)
		if err != nil {
			return err
		}

		return setSubscriptionsInEchoContext(c, subscriptions)
	}

	// Handle external API key based call
	if apiKey == "" {
		return ErrApiKeyMissing
	}

	apiKeyData, err := a.db.GetApiKeyData(c.Request().Context(), apiKey)
	if err != nil {
		return err
	}

	expiresAt, err := time.Parse(time.RFC3339, apiKeyData.ExpiresAt)
	if err != nil {
		return fmt.Errorf("invalid expiration date format: %w", err)
	}

	if expiresAt.Before(time.Now()) {
		return ErrApiKeyExpired
	}

	return setSubscriptionsInEchoContext(c, apiKeyData.Subscriptions)
}

func grantsDatasetAccess(subscriptions []Subscription, dataset configuration.DatasetResolved) bool {
	if isAdminSubscription(subscriptions) {
		return true
	}

	for _, subscription := range subscriptions {
		if subscription.DataSetName == "*" {
			return true
		}

		if subscription.DataSetName != dataset.Name {
			continue
		}
		for _, datasetSub := range dataset.Subscriptions {
			if subscription.SubscriptionName == datasetSub.Name {
				return true
			}
		}
	}
	return false
}

func isAdminSubscription(subscriptions []Subscription) bool {
	for _, sub := range subscriptions {
		if sub.ID == "*" || sub.DataSetName == "*" {
			return true
		}
	}
	return false
}

func applySubscriptionFilters(query sq.SelectBuilder, dataset configuration.DatasetResolved, subscriptions []Subscription) sq.SelectBuilder {
	// If user has admin access, return query without filters
	if isAdminSubscription(subscriptions) {
		return query
	}

	for _, datasetSub := range dataset.Subscriptions {
		for _, subscription := range subscriptions {
			if subscription.DataSetName != dataset.Name {
				continue
			}
			if subscription.SubscriptionName == datasetSub.Name {
				// Apply filters defined in the subscription
				for _, filter := range datasetSub.Filter {
					query = query.Where(sq.Expr(filter))
				}
				// Apply limit if defined
				if datasetSub.Limit > 0 {
					query = query.Limit(uint64(datasetSub.Limit))
				}
				return query
			}
		}
	}
	return query
}

func (a *Authorizer) Process(next echo.HandlerFunc) echo.HandlerFunc {
	type ErrResp struct {
		Message string `json:"message"`
	}

	return func(c echo.Context) error {
		err := a.addSubscriptionsToEchoContext(c)
		if err != nil {
			errStatusCode := http.StatusInternalServerError
			if errors.Is(err, ErrInvalidApiKey) || errors.Is(err, ErrApiKeyInvalid) ||
				errors.Is(err, ErrApiKeyMissing) || errors.Is(err, ErrUnauthorized) || errors.Is(err, ErrApiKeyExpired) {
				errStatusCode = http.StatusUnauthorized
			}
			if errors.Is(err, ErrOriginNotAllowed) {
				errStatusCode = http.StatusForbidden
			}
			return c.JSON(errStatusCode, ErrResp{Message: err.Error()})
		}
		return next(c)
	}
}

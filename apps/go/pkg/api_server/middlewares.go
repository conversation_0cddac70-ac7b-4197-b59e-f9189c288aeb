package api_server

import (
	"fmt"
	"github.com/google/uuid"
	"gitlab.com/lexunit/hupx-labs/pkg/api_server/event_logger"
	"log/slog"
	"os"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"golang.org/x/time/rate"
)

const (
	DEFAULT_RATE_LIMIT_UI  = 10.0
	DEFAULT_RATE_BURST_UI  = 50
	DEFAULT_RATE_LIMIT_API = 10.0
	DEFAULT_RATE_BURST_API = 100
)

func (s *ApiServer) middlewares() {
	// Add gzip middleware before other middlewares for better performance
	s.e.Use(middleware.GzipWithConfig(middleware.GzipConfig{
		Level: 5, // Default compression level
		Skipper: func(c echo.Context) bool {
			// skip for paths ending with /csv or /xlsx
			if strings.HasSuffix(c.Request().URL.Path, "/csv") ||
				strings.HasSuffix(c.Request().URL.Path, "/xlsx") {
				return true
			}

			// Skip compression for Server-Sent Events (SSE) or WebSocket connections
			if c.Request().Header.Get("Accept") == "text/event-stream" {
				return true
			}
			return false
		},
	}))

	s.e.Use(middleware.LoggerWithConfig(middleware.LoggerConfig{
		Skipper: func(c echo.Context) bool { return false },
		Format: `{"time":"${time_rfc3339_nano}","id":"${id}","remote_ip":"${remote_ip}",` +
			`"host":"${host}","method":"${method}","uri":"${uri}","user_agent":"${user_agent}",` +
			`"status":${status},"error":"${error}","latency":${latency},"latency_human":"${latency_human}"` +
			`,"bytes_in":${bytes_in},"bytes_out":${bytes_out},"x-api-key":"${header:x-api-key}"` +
			`,"x-internal-api-key":"${header:x-internal-api-key}","x-embedding-token":"${header:x-embedding-token}"` +
			`,"x-orig-referer":"${header:x-orig-referer}", "x-subscriptions":${header:x-subscriptions}}` +
			"\n",
		CustomTimeFormat: "2006-01-02 15:04:05.00000",
	}))

	s.e.Use(s.authorizer.Process)
	env := os.Getenv("ENV")
	if strings.ToLower(env) != "dev" {
		apiRateLimiter := rateLimiter(
			DEFAULT_RATE_LIMIT_API,
			DEFAULT_RATE_BURST_API,
			apiKeyIdentifier,
			apiKeySkipper,
		)
		internalApiRateLimiter := rateLimiter(
			DEFAULT_RATE_LIMIT_UI,
			DEFAULT_RATE_BURST_UI,
			internalApiKeyIdentifier,
			internalApiSkipper,
		)

		s.e.Use(apiRateLimiter)
		s.e.Use(internalApiRateLimiter)
	}

	s.e.Use(func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			req := c.Request()
			res := c.Response()
			start := time.Now()

			err := next(c)

			apiKey, _ := apiKeyIdentifier(c)

			event := event_logger.ApiEvent{
				ID:         uuid.New().String(),
				Timestamp:  time.Now(),
				Method:     req.Method,
				Path:       req.URL.Path,
				StatusCode: res.Status,
				Duration:   time.Since(start).Milliseconds(),
				Filters:    c.QueryParam("filter"),
				ApiKey:     apiKey,
			}

			if logErr := s.eventLogger.LogEvent(event); logErr != nil {
				// Log the error but don't affect the response
				slog.Error("Failed to log API event", "error", logErr)
			}

			return err
		}
	},
	)
}

// internalApiSkipper skips every request that has an api key in the header
func internalApiSkipper(c echo.Context) bool {
	return c.Request().Header.Get(headerApiKey) != ""
}

// internalApiKeyIdentifier returns the remote ip from the header if the
// internal api key is present, errors otherwise
func internalApiKeyIdentifier(c echo.Context) (string, error) {
	internalApiKey := c.Request().Header.Get(headerInternalApiKey)
	if internalApiKey == "" {
		return "", ErrApiKeyMissing
	}

	// As it comes from the UI we need to use something else as
	// a key to make sure we won't get spammed from there neither
	remoteIp := c.RealIP()
	if remoteIp == "" {
		return "", fmt.Errorf("could not determine remote ip, can't check for rate limit")
	}
	return remoteIp, nil
}

// apiKeySkipper skips the requests where API key header is empty or missing
func apiKeySkipper(c echo.Context) bool {
	return c.Request().Header.Get(headerApiKey) == ""
}

// apiKeyIdentifier returns the api key from the header, errors if not present
func apiKeyIdentifier(c echo.Context) (string, error) {
	apiKey := c.Request().Header.Get(headerApiKey)
	if apiKey == "" {
		return "", ErrApiKeyMissing
	}
	return apiKey, nil
}

func rateLimiter(rate rate.Limit, burst int, identifier func(c echo.Context) (string, error), skipper func(c echo.Context) bool) echo.MiddlewareFunc {
	store := middleware.NewRateLimiterMemoryStoreWithConfig(
		middleware.RateLimiterMemoryStoreConfig{
			Rate:      rate,
			Burst:     burst,
			ExpiresIn: 5 * time.Minute,
		},
	)

	return middleware.RateLimiterWithConfig(middleware.RateLimiterConfig{
		Skipper:             skipper,
		BeforeFunc:          nil,
		IdentifierExtractor: identifier,
		Store:               store,
		ErrorHandler:        middleware.DefaultRateLimiterConfig.ErrorHandler,
		DenyHandler:         middleware.DefaultRateLimiterConfig.DenyHandler,
	})
}

package api_server

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/labstack/echo/v4"
)

type MockAuthorizerRepository struct {
	data []ApiKeyData
}

func (a *MockAuthorizerRepository) GetApiKeyData(_ context.Context, apiKey string) (*ApiKeyData, error) {
	for _, d := range a.data {
		if d.Value == apiKey {
			return &d, nil
		}
	}
	return nil, ErrInvalidApiKey
}

func makeEchoContext(apikey, internalApiKey, subscriptions string) echo.Context {
	e := echo.New()
	request := httptest.NewRequest(http.MethodGet, "/", nil)
	if apikey != "" {
		request.Header.Set(headerApiKey, apikey)
	}
	if internalApiKey != "" {
		request.Header.Set(headerInternalApiKey, internalApiKey)
		request.Header.Set(headerSubscriptions, subscriptions)
	}
	response := httptest.NewRecorder()

	return e.NewContext(request, response)
}

func TestAuthorizer_validApiKeyResultsInSubscriptionsAddedToContext(t *testing.T) {
	subscriptions := []Subscription{
		{
			ID:               "sub1",
			DataSetName:      "orders",
			SubscriptionName: "Full",
		},
	}
	authRepo := &MockAuthorizerRepository{
		data: []ApiKeyData{{
			Value:         "api-key-1",
			ExpiresAt:     time.Now().Add(1 * time.Hour).Format(time.RFC3339),
			Subscriptions: subscriptions,
		}},
	}
	echoContext := makeEchoContext("api-key-1", "", "")
	authorizer := NewAuthorizer(authRepo, nil)

	err := authorizer.addSubscriptionsToEchoContext(echoContext)

	if err != nil {
		t.Fatalf("could not extract subscriptions from echo context: %v", err)
	}
	contextSubscriptions := getSubscriptionsFromContext(echoContext.Request().Context())
	if !cmp.Equal(contextSubscriptions, subscriptions) {
		t.Errorf("correct subscriptions are missing, diff: %v", cmp.Diff(contextSubscriptions, subscriptions))
	}
}

func TestAuthorizer_withValidInternalApiKeySubscriptionsAreTakenFromRequestHeader(t *testing.T) {
	internalApiKey := "internal-api-key-1"
	err := os.Setenv(envVarInternalApiKey, internalApiKey)
	if err != nil {
		panic(err)
	}
	defer os.Unsetenv(envVarInternalApiKey)

	subscriptionsJson := `[{"id":"sub1","dataSetName":"orders","subscriptionName":"Full"},{"id":"sub2","dataSetName":"trades","subscriptionName":"Public"}]`
	expectedSubscriptions := []Subscription{
		{ID: "sub1", DataSetName: "orders", SubscriptionName: "Full"},
		{ID: "sub2", DataSetName: "trades", SubscriptionName: "Public"},
	}

	authRepo := &MockAuthorizerRepository{
		data: []ApiKeyData{{
			Value:         "api-key-1",
			ExpiresAt:     time.Now().Add(1 * time.Hour).Format(time.RFC3339),
			Subscriptions: []Subscription{},
		}},
	}
	echoContext := makeEchoContext("", "internal-api-key-1", subscriptionsJson)
	authorizer := NewAuthorizer(authRepo, nil)

	err = authorizer.addSubscriptionsToEchoContext(echoContext)

	if err != nil {
		t.Fatalf("could not extract subscriptions from echo context: %v", err)
	}
	contextSubscriptions := getSubscriptionsFromContext(echoContext.Request().Context())
	if !cmp.Equal(contextSubscriptions, expectedSubscriptions) {
		t.Errorf("correct subscriptions are missing, diff: %v", cmp.Diff(contextSubscriptions, expectedSubscriptions))
	}
}

func TestAuthorizer_withoutAnyAuthorizationItReturnsWithUnauthorized(t *testing.T) {
	internalApiKey := "internal-api-key-1"
	err := os.Setenv(envVarInternalApiKey, internalApiKey)
	if err != nil {
		panic(err)
	}
	defer os.Unsetenv(envVarInternalApiKey)

	subscriptions := []Subscription{
		{
			ID:               "sub1",
			DataSetName:      "orders",
			SubscriptionName: "Full",
		},
	}
	authRepo := &MockAuthorizerRepository{
		data: []ApiKeyData{{
			Value:         "api-key-1",
			ExpiresAt:     time.Now().Add(1 * time.Hour).Format(time.RFC3339),
			Subscriptions: subscriptions,
		}},
	}

	// Test with different subscription header contents
	headers := []string{"", `[{"id":"sub1","dataSetName":"orders","subscriptionName":"Full"}]`}
	for _, subHeader := range headers {
		t.Run(fmt.Sprintf("subscription header set to %s", subHeader), func(t *testing.T) {
			echoContext := makeEchoContext("", "", subHeader)
			authorizer := NewAuthorizer(authRepo, nil)

			err := authorizer.addSubscriptionsToEchoContext(echoContext)

			if err == nil {
				t.Fatalf("should have resulted in error")
			}
			if !errors.Is(err, ErrApiKeyMissing) {
				t.Fatalf("error should be ErrApiKeyMissing")
			}
		})
	}
}

func TestAuthorizer_OpenAPIEndpointWithInternalKey(t *testing.T) {
	internalApiKey := "internal-api-key-1"
	err := os.Setenv(envVarInternalApiKey, internalApiKey)
	if err != nil {
		panic(err)
	}
	defer os.Unsetenv(envVarInternalApiKey)

	e := echo.New()
	request := httptest.NewRequest(http.MethodGet, "/openapi.json", nil)
	request.Header.Set(headerInternalApiKey, internalApiKey)
	response := httptest.NewRecorder()
	echoContext := e.NewContext(request, response)

	authorizer := NewAuthorizer(&MockAuthorizerRepository{}, nil)
	err = authorizer.addSubscriptionsToEchoContext(echoContext)

	if err != nil {
		t.Fatalf("OpenAPI endpoint with internal key should not return error: %v", err)
	}

	subs := getSubscriptionsFromContext(echoContext.Request().Context())
	if len(subs) != 0 {
		t.Errorf("OpenAPI endpoint should have empty subscriptions, got: %v", subs)
	}
}

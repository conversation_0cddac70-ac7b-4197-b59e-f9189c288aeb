package api_server

import (
	"database/sql"
	"encoding/xml"
	"fmt"
	"gitlab.com/lexunit/hupx-labs/pkg/api_server/event_logger"
	"io"
	"log/slog"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humaecho"
	"github.com/labstack/echo/v4"
	"gitlab.com/lexunit/hupx-labs/pkg/configuration"

	_ "github.com/jackc/pgx/v5/stdlib"
)

func getPublicDomain() string {
	domain := os.Getenv("PUBLIC_DOMAIN")
	if domain == "" {
		return "http://local.atotalinsurtech.com"
	}
	return domain
}

func isCacheTurnedOn() bool {
	v := os.Getenv("API_CACHE")
	return strings.ToLower(v) != "off"
}

func maxCacheSizeForDatasets() int {
	defaultSize := 25
	v := os.Getenv("CACHE_SIZE_FOR_DATASETS")
	if v == "" {
		return defaultSize
	}
	sizeFromEnv, err := strconv.Atoi(v)
	if err != nil {
		slog.Error("could not convert cache size env var to int", "err", err, "CACHE_SIZE_FOR_DATASETS", v)
		return defaultSize
	}
	return sizeFromEnv
}

type ServerConfig struct {
	Host string
	Port string
}

type ApiServer struct {
	e             *echo.Echo
	api           huma.API
	c             ServerConfig
	datasetConfig *configuration.ConfigResolved
	db            *sql.DB
	authorizer    *Authorizer
	cache         *datasetCache
	eventLogger   *event_logger.EventLogger
}

var XMLFormat = huma.Format{
	Marshal: func(w io.Writer, v any) error {
		encoder := xml.NewEncoder(w)
		if encoder == nil {
			return fmt.Errorf("could not create encoder")
		}
		return encoder.Encode(v)
	},
	Unmarshal: xml.Unmarshal,
}

// maybe later on we should attach a separate logger just for the server
// and not use the default everywhere?
func setupLogging() {
	logger := slog.New(
		slog.NewJSONHandler(
			os.Stdout,
			&slog.HandlerOptions{
				Level: slog.LevelDebug, // left in debug to fine-tune cache
			},
		),
	)
	slog.SetDefault(logger)
}

func NewServer(config ServerConfig, datasetConfig *configuration.ConfigResolved, db *sql.DB, authorizer *Authorizer) *ApiServer {
	setupLogging()

	e := echo.New()

	schemaPrefix := "#/components/schemas/"
	schemasPath := "/api/schemas"

	formats := map[string]huma.Format{
		"application/json": huma.DefaultJSONFormat,
		"json":             huma.DefaultJSONFormat,
		"application/xml":  XMLFormat,
		"xml":              XMLFormat,
	}

	registry := huma.NewMapRegistry(schemaPrefix, huma.DefaultSchemaNamer)
	humaConfig := huma.Config{
		OpenAPI: &huma.OpenAPI{
			OpenAPI: "3.1.0",
			Info: &huma.Info{
				Title:   "HUPX API",
				Version: "1.0.0",
			},
			Components: &huma.Components{
				Schemas: registry,
			},
		},
		OpenAPIPath:   "/openapi",
		DocsPath:      "/docs",
		SchemasPath:   schemasPath,
		Formats:       formats,
		DefaultFormat: "application/json",
		CreateHooks: []func(huma.Config) huma.Config{
			func(c huma.Config) huma.Config {
				linkTransformer := huma.NewSchemaLinkTransformer(schemaPrefix, c.SchemasPath)
				c.OpenAPI.OnAddOperation = append(c.OpenAPI.OnAddOperation, linkTransformer.OnAddOperation)
				c.Transformers = append(c.Transformers, linkTransformer.Transform)
				return c
			},
		},
	}
	humaConfig.Servers = []*huma.Server{
		{URL: fmt.Sprintf("https://%s/api", getPublicDomain())},
	}

	api := humaecho.New(e, humaConfig)

	datasetNames := make([]string, 0, len(datasetConfig.Datasets))
	for _, ds := range datasetConfig.Datasets {
		datasetNames = append(datasetNames, ds.Name)
	}
	var cache *datasetCache
	if isCacheTurnedOn() {
		cache = newDatasetCache(db,
			15*time.Minute,
			1*time.Minute,
			datasetNames,
			maxCacheSizeForDatasets(),
		)
	} else {
		cache = nil
	}

	eventLogger := event_logger.NewEventLogger(1000, 5*time.Second)

	s := &ApiServer{
		e:             e,
		api:           api,
		c:             config,
		datasetConfig: datasetConfig,
		db:            db,
		authorizer:    authorizer,
		cache:         cache,
		eventLogger:   eventLogger,
	}

	s.middlewares()
	s.routes()
	s.csvRoutes()
	s.xlsxRoutes()

	return s
}

func (s *ApiServer) Start() {
	address := fmt.Sprintf("%s:%s", s.c.Host, s.c.Port)
	s.e.Logger.Fatal(s.e.Start(address))
}

package api_server

import (
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"slices"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	_ "github.com/jackc/pgx/v5/stdlib"
	"github.com/stretchr/testify/assert"
	"gitlab.com/lexunit/hupx-labs/pkg/configuration"
	"gitlab.com/lexunit/hupx-labs/pkg/db"
)

func makeAuthorizedTestRequest(method, target string, body io.Reader) *http.Request {
	req := httptest.NewRequest(method, target, body)
	req.Header.Set("x-api-key", "valid-api-key")
	return req
}

func newServerWithTestConfig(t *testing.T) *ApiServer {
	authDb := &MockAuthorizerRepository{data: []ApiKeyData{
		{
			Value:     "valid-api-key",
			ExpiresAt: time.Now().Add(time.Hour).Format(time.RFC3339),
			Subscriptions: []Subscription{
				{ID: "sub1", DataSetName: "DAM_Aggregated_curve", SubscriptionName: "Full"},
				{ID: "sub2", DataSetName: "DAM_Aggregated_Trading_Data", SubscriptionName: "Full"},
				{ID: "sub3", DataSetName: "DAM_IC_Flows", SubscriptionName: "Full"},
				{ID: "sub4", DataSetName: "IDA", SubscriptionName: "Full"},
				{ID: "sub5", DataSetName: "IDA_QuarterHourly", SubscriptionName: "Full"},
				{ID: "sub6", DataSetName: "IDC_Hourly", SubscriptionName: "Full"},
				{ID: "sub7", DataSetName: "IDC_QuarterHourly", SubscriptionName: "Full"},
				{ID: "sub8", DataSetName: "IIP", SubscriptionName: "Full"},
			},
		},
		{
			Value:     "valid-api-key-1",
			ExpiresAt: time.Now().Add(time.Hour).Format(time.RFC3339),
			Subscriptions: []Subscription{
				{ID: "sub9", DataSetName: "DAM_IC_Flows", SubscriptionName: "Public"},
			},
		},
		{
			Value:     "valid-api-key-2",
			ExpiresAt: time.Now().Add(time.Hour).Format(time.RFC3339),
			Subscriptions: []Subscription{
				{ID: "sub10", DataSetName: "DAM_IC_Flows", SubscriptionName: "Public"},
			},
		},
		{
			Value:     "expired-api-key",
			ExpiresAt: time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
			Subscriptions: []Subscription{
				{ID: "sub11", DataSetName: "DAM_Aggregated_curve", SubscriptionName: "Full"},
				{ID: "sub12", DataSetName: "DAM_IC_Flows", SubscriptionName: "Full"},
				{ID: "sub13", DataSetName: "IDA", SubscriptionName: "Full"},
				{ID: "sub14", DataSetName: "IDC_Hourly", SubscriptionName: "Full"},
			},
		},
	}}
	connection := db.TestDatabaseConnection()

	t.Cleanup(func() {
		if err := connection.Close(); err != nil {
			t.Errorf("could not close connection: %v", err)
		}
	})

	datasetConfig, err := configuration.ParseConfigDir("../../dsconfigs/")
	if err != nil {
		panic(err)
	}
	serverConfig := ServerConfig{}
	authorizer := NewAuthorizer(authDb, datasetConfig)
	return NewServer(serverConfig, datasetConfig, connection, authorizer)
}

func TestRequestWithoutAPIKeyResultsNoAllowedDatasets(t *testing.T) {
	server := newServerWithTestConfig(t)
	request := httptest.NewRequest("GET", "/v1/ida", nil)
	response := httptest.NewRecorder()
	reqCtx := server.e.NewContext(request, response)

	server.e.ServeHTTP(response, reqCtx.Request())

	subs := getSubscriptionsFromContext(reqCtx.Request().Context())
	if len(subs) != 0 {
		t.Error("Request should not have any subscriptions because of missing API key")
	}
}

func TestOffset(t *testing.T) {
	server := newServerWithTestConfig(t)
	version := "v1"
	datasetName := "iip"
	type Resp struct {
		Message string
		Data    []map[string]any
	}

	limit1 := 10 // there are like 13 rows with the default filter
	offset1 := 0

	url1 := fmt.Sprintf("/%s/%s?limit=%d&offset=%d", version, datasetName, limit1, offset1)
	request1 := makeAuthorizedTestRequest("GET", url1, nil)
	response1 := httptest.NewRecorder()
	reqCtx1 := server.e.NewContext(request1, response1)

	server.e.ServeHTTP(response1, reqCtx1.Request())

	if response1.Code != 200 {
		t.Fatalf("bad response code: got %d, want %d", response1.Code, 200)
	}

	resp1 := Resp{
		Data: make([]map[string]any, 0),
	}
	err := json.Unmarshal(response1.Body.Bytes(), &resp1)
	if err != nil {
		t.Fatalf("could not unmarshal response json: %v", err)
	}
	if amount := len(resp1.Data); amount != limit1 {
		t.Errorf("expected %d data in response 1, got %d", limit1, amount)
	}
	// vvv teszt(?) vvv
	// ha a visszakapott resp.Data -mondjuk- 5. eleme nem egyezik a
	// limit = 1 & offset = 4 lekérés eredményével, akkor valami nem jó

	halfLimit := limit1 / 2

	resp2Data := make([]map[string]any, 0)
	for i := 0; i < 2; i++ {
		url2 := fmt.Sprintf("/%s/%s?limit=%d&offset=%d", version, datasetName, halfLimit, 0+(halfLimit*i))
		request2 := makeAuthorizedTestRequest("GET", url2, nil)
		response2 := httptest.NewRecorder()
		reqCtx2 := server.e.NewContext(request2, response2)

		server.e.ServeHTTP(response2, reqCtx2.Request())
		if rc := response2.Code; rc != 200 {
			t.Fatalf("bad response code: got %d, want %d", rc, 200)
		}

		resp2 := Resp{
			Data: make([]map[string]any, 0),
		}
		err = json.Unmarshal(response2.Body.Bytes(), &resp2)
		if err != nil {
			t.Fatalf("could not unmarshal response 2 json: %v", err)
		}
		if l := len(resp2.Data); l != halfLimit {
			t.Fatalf("expected %d datapoint got %d", halfLimit, l)
		}

		resp2Data = append(resp2Data, resp2.Data...)
	}

	for _, d1 := range resp1.Data {
		foundInD2 := false
		for _, d2 := range resp2Data {
			if cmp.Equal(d1, d2) {
				foundInD2 = true
				break
			}
		}
		if !foundInD2 {
			t.Errorf("%#v not found in merged offset response data", d1)
		}
	}
}

func TestFilters(t *testing.T) {
	server := newServerWithTestConfig(t)
	validFilters := []struct {
		version     string
		datasetName string
		query       string
		expected    string
		invalid     bool
	}{
		{
			version:     "v1",
			datasetName: "dam_ic_flows",
			query:       "DeliveryDay__eq__2024-11-01,Hours__eq__1",
			expected:    `{"message":"Success","data":[{"AT_HU":459.9, "DeliveryDay":"2024-11-01T00:00:00Z", "HR_HU":0, "HU_AT":0, "HU_HR":75.5, "HU_RO":1195.7, "HU_SI":0, "HU_SK":0, "Hours":1, "RO_HU":0, "SI_HU":505.8, "SK_HU":1578.7}]}`,
			invalid:     false,
		},
		{
			version:     "v1",
			datasetName: "dam_aggregated_trading_data",
			query:       "DeliveryDay__eq__2024-11-01,ProductH__eq__24",
			expected:    `{"message":"Success","data":[{"DeliveryDay":"2024-11-01T00:00:00Z","ProductH":24,"Price":103.71,"Volume":2507.3,"BaseloadPrice":96.63}]}`,
			invalid:     false,
		},
	}
	for _, variant := range []string{"", "/csv", "/xlsx"} {
		for _, arg := range validFilters {
			url := fmt.Sprintf("/%s/%s%s?filter=%s", arg.version, arg.datasetName, variant, arg.query)
			request := makeAuthorizedTestRequest("GET", url, nil)
			response := httptest.NewRecorder()
			reqCtx := server.e.NewContext(request, response)

			server.e.ServeHTTP(response, reqCtx.Request())

			if response.Code != 200 {
				t.Fatalf("bad response code: got %d, want %d", response.Code, 200)
			}
			// xlsx or csv endpoint, let's mov on, won't check them :/
			if variant != "" {
				continue
			}

			if !assert.JSONEq(t, arg.expected, response.Body.String()) {
				t.Errorf("bad response body: got %s, want %s", response.Body.String(), arg.expected)
			}
		}
	}

	// expecting some 400 errors because of invalid triggers
	invalidFilters := []struct {
		version     string
		datasetName string
		query       string
		invalid     bool
	}{
		{
			version:     "v1",
			datasetName: "iip",
			query:       "ID__eq__22056",
			invalid:     true,
		},
	}

	// the validity check is present for the csv and xlsx endpoints as well
	for _, variant := range []string{"", "/csv", "/xlsx"} {
		for _, arg := range invalidFilters {
			url := fmt.Sprintf("/%s/%s%s?filter=%s", arg.version, arg.datasetName, variant, arg.query)
			request := makeAuthorizedTestRequest("GET", url, nil)
			response := httptest.NewRecorder()
			reqCtx := server.e.NewContext(request, response)

			server.e.ServeHTTP(response, reqCtx.Request())

			if response.Code != http.StatusBadRequest {
				t.Fatalf("bad response code: got %d, want %d", response.Code, http.StatusBadRequest)
			}
		}
	}

}

func TestCanSendXMLResponse(t *testing.T) {
	server := newServerWithTestConfig(t)
	request := makeAuthorizedTestRequest(http.MethodGet, "/v1/dam_ic_flows", nil)
	request.Header.Set("Accept", "application/xml")
	response := httptest.NewRecorder()
	reqCtx := server.e.NewContext(request, response)

	server.e.ServeHTTP(response, reqCtx.Request())

	if response.Code != http.StatusOK {
		t.Errorf("status should be %d but %d", http.StatusOK, response.Code)
	}
	contentTypeHeader := response.Header().Get("Content-Type")
	if !strings.Contains(strings.ToLower(contentTypeHeader), "xml") {
		t.Errorf("response content type header not correctly set: expected ~%s got %s", "xml", contentTypeHeader)
	}
	body, err := io.ReadAll(response.Body)
	if err != nil {
		t.Fatalf("could not read response body: %v", err)
	}
	if err := xml.Unmarshal(body, new(any)); err != nil {
		t.Errorf("could not parse the response body into xml: %v", err)
	}
}

func TestWithoutAuthorization401UnauthorizedReturned(t *testing.T) {
	server := newServerWithTestConfig(t)
	request := httptest.NewRequest("GET", "/v1/dam_ic_flows", nil)
	response := httptest.NewRecorder()
	reqCtx := server.e.NewContext(request, response)

	server.e.ServeHTTP(response, reqCtx.Request())

	assert.Equal(t, 401, response.Code)
}

func TestInvalidAPIKeyResultsUnauthorizedResponse(t *testing.T) {
	server := newServerWithTestConfig(t)
	request := httptest.NewRequest("GET", "/v1/dam_ic_flows", nil)
	request.Header.Set(headerApiKey, "invalid-api-key")
	response := httptest.NewRecorder()
	reqCtx := server.e.NewContext(request, response)

	server.e.ServeHTTP(response, reqCtx.Request())

	assert.Equal(t, 401, response.Code)
}

func TestExpiredAPIKeyResultsUnauthorizedResponse(t *testing.T) {
	server := newServerWithTestConfig(t)
	request := httptest.NewRequest("GET", "/v1/dam_ic_flows", nil)
	request.Header.Set(headerApiKey, "expired-api-key")
	response := httptest.NewRecorder()
	reqCtx := server.e.NewContext(request, response)

	server.e.ServeHTTP(response, reqCtx.Request())

	assert.Equal(t, 401, response.Code)
}

func makeRecorders(n int) []*httptest.ResponseRecorder {
	responses := make([]*httptest.ResponseRecorder, n)
	for i := 0; i < n; i++ {
		responses[i] = httptest.NewRecorder()
	}
	return responses
}

func TestRateLimitingWorksForSingleUser(t *testing.T) {
	server := newServerWithTestConfig(t)

	requestNumber := DEFAULT_RATE_BURST_API + 2
	responses := makeRecorders(requestNumber)

	wg := sync.WaitGroup{}
	for _, resp := range responses {
		wg.Add(1)
		go func(r *httptest.ResponseRecorder) {
			defer wg.Done()
			request := makeAuthorizedTestRequest("GET", "/v1/dam_ic_flows", nil)
			reqCtx := server.e.NewContext(request, nil)
			server.e.ServeHTTP(r, reqCtx.Request())
		}(resp)
	}
	wg.Wait()

	numberOfRejectedRequests := 0
	for _, resp := range responses {
		if resp.Code == http.StatusTooManyRequests {
			numberOfRejectedRequests++
		}
	}

	expectedRejectedRequests := 1
	if numberOfRejectedRequests < expectedRejectedRequests {
		t.Fatalf(
			"there should be at least %d rejected request with 429 status code but there is %d",
			expectedRejectedRequests,
			numberOfRejectedRequests,
		)
	}
	if requestNumber-numberOfRejectedRequests < DEFAULT_RATE_BURST_API {
		t.Fatalf(
			"there should be at least %d successful requests but there is only %d",
			DEFAULT_RATE_BURST_API,
			requestNumber-numberOfRejectedRequests,
		)
	}
}

func TestRateLimitingIsUsedSeparatelyForEachAPIKey(t *testing.T) {
	server := newServerWithTestConfig(t)
	apiKey1 := "valid-api-key-1"
	apiKey2 := "valid-api-key-2"

	wg := sync.WaitGroup{}
	for i := 0; i < DEFAULT_RATE_BURST_API; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			request := httptest.NewRequest("GET", "/v1/dam_ic_flows", nil)
			request.Header.Set(headerApiKey, apiKey1)
			response := httptest.NewRecorder()
			reqCtx := server.e.NewContext(request, response)
			server.e.ServeHTTP(response, reqCtx.Request())
		}()
	}
	wg.Wait()

	apiKey1Responses := makeRecorders(DEFAULT_RATE_BURST_API)
	apiKey2Responses := makeRecorders(DEFAULT_RATE_BURST_API)
	for i := 0; i < DEFAULT_RATE_BURST_API; i++ {
		i := i // Create new variable for goroutine
		wg.Add(1)
		go func() {
			defer wg.Done()
			request := httptest.NewRequest("GET", "/v1/dam_ic_flows", nil)
			request.Header.Set(headerApiKey, apiKey1)
			reqCtx := server.e.NewContext(request, apiKey1Responses[i])
			server.e.ServeHTTP(apiKey1Responses[i], reqCtx.Request())
		}()
		wg.Add(1)
		go func() {
			defer wg.Done()
			request := httptest.NewRequest("GET", "/v1/dam_ic_flows", nil)
			request.Header.Set(headerApiKey, apiKey2)
			reqCtx := server.e.NewContext(request, apiKey2Responses[i])
			server.e.ServeHTTP(apiKey2Responses[i], reqCtx.Request())
		}()
	}
	wg.Wait()

	rejectRateForApiKey1 := calculateRejectRate(apiKey1Responses)
	rejectRateForApiKey2 := calculateRejectRate(apiKey2Responses)
	expectedRejectRateForApiKey1 := 0.9

	if rejectRateForApiKey1 < expectedRejectRateForApiKey1 {
		t.Errorf(
			"Reject rate for api key 1 is %f, expected at least %f",
			rejectRateForApiKey1,
			expectedRejectRateForApiKey1,
		)
	}
	if rejectRateForApiKey2 > 0.0 {
		t.Errorf(
			"Reject rate for api key 2 is %f, expected none",
			rejectRateForApiKey2,
		)
	}
}

func TestServerReturns400WhenInvalidFilterUsed(t *testing.T) {

}

// some mixed in unittest-like stuff...
func TestFilterValidator(t *testing.T) {
	// minimal config for the function to run
	config := configuration.DatasetResolved{
		Dataset: configuration.Dataset{
			Fields: []configuration.Field{
				{
					Name:    "First",
					Filters: []string{"eq"},
				},
				{
					Name:    "Second",
					Filters: []string{"eq", "lte", "lt", "gte", "gt"},
				},
			},
		},
	}

	validFilter := []FilterEntry{
		{Field: "First", Op: "eq", Value: "1"},
		{Field: "Second", Op: "lte", Value: "1"},
		{Field: "Second", Op: "gt", Value: "1"},
	}

	if ifs := findInvalidFilters(validFilter, config); len(ifs) != 0 {
		t.Errorf("expected filter to be valid, but invalid filters were provided %v", ifs)
	}

	invalidFilter := []FilterEntry{
		{Field: "First", Op: "eq", Value: "1"}, // ok
		{Field: "First", Op: "lt", Value: "2"}, // invalid operand
		{Field: "Thirs", Op: "eq", Value: "1"}, // not allowed filter name
	}

	ifs := findInvalidFilters(invalidFilter, config)
	if len(ifs) != 2 {
		t.Errorf("expected to have 2 invalid filters, got %d", len(ifs))
	}
	slices.ContainsFunc(ifs, func(fe1 FilterEntry) bool {
		return slices.ContainsFunc(invalidFilter, func(fe2 FilterEntry) bool {
			return fe1.Equal(&fe2)
		})
	})
}

func calculateRejectRate(responses []*httptest.ResponseRecorder) float64 {
	total := len(responses)
	rejected := 0
	for _, response := range responses {
		if response.Code == http.StatusTooManyRequests {
			rejected++
		}
	}
	return float64(rejected) / float64(total)
}

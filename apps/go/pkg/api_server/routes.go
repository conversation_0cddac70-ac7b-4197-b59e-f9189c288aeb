package api_server

import (
	"bufio"
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/xml"
	"errors"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	sq "github.com/Masterminds/squirrel"
	"github.com/danielgtaylor/huma/v2"
	"github.com/tealeg/xlsx/v3"
	"gitlab.com/lexunit/hupx-labs/pkg/configuration"
)

const (
	RESPONSE_DATA_HARD_LIMIT = 50_000
)

type ErrBadFilters struct {
	InvalidFilters []FilterEntry `json:"invalid_filters" xml:"invalid_filters"`
}

func tableName(dataset configuration.DatasetResolved) string {
	return dataset.Database + "_" + dataset.Name
}

func (e ErrBadFilters) Error() string {
	sb := strings.Builder{}
	for i, invf := range e.InvalidFilters {
		if i != 0 {
			sb.WriteString(", ")
		}
		sb.WriteString(fmt.Sprintf("(%s; %s; %s)", invf.Field, invf.Op, invf.Value))
	}
	return sb.String()
}

type ValueMapper struct {
	fieldMappings map[string][]ValueMapping
	hasMappings   bool
}

type ValueMapping struct {
	From any
	To   any
}

func createValueMapper(fields []configuration.Field) *ValueMapper {
	fieldMappings := make(map[string][]ValueMapping)
	hasMappings := false

	for _, field := range fields {
		if len(field.ValueMappings) > 0 {
			var mappings []ValueMapping
			for _, mapping := range field.ValueMappings {
				fromVal, err := convertToFieldType(mapping.From, field.Type)
				if err != nil {
					slog.Error("failed to convert 'from' value", "field", field.Name, "value", mapping.From, "type", field.Type, "err", err)
					continue
				}
				toVal, err := convertToFieldType(mapping.To, field.Type)
				if err != nil {
					slog.Error("failed to convert 'to' value", "field", field.Name, "value", mapping.To, "type", field.Type, "err", err)
					continue
				}
				mappings = append(mappings, ValueMapping{From: fromVal, To: toVal})
			}
			fieldMappings[field.Name] = mappings
			hasMappings = true
		}
	}

	return &ValueMapper{
		fieldMappings: fieldMappings,
		hasMappings:   hasMappings,
	}
}

func convertToFieldType(value any, fieldType string) (any, error) {
	strVal, isString := value.(string)
	if !isString {
		return value, nil
	}

	switch fieldType {
	case "bit", "bool":
		switch strings.ToLower(strVal) {
		case "true", "1", "yes", "on":
			return true, nil
		case "false", "0", "no", "off":
			return false, nil
		default:
			return nil, fmt.Errorf("invalid boolean value: %s", strVal)
		}
	case "int", "bigint":
		return strconv.ParseInt(strVal, 10, 64)
	case "float", "decimal", "numeric":
		return strconv.ParseFloat(strVal, 64)
	case "datetime", "datetime2", "datetimeoffset":
		return time.Parse(time.RFC3339, strVal)
	default:
		// For string types or unknown types, keep as string
		return strVal, nil
	}
}

func (vm *ValueMapper) mapValue(fieldName string, value any) any {
	// Quick return if no mappings exist
	if !vm.hasMappings {
		return value
	}

	// Check if we have mappings for this field
	mappings, exists := vm.fieldMappings[fieldName]
	if !exists {
		return value
	}

	// Handle nil values
	if value == nil {
		return value
	}

	// Try to find a matching mapping
	for _, mapping := range mappings {
		// First try direct equality
		if value == mapping.From {
			return mapping.To
		}

		// Try type-specific comparison
		switch fromVal := mapping.From.(type) {
		case string:
			if strVal, ok := value.(string); ok && strVal == fromVal {
				return mapping.To
			}
		case int64:
			switch v := value.(type) {
			case int64:
				if v == fromVal {
					return mapping.To
				}
			case int32:
				if int64(v) == fromVal {
					return mapping.To
				}
			case int:
				if int64(v) == fromVal {
					return mapping.To
				}
			}
		case float64:
			if floatVal, ok := value.(float64); ok && floatVal == fromVal {
				return mapping.To
			}
		case bool:
			if boolVal, ok := value.(bool); ok && boolVal == fromVal {
				return mapping.To
			}
		case time.Time:
			if timeVal, ok := value.(time.Time); ok && timeVal.Equal(fromVal) {
				return mapping.To
			}
		}
	}

	// If no mapping found, return original value
	return value
}

func getResponseDataHardLimit() int {
	if hardLimit := os.Getenv("RESPONSE_DATA_HARD_LIMIT"); hardLimit != "" {
		hl, err := strconv.Atoi(hardLimit)
		if err != nil {
			return RESPONSE_DATA_HARD_LIMIT
		}
		return hl
	}
	return RESPONSE_DATA_HARD_LIMIT
}

func convertToCorrectType(fields []configuration.Field, fieldName, value string) (any, error) {
	for _, field := range fields {
		if field.Name == fieldName {
			switch field.Type {
			case "int", "bigint":
				return strconv.Atoi(value)
			case "float":
				return strconv.ParseFloat(value, 64)
			case "bool":
				return strconv.ParseBool(value)
			case "date", "time", "datetime", "datetimeoffset":
				for _, format := range []string{
					"2006-01-02T15:04:05Z",
					"2006-01-02T15:04:05",
				} {
					v, err := time.Parse(format, value)
					if err == nil { // its ok, we parsed it
						return v, nil
					}
				}
				return time.Parse("2006-01-02", value) // fallback
			default:
				return value, nil
			}
		}
	}
	// if we get here, we didn't find a matching field
	slog.Error("Field not found", "field", fieldName)
	return nil, fmt.Errorf("field not found: %s", fieldName)
}

// findInvalidFilters returns whether the supplied filterEntries are allowed
// to be used based on the current configuration.
// If the filters are in sync with the configuration the returned value is
// an empty list, otherwise the problematic filters.
func findInvalidFilters(fes []FilterEntry, config configuration.DatasetResolved) []FilterEntry {
	invalid := make([]FilterEntry, 0)
	for _, e := range fes {
		found := false
		for _, field := range config.Fields {
			if !(field.Name == e.Field) {
				continue
			}

			if !slices.Contains(field.Filters, e.Op) {
				continue
			}
			found = true
			break
		}

		if !found {
			invalid = append(invalid, e)
		}
	}

	return invalid
}

func buildBaseQuery(c context.Context, dataset configuration.DatasetResolved, validFields map[string]bool, columns []string, input *Input) (sq.SelectBuilder, error) {
	var query sq.SelectBuilder
	if dataset.ApiQuery != "" {
		query = sq.Select(columns...).From("(" + dataset.ApiQuery + ") as sq").PlaceholderFormat(sq.Dollar)
	} else {
		tableName := tableName(dataset)
		query = sq.StatementBuilder.PlaceholderFormat(sq.Dollar).Select(columns...).From(tableName)
	}

	// Apply subscription-specific filters if exists
	subscriptions := getSubscriptionsFromContext(c)
	if !isAdminSubscription(subscriptions) {
		query = applySubscriptionFilters(query, dataset, subscriptions)
	}

	invalidFilters := findInvalidFilters(input.FilterEntries, dataset)
	if len(invalidFilters) != 0 {
		return query, ErrBadFilters{invalidFilters}
	}

	// Sort the filterEntries, as we are using the sql query
	// as the cache key
	slices.SortFunc(input.FilterEntries, func(a, b FilterEntry) int {
		if a.Field < b.Field {
			return -1
		}

		if a.Field > b.Field {
			return 1
		}

		// Field is the same
		if a.Op < b.Op {
			return -1
		}
		if a.Op > b.Op {
			return 1
		}

		// Operand is the same
		if a.Value < b.Value {
			return -1
		}
		if a.Value > b.Value {
			return 1
		}

		// The same filter twice?
		return 0
	})

	// Validate and apply filters
	for _, filter := range input.FilterEntries {
		if !validFields[filter.Field] {
			return query, fmt.Errorf("invalid field in filter: %s", filter.Field)
		}

		quotedField := fmt.Sprintf(`"%s"`, filter.Field)
		val, err := convertToCorrectType(dataset.Fields, filter.Field, filter.Value)
		if err != nil {
			return query, fmt.Errorf("error converting value to correct type: %w", err)
		}

		switch filter.Op {
		case "eq":
			query = query.Where(sq.Eq{quotedField: val})
		case "neq":
			query = query.Where(sq.NotEq{quotedField: val})
		case "gt":
			query = query.Where(sq.Gt{quotedField: val})
		case "gte":
			query = query.Where(sq.GtOrEq{quotedField: val})
		case "lt":
			query = query.Where(sq.Lt{quotedField: val})
		case "lte":
			query = query.Where(sq.LtOrEq{quotedField: val})
		case "like":
			query = query.Where(sq.Like{quotedField: "%" + filter.Value + "%"})
		default:
			return query, fmt.Errorf("unsupported operator: %s", filter.Op)
		}
	}

	if dataset.DefaultOrder != nil {
		for _, order := range dataset.DefaultOrder {
			if !validFields[order.Key] {
				return query, fmt.Errorf("invalid field in default order: %s, validFields: %#v", order.Key, validFields)
			}

			quotedField := fmt.Sprintf(`"%s"`, order.Key)
			switch order.Order {
			case "asc":
				query = query.OrderBy(quotedField)
			case "desc":
				query = query.OrderBy(quotedField + " DESC")
			default:
				return query, fmt.Errorf("unsupported order: %s", order.Order)
			}
		}
	} else if dataset.Plot.X != "" {
		query = query.OrderBy(fmt.Sprintf(`"%s"`, dataset.Plot.X))
	}

	query = query.Limit(uint64(input.Limit))
	query = query.Offset(uint64(input.Offset))

	return query, nil
}

func columns(ds configuration.DatasetResolved) []string {
	columns := make([]string, 0, len(ds.Fields))
	for _, field := range ds.Fields {
		columns = append(columns, fmt.Sprintf(`"%s"`, field.Name))
	}
	return columns
}

func validFields(dataset configuration.DatasetResolved) map[string]bool {
	validFields := make(map[string]bool)
	for _, field := range dataset.Fields {
		validFields[field.Name] = true
	}
	return validFields
}

func (s *ApiServer) loadDataFromDbOrCache(c context.Context, dataset configuration.DatasetResolved, input *Input) ([]map[string]any, error) {
	query, err := buildBaseQuery(c, dataset, validFields(dataset), columns(dataset), input)
	if err != nil {
		// is this pattern good? not quite sure yet
		// somehow want to differentiate between errors and return
		// some extra info accordingly
		var errBadFilters ErrBadFilters
		switch {
		case errors.As(err, &errBadFilters):
			slog.Error("Got invalid filters", "err", err)
			return nil, huma.Error400BadRequest("received invalid filters", err)
		default:
			slog.Error("Error building base query", "err", err)
			return nil, huma.Error500InternalServerError("error building base query", err)
		}
	}

	sql, args, err := query.ToSql()
	if err != nil {
		slog.Error("could not build sql query", "err", err)
		return nil, huma.Error500InternalServerError("error building sql query", err)
	}

	var results []map[string]any
	if s.cache != nil {
		results, err = s.cache.Get(dataset.Name, sql, args...)
	}
	if err != nil {
		if errors.Is(err, errEntryExpired) {
			slog.Info("the cache expired for the query, running against the db", "sql", sql)
		} else if errors.Is(err, errEntryNotFound) {
			slog.Info(
				"cache for query not found",
				"sql", sql[:50]+"...",
				"datasetName", dataset.Name,
				"args", args,
			)
		} else {
			slog.Error("could not get cache value", "err", err, "sql", sql)
		}
	}

	if len(results) == 0 { // cache is empty
		rows, err := s.db.Query(sql, args...)
		if err != nil {
			slog.Error("could not query database", "err", err)
			return nil, huma.Error500InternalServerError("error querying database", err)
		}
		defer rows.Close()

		results, err = scanRows(rows, columns(dataset), createValueMapper(dataset.Fields))
		if err != nil {
			slog.Error("could not scan rows from db", "err", err)
			return nil, huma.Error500InternalServerError("error scanning rows from db", err)
		}

		if s.cache != nil {
			s.cache.Add(dataset.Name, sql, results, args...)
		}
	}

	return results, nil
}

func (s *ApiServer) newHandler(dataset configuration.DatasetResolved) func(context.Context, *Input) (*Output, error) {
	return func(c context.Context, input *Input) (*Output, error) {
		subscriptions := getSubscriptionsFromContext(c)
		if !grantsDatasetAccess(subscriptions, dataset) {
			// return empty array
			return &Output{
				Body: Body{
					Data:    []map[string]any{},
					Message: "Success",
				},
			}, nil
		}

		results, err := s.loadDataFromDbOrCache(c, dataset, input)
		if err != nil {
			slog.Error("could not load data from db or cache", "err", err)
			return nil, huma.Error500InternalServerError("internal server error")
		}

		response := &Output{}
		response.Body.Data = results
		response.Body.Message = "Success"

		return response, nil
	}
}

func (s *ApiServer) routes() {
	for _, dataset := range s.datasetConfig.Datasets {
		// Create a map of field names to their Huma schema types
		fieldTypes := make(map[string]*huma.Schema)
		for idx, field := range dataset.Fields {
			goType := mssqlToGoType(field.Type)
			fieldTypes[field.Name] = getHumaType(goType)
			fieldTypes[field.Name].Description = field.Description
			fieldTypes[field.Name].Title = field.Title
			fieldTypes[field.Name].Extensions = map[string]any{
				"unit":  field.Unit,
				"order": idx,
			}
		}

		for _, version := range dataset.ApiVersions {
			path := fmt.Sprintf("/%s/%s", version, strings.ToLower(dataset.Name))
			slog.Info("registering path ==>", "path", path)
			OperationID := dataset.Name + "_" + version
			huma.Register(s.api, huma.Operation{
				OperationID: OperationID,
				Method:      http.MethodGet,
				Path:        path,
				Summary:     dataset.Summary,
				Description: dataset.Description,
				Tags:        dataset.Tags,
				Responses: map[string]*huma.Response{
					"200": {
						Description: "Successful response",
						Content: map[string]*huma.MediaType{
							"application/json": {
								Schema: &huma.Schema{
									Type: "object",
									Properties: map[string]*huma.Schema{
										"message": {Type: "string"},
										"data": {
											Type: "array",
											Items: &huma.Schema{
												Type:       "object",
												Properties: fieldTypes,
											},
										},
									},
								},
							},
							"application/xml": {
								Schema: &huma.Schema{
									Type: "object",
									Properties: map[string]*huma.Schema{
										"message": {Type: "string"},
										"data": {
											Type: "array",
											Items: &huma.Schema{
												Type:       "object",
												Properties: fieldTypes,
											},
										},
									},
								},
							},
						},
					},
				},
				Extensions: map[string]any{
					"x-api-version-change": dataset.ApiChange,
					"x-data-view-url-name": dataset.DataViewUrlName,
					"x-name":               dataset.Name,
					"x-plot": map[string]any{
						"group": dataset.Plot.Group,
						"x":     dataset.Plot.X,
						"y":     dataset.Plot.Y,
						"type":  dataset.Plot.Type,
						"titles": func() map[string]string {
							titles := make(map[string]string)
							for _, field := range dataset.Fields {
								titles[field.Name] = field.Title
							}
							return titles
						}(),
					},
					"x-has-public-subscription": func() bool {
						for _, subscription := range dataset.Subscriptions {
							if strings.ToLower(subscription.Name) == "public" {
								return true
							}
						}
						return false
					}(),
					"x-data-filters": func() map[string][]string {
						filters := make(map[string][]string)
						for _, field := range dataset.Fields {
							if len(field.Filters) == 0 {
								continue
							}
							filters[field.Name] = field.Filters
						}
						return filters
					}(),
				},
			}, s.newHandler(dataset))
		}
	}
}

func (s *ApiServer) csvRoutes() {
	for _, dataset := range s.datasetConfig.Datasets {
		// Create a map of field names to their Huma schema types
		fieldTypes := make(map[string]*huma.Schema)
		for _, field := range dataset.Fields {
			goType := mssqlToGoType(field.Type)
			fieldTypes[field.Name] = getHumaType(goType)
		}

		for _, version := range dataset.ApiVersions {
			path := fmt.Sprintf("/%s/%s/csv", version, strings.ToLower(dataset.Name))
			slog.Info("registering path ==>", "path", path)
			OperationID := dataset.Name + "_" + version + "_csv"
			huma.Register(s.api, huma.Operation{
				OperationID: OperationID,
				Method:      http.MethodGet,
				Path:        path,
				Summary:     dataset.Summary,
				Description: dataset.Description,
				Responses: map[string]*huma.Response{
					"200": {
						Description: "Successful response",
						Content: map[string]*huma.MediaType{
							"text/csv": {},
						},
					},
				},
				Extensions: map[string]any{
					"x-data-filters": func() map[string][]string {
						filters := make(map[string][]string)
						for _, field := range dataset.Fields {
							if len(field.Filters) == 0 {
								continue
							}
							filters[field.Name] = field.Filters
						}
						return filters
					}(),
				},
			}, s.newCsvHandler(dataset))
		}
	}
}

func (s *ApiServer) xlsxRoutes() {
	for _, dataset := range s.datasetConfig.Datasets {
		// Create a map of field names to their Huma schema types
		fieldTypes := make(map[string]*huma.Schema)
		for _, field := range dataset.Fields {
			goType := mssqlToGoType(field.Type)
			fieldTypes[field.Name] = getHumaType(goType)
		}

		for _, version := range dataset.ApiVersions {
			path := fmt.Sprintf("/%s/%s/xlsx", version, strings.ToLower(dataset.Name))
			slog.Info("registering path ==>", "path", path)
			OperationID := dataset.Name + "_" + version + "_xlsx"
			huma.Register(s.api, huma.Operation{
				OperationID: OperationID,
				Method:      http.MethodGet,
				Path:        path,
				Summary:     dataset.Summary,
				Description: dataset.Description,
				Responses: map[string]*huma.Response{
					"200": {
						Description: "Successful response",
						Content: map[string]*huma.MediaType{
							"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": {},
						},
					},
				},
				Extensions: map[string]any{
					"x-data-filters": func() map[string][]string {
						filters := make(map[string][]string)
						for _, field := range dataset.Fields {
							if len(field.Filters) == 0 {
								continue
							}
							filters[field.Name] = field.Filters
						}
						return filters
					}(),
				},
			}, s.newXlsxHandler(dataset))
		}
	}
}

type FileOutput struct {
	ContentType        string `header:"Content-Type"`
	ContentDisposition string `header:"Content-Disposition"`
	Body               []byte
}

type CsvParams struct {
	Input
	FileName string `query:"filename"`
}

func (s *ApiServer) newCsvHandler(dataset configuration.DatasetResolved) func(context.Context, *CsvParams) (*FileOutput, error) {
	columns := make([]string, 0, len(dataset.Fields))
	validFields := make(map[string]bool)
	for _, field := range dataset.Fields {
		fieldName := fmt.Sprintf(`"%s"`, field.Name)
		columns = append(columns, fieldName)
		validFields[field.Name] = true
	}

	return func(c context.Context, st *CsvParams) (*FileOutput, error) {
		subscriptions := getSubscriptionsFromContext(c)
		if !grantsDatasetAccess(subscriptions, dataset) {
			// return empty array
			return &FileOutput{
				ContentType:        "text/csv",
				ContentDisposition: fmt.Sprintf("attachment; filename=%s", st.FileName),
				Body:               []byte("[]"),
			}, nil
		}
		query, err := buildBaseQuery(c, dataset, validFields, columns, &st.Input)
		if err != nil {
			var errBadFilters ErrBadFilters
			switch {
			case errors.As(err, &errBadFilters):
				slog.Error("Got invalid filters", "err", err)
				return nil, huma.Error400BadRequest("received invalid filters", err)
			default:
				slog.Error("Error building base query", "err", err)
				return nil, huma.Error500InternalServerError("internal server error")
			}
		}
		sql, args, err := query.ToSql()
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}

		rows, err := s.db.Query(sql, args...)
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}
		defer rows.Close()

		results, err := scanRows(rows, columns, createValueMapper(dataset.Fields))
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}

		body, err := createCsv(c, columns, results)
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}

		response := &FileOutput{
			ContentType:        "text/csv",
			ContentDisposition: fmt.Sprintf("attachment; filename=%s", st.FileName),
			Body:               body,
		}

		return response, nil
	}
}

func createCsv(c context.Context, columns []string, results []map[string]any) ([]byte, error) {
	type result struct {
		data []byte
		err  error
	}
	resChan := make(chan result, 1)

	go func() {
		buffer := &bytes.Buffer{}
		csvWriter := csv.NewWriter(buffer)

		err := csvWriter.Write(columns)
		if err != nil {
			resChan <- result{nil, err}
			return
		}
		resultRows := make([][]string, 0, len(results))
		for _, row := range results {
			rr := make([]string, 0, len(row))
			for _, col := range columns {
				// remove surrounding "
				c := strings.Trim(col, `"`)
				v := row[c]
				switch vv := v.(type) {
				case time.Time:
					rr = append(rr, vv.Format("2006-01-02T15:04:05Z"))
				case nil:
					rr = append(rr, "")
				default:
					rr = append(rr, fmt.Sprintf("%v", v))
				}
			}
			resultRows = append(resultRows, rr)
		}

		err = csvWriter.WriteAll(resultRows)
		if err != nil {
			resChan <- result{nil, err}
			return
		}

		resChan <- result{buffer.Bytes(), nil}
	}()

	select {
	case res := <-resChan:
		return res.data, res.err
	case <-c.Done():
		slog.Debug("context cancelled", "function", "createCsv")
		return nil, nil
	}
}

func (s *ApiServer) newXlsxHandler(dataset configuration.DatasetResolved) func(context.Context, *CsvParams) (*FileOutput, error) {
	columns := make([]string, 0, len(dataset.Fields))
	validFields := make(map[string]bool)
	for _, field := range dataset.Fields {
		fieldName := fmt.Sprintf(`"%s"`, field.Name)
		columns = append(columns, fieldName)
		validFields[field.Name] = true
	}

	return func(c context.Context, st *CsvParams) (*FileOutput, error) {
		subscriptions := getSubscriptionsFromContext(c)
		if !grantsDatasetAccess(subscriptions, dataset) {
			return &FileOutput{
				ContentType:        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
				ContentDisposition: fmt.Sprintf("attachment; filename=%s", st.FileName),
				Body:               []byte(""),
			}, nil
		}
		query, err := buildBaseQuery(c, dataset, validFields, columns, &st.Input)
		if err != nil {
			var errBadFilters ErrBadFilters
			switch {
			case errors.As(err, &errBadFilters):
				slog.Error("Got invalid filters", "err", err)
				return nil, huma.Error400BadRequest("received invalid filters", err)
			default:
				slog.Error("Error building base query", "err", err)
				return nil, huma.Error500InternalServerError("internal server error")
			}
		}

		sql, args, err := query.ToSql()
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}

		rows, err := s.db.Query(sql, args...)
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}
		defer rows.Close()

		results, err := scanRows(rows, columns, createValueMapper(dataset.Fields))
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}

		body, err := createXlsx(c, columns, results, "data")
		if err != nil {
			return nil, huma.Error500InternalServerError("internal server error")
		}

		response := &FileOutput{
			ContentType:        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			ContentDisposition: fmt.Sprintf("attachment; filename=%s", st.FileName),
			Body:               body,
		}

		return response, nil
	}
}

func createXlsx(c context.Context, columns []string, results []map[string]any, sheetName string) ([]byte, error) {
	type result struct {
		data []byte
		err  error
	}
	resChan := make(chan result, 1)

	go func() {
		f := xlsx.NewFile()
		sheet, err := f.AddSheet(sheetName)
		if err != nil {
			slog.Error("Error creating sheet", "err", err)
			resChan <- result{nil, fmt.Errorf("error creating sheet: %w", err)}
			return
		}
		colDef := xlsx.NewColForRange(1, len(columns)+1)
		colDef.SetWidth(20)
		sheet.SetColParameters(colDef)

		firstRow := sheet.AddRow()
		headerIndexMap := make(map[string]int, len(columns))
		for i, col := range columns {
			idx := i
			firstRow.GetCell(idx).SetString(col)
			col := strings.Trim(col, `"`)
			headerIndexMap[col] = idx
		}

		for _, rowMap := range results {
			rowRef := sheet.AddRow()
			for k, v := range rowMap {
				idx, ok := headerIndexMap[k]
				if !ok {
					slog.Error("could not find index for column", "column", k, "headerIndexMap", headerIndexMap)
					return
				}
				var formatted string
				switch v.(type) {
				case nil:
					formatted = ""
				default:
					formatted = fmt.Sprint(v)
				}
				rowRef.GetCell(idx).SetString(formatted)
			}
		}

		var b bytes.Buffer
		writer := bufio.NewWriter(&b)
		writeErr := f.Write(writer)
		if writeErr != nil {
			slog.Error("Error writing sheet to buffer", "err", err)
			resChan <- result{nil, fmt.Errorf("error writing sheet to buffer: %w", err)}
		}
		sheetBuffer := b.Bytes()
		resChan <- result{sheetBuffer, nil}
	}()

	select {
	case res := <-resChan:
		return res.data, res.err
	case <-c.Done():
		slog.Debug("context cancelled", "function", "createXlsx")
		return nil, nil
	}
}

type FilterEntry struct {
	Field string `json:"field" xml:"field"`
	Op    string `json:"op" xml:"op"`
	Value string `json:"value" xml:"value"`
}

// Equal is a helper method to compare the filter fields in realistic manner.
// var f1, f2 *FilterEntry; f1 == f2 would be true, but f1.Equal(f2) is false.
// It is safe - I think - to just blindly take the address of and FilterEntry
// and drop it into Equal
func (f *FilterEntry) Equal(o *FilterEntry) bool {
	if o == nil || f == nil {
		return false
	}
	return f.Field == o.Field && f.Op == o.Op && f.Value == o.Value
}

type Input struct {
	Filter        string `query:"filter"`
	Limit         int    `query:"limit"`
	Offset        int    `query:"offset"`
	FilterEntries []FilterEntry
}

func (i *Input) Resolve(ctx huma.Context) []error {
	var errors []error

	if i.Filter != "" {
		filterParts := strings.Split(i.Filter, ",")
		for _, part := range filterParts {
			entry, err := parseFilterEntry(part)
			if err != nil {
				errors = append(errors, err)
			} else {
				i.FilterEntries = append(i.FilterEntries, entry)
			}
		}
	}

	if i.Limit == 0 || i.Limit > getResponseDataHardLimit() {
		i.Limit = getResponseDataHardLimit()
	}

	return errors
}

func parseFilterEntry(filterPart string) (FilterEntry, error) {
	parts := strings.Split(filterPart, "__")
	if len(parts) != 3 {
		return FilterEntry{}, fmt.Errorf("invalid filter format: %s", filterPart)
	}

	return FilterEntry{
		Field: parts[0],
		Op:    parts[1],
		Value: parts[2],
	}, nil
}

type Body struct {
	Message string           `json:"message,omitempty" xml:"message"`
	Data    []map[string]any `json:"data" xml:"data"`
}

type Output struct {
	Body Body `xml:"body"`
}

func (o Body) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	if err := e.EncodeToken(xml.StartElement{Name: xml.Name{Local: strings.ToLower(start.Name.Local)}}); err != nil {
		return err
	}

	if err := e.EncodeElement(o.Message, xml.StartElement{Name: xml.Name{Local: "message"}}); err != nil {
		return err
	}

	for _, d := range o.Data {
		if err := e.EncodeToken(xml.StartElement{Name: xml.Name{Local: "data"}}); err != nil {
			return err
		}

		for k, v := range d {
			if err := encodeElement(e, k, v); err != nil {
				return err
			}
		}

		if err := e.EncodeToken(xml.EndElement{Name: xml.Name{Local: "data"}}); err != nil {
			return err
		}
	}

	return e.EncodeToken(xml.EndElement{Name: xml.Name{Local: strings.ToLower(start.Name.Local)}})
}

func encodeElement(e *xml.Encoder, key string, value any) error {
	switch v := value.(type) {
	case string:
		return e.EncodeElement(value, xml.StartElement{Name: xml.Name{Local: key}})
	case int, int64:
		return e.EncodeElement(fmt.Sprintf("%d", v), xml.StartElement{Name: xml.Name{Local: key}})
	case float64:
		return e.EncodeElement(fmt.Sprintf("%.2f", v), xml.StartElement{Name: xml.Name{Local: key}})
	case time.Time:
		return e.EncodeElement(v.Format(time.RFC3339), xml.StartElement{Name: xml.Name{Local: key}})
	default:
		return e.EncodeElement(fmt.Sprintf("%v", v), xml.StartElement{Name: xml.Name{Local: key}})
	}
}

func scanRows(rows *sql.Rows, columns []string, mapper *ValueMapper) ([]map[string]any, error) {
	values := make([]any, len(columns))
	scanArgs := make([]any, len(columns))
	for i := range values {
		scanArgs[i] = &values[i]
	}

	var results []map[string]any

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			slog.Error("Error scanning row", "err", err)
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		row := make(map[string]any)
		for i, col := range columns {
			unquotedCol := strings.Trim(col, `"`)
			// hopefully this does not hurts performance too bad
			value := mapper.mapValue(unquotedCol, values[i])
			row[unquotedCol] = value
		}

		results = append(results, row)
	}

	if err := rows.Err(); err != nil {
		slog.Error("Error iterating rows", "err", err)
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return results, nil
}

func mssqlToGoType(sqlType string) string {
	switch sqlType {
	case "bit":
		return "bool"
	case "tinyint":
		return "uint8"
	case "smallint":
		return "int16"
	case "int":
		return "int"
	case "bigint":
		return "int64"
	case "decimal", "numeric", "smallmoney", "money":
		return "float64"
	case "float":
		return "float64"
	case "real":
		return "float32"
	case "date", "time", "datetime", "datetime2", "smalldatetime", "datetimeoffset":
		return "time.Time"
	case "char", "varchar", "text", "nchar", "nvarchar", "ntext":
		return "string"
	case "binary", "varbinary", "image":
		return "[]byte"
	case "timestamp", "rowversion":
		return "[]byte"
	default:
		return "interface{}"
	}
}

func getHumaType(goType string) *huma.Schema {
	switch goType {
	case "uint8", "int16", "int", "int64":
		return &huma.Schema{Type: "integer"}
	case "float32", "float64":
		return &huma.Schema{Type: "number"}
	case "bool":
		return &huma.Schema{Type: "boolean"}
	case "time.Time":
		return &huma.Schema{Type: "string", Format: "date-time"}
	case "[]byte":
		return &huma.Schema{Type: "string", Format: "byte"}
	case "string":
		return &huma.Schema{Type: "string"}
	default:
		return &huma.Schema{Type: "string"}
	}
}

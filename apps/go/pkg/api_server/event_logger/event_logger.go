package event_logger

import (
	"fmt"
	"sync"
	"time"
)

type EventLogger struct {
	events        []ApiEvent
	mutex         sync.Mutex
	batchSize     int
	flushInterval time.Duration
}

func NewEventLogger(batchSize int, flushInterval time.Duration) *EventLogger {
	logger := &EventLogger{
		events:        make([]ApiEvent, 0, batchSize),
		batchSize:     batchSize,
		flushInterval: flushInterval,
	}

	go logger.periodicFlush()

	return logger
}

func (logger *EventLogger) LogEvent(event ApiEvent) error {
	logger.mutex.Lock()
	logger.events = append(logger.events, event)

	shouldFlush := len(logger.events) >= logger.batchSize
	logger.mutex.Unlock()

	if shouldFlush {
		return logger.Flush()
	}

	return nil
}

func (logger *EventLogger) Flush() error {
	logger.mutex.Lock()
	if len(logger.events) == 0 {
		logger.mutex.Unlock()
		return nil
	}

	eventsToFlush := make([]ApiEvent, len(logger.events))
	copy(eventsToFlush, logger.events)
	logger.events = logger.events[:0]
	logger.mutex.Unlock()

	for _, event := range eventsToFlush {
		fmt.Printf("%+v", event)
	}

	return nil

}

func (logger *EventLogger) periodicFlush() {
	ticker := time.NewTicker(logger.flushInterval)
	defer ticker.Stop()

	for range ticker.C {
		if err := logger.Flush; err != nil {
			// Handle error (log it, metrics, etc.)
		}
	}
}

package db_sync

import (
	"encoding/json"
	"log"
	"log/slog"
	"os"
	"strings"
	"time"

	"github.com/hibiken/asynq"
	"gitlab.com/lexunit/hupx-labs/pkg/configuration"
)

func handleEnqueueError(task *asynq.Task, _ []asynq.Option, err error) {
	slog.Error("failed to enqueue task", "taskType", task.Type(), "err", err)
}

func getTimeout(dataset configuration.DatasetResolved) time.Duration {
	if env := os.Getenv("ENV"); strings.ToLower(env) == "dev" {
		return 180 * time.Second
	}
	return time.Duration(dataset.TimeoutSeconds) * time.Second
}

func getFrequency(dataset configuration.DatasetResolved) string {
	if env := os.Getenv("ENV"); strings.ToLower(env) == "dev" {
		return "*/3 * * * *"
	}
	return dataset.FrequencyCron
}

func startScheduler() {
	scheduler := asynq.NewScheduler(
		redisConnOpt(),
		&asynq.SchedulerOpts{
			LogLevel:            getLogLevel(),
			EnqueueErrorHandler: handleEnqueueError,
		},
	)
	config, err := configuration.ParseConfigDir(configuration.GetConfigPath())
	if err != nil {
		log.Fatal(err)
	}

	inspector := asynq.NewInspector(redisConnOpt())
	queues, err := inspector.Queues()
	if err != nil {
		slog.Error("failed to get queues", "err", err)
		panic(err)
	}
	for _, queue := range queues {
		inspector.DeleteAllArchivedTasks(queue)
	}

	for _, dataset := range config.Datasets {
		// Where apiQuery is present, we don't need sync process
		// Possible TODO: If we later decide we want to copy data and also
		// modify the api query, update this
		if dataset.ApiQuery != "" {
			continue
		}

		// if neither query nor apiQuery is present, we don't need sync process
		// this is the case for some calculated & manually inserted datasets
		// like the ones related to Twin EU
		if dataset.Query == "" && dataset.ApiQuery == "" {
			continue
		}

		payload, err := json.Marshal(dataset)
		if err != nil {
			slog.Error("could not marshal dataset", "err", err)
			panic(err)
		}
		task := asynq.NewTask(
			TypeDbSyncTask,
			payload,
			asynq.MaxRetry(0),
			asynq.Timeout(getTimeout(dataset)),
		)
		entryId, err := scheduler.Register(getFrequency(dataset), task)
		if err != nil {
			slog.Error("could not register task", "task", task.Type(), "err", err)
			panic(err)
		}
		slog.Info("registered an entry", "entryId", entryId)
	}

	if err := scheduler.Run(); err != nil {
		slog.Error("failed to run task scheduler", "err", err)
	}
}

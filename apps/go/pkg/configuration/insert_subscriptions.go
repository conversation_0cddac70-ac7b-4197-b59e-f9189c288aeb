package configuration

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/google/uuid"
)

// InsertSubscriptions ensures all subscriptions from the configuration exist in the database
func InsertSubscriptions(ctx context.Context, db *sql.DB, config *ConfigResolved) error {
	// Start a transaction
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback() // Will be ignored if transaction is committed

	// For each dataset, ensure its subscriptions exist
	for _, dataset := range config.Datasets {
		for _, subscription := range dataset.Subscriptions {
			// Check if subscription already exists
			var exists bool
			err := tx.QueryRowContext(ctx,
				`SELECT EXISTS(
					SELECT 1 FROM "Subscription" 
					WHERE "dataSetName" = $1 AND "subscriptionName" = $2
				)`,
				dataset.Name,
				subscription.Name,
			).Scan(&exists)
			if err != nil {
				return fmt.E<PERSON>rf("failed to check subscription existence: %w", err)
			}

			// Skip if subscription already exists
			if exists {
				slog.Debug("subscription already exists",
					"dataSetName", dataset.Name,
					"subscriptionName", subscription.Name)
				continue
			}

			// Insert new subscription
			_, err = tx.ExecContext(ctx,
				`INSERT INTO "Subscription" (
					id,
					"dataSetName",
					"subscriptionName",
					"createdAt"
				) VALUES (
					gen_random_uuid(),
					$1,
					$2,
					$3
				)`,
				dataset.Name,
				subscription.Name,
				time.Now().UTC(),
			)
			if err != nil {
				return fmt.Errorf("failed to insert subscription: %w", err)
			}

			slog.Info("inserted new subscription",
				"dataSetName", dataset.Name,
				"subscriptionName", subscription.Name)
		}
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

type Sub struct {
	DataSetName      string
	SubscriptionName string
}

func InsertSubscriptionPackage(ctx context.Context, db *sql.DB, name string, priceEur int, subscriptions []Sub) error {
	tx, err := db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback() // Will be ignored if transaction is committed

	var exists bool
	err = tx.QueryRowContext(ctx,
		`SELECT EXISTS(
			SELECT 1 FROM "SubscriptionPackage"
			WHERE name = $1
		)`,
		name,
	).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check subscription package existence: %w", err)
	}
	if exists {
		slog.Debug("subscription package already exists", "name", name)
		return nil
	}

	var packageID string
	now := time.Now().UTC()
	err = tx.QueryRowContext(ctx,
		`INSERT INTO "SubscriptionPackage" (
			id,
			name,
			"priceEur",
			"createdAt",
			"updatedAt"
		) VALUES (
			gen_random_uuid(),
			$1,
			$2,
			$3,
			$3
		) RETURNING id`,
		name,
		priceEur,
		now,
	).Scan(&packageID)
	if err != nil {
		return fmt.Errorf("failed to insert subscription package: %w", err)
	}

	for _, sub := range subscriptions {
		var subID string
		err := tx.QueryRowContext(ctx,
			`SELECT id FROM "Subscription" 
			WHERE "dataSetName" = $1 AND "subscriptionName" = $2`,
			sub.DataSetName,
			sub.SubscriptionName,
		).Scan(&subID)
		if err != nil {
			if err == sql.ErrNoRows {
				continue
			}
			return fmt.Errorf("failed to find subscription: %w", err)
		}

		uuid := uuid.NewString()
		today := strings.Split(time.Now().UTC().Format(time.RFC3339), "Z")[0]
		_, err = tx.ExecContext(ctx,
			`INSERT INTO "SubscriptionToPackage" (
				"id",
				"createdAt",
				"subscriptionId",
				"subscriptionPackageId"
			) VALUES (
				$1,
				$2,
				$3,
				$4
			) ON CONFLICT DO NOTHING`,
			uuid,
			today,
			subID,
			packageID,
		)
		if err != nil {
			return fmt.Errorf("failed to connect subscription to package: %w", err)
		}
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

func InsertSubscriptionPackages(ctx context.Context, db *sql.DB) error {
	idcFullPackage := Sub{
		DataSetName:      "IDC",
		SubscriptionName: "Full",
	}
	idcHourlyFullPackage := Sub{
		DataSetName:      "IDC_Hourly",
		SubscriptionName: "Full",
	}
	idcQuarterHourlyFullPackage := Sub{
		DataSetName:      "IDC_QuarterHourly",
		SubscriptionName: "Full",
	}
	idaAggrCurveFullPackage := Sub{
		DataSetName:      "IDA_Aggregated_curve",
		SubscriptionName: "Full",
	}

	idaQuarterHourlyFullPackage := Sub{
		DataSetName:      "IDA_QuarterHourly",
		SubscriptionName: "Full",
	}

	idaMcFlowsFullPackage := Sub{
		DataSetName:      "IDA_MC_Flows",
		SubscriptionName: "Full",
	}

	isiFullPackage := Sub{
		DataSetName:      "Imbalance_settlement_inputs",
		SubscriptionName: "Full",
	}

	damAggrTradingDataFullPackage := Sub{
		DataSetName:      "DAM_Aggregated_Trading_Data",
		SubscriptionName: "Full",
	}

	damIcFlowsFullPackage := Sub{
		DataSetName:      "DAM_IC_Flows",
		SubscriptionName: "Full",
	}

	damAggrCurveFullPackage := Sub{
		DataSetName:      "DAM_Aggregated_curve",
		SubscriptionName: "Full",
	}

	type subPackage struct {
		name     string
		priceEur int
		subs     []Sub
	}

	subPacks := []subPackage{
	    {
			name:     "HUPX essentials",
			priceEur: 450,
			subs: []Sub{
				isiFullPackage,
				damAggrTradingDataFullPackage,
				damIcFlowsFullPackage,
				idaQuarterHourlyFullPackage,
				idaMcFlowsFullPackage,
				idcFullPackage,
				idcHourlyFullPackage,
				idcQuarterHourlyFullPackage,
			},
		},
		{
			name:     "HUPX expert",
			priceEur: 750,
			subs: []Sub{
				isiFullPackage,
				damAggrTradingDataFullPackage,
				damIcFlowsFullPackage,
				damAggrCurveFullPackage,
				idaQuarterHourlyFullPackage,
				idaMcFlowsFullPackage,
				idaAggrCurveFullPackage,
				idcFullPackage,
				idcHourlyFullPackage,
				idcQuarterHourlyFullPackage,
			},
		},
		{
			name:     "DAM expert",
			priceEur: 300,
			subs: []Sub{
				damAggrTradingDataFullPackage,
				damIcFlowsFullPackage,
				damAggrCurveFullPackage,
			},
		},
		{
			name:     "IDA expert",
			priceEur: 300,
			subs: []Sub{
				idaQuarterHourlyFullPackage,
				idaMcFlowsFullPackage,
				idaAggrCurveFullPackage,
			},
		},
		{
			name:     "IDC expert",
			priceEur: 300,
			subs: []Sub{
				idcFullPackage,
				idcHourlyFullPackage,
				idcQuarterHourlyFullPackage,
			},
		},
		{
			name:     "IDC orders delayed",
			priceEur: 900,
			subs: []Sub{
				isiFullPackage,
			},
		},
		{
			name:     "IDC trades delayed",
			priceEur: 900,
			subs: []Sub{
				isiFullPackage,
			},
		},
		{
			name:     "IDC orders and trades delayed",
			priceEur: 1500,
			subs: []Sub{
				isiFullPackage,
			},
		},
		{
			name:     "Additional M7 API user",
			priceEur: 1000,
			subs: []Sub{
				isiFullPackage,
			},
		},
		{
			name:     "Historical IDC orders and trades via sFTP",
			priceEur: 250,
			subs: []Sub{},
		},
		{
			name:     "HU Futures",
			priceEur: 150,
			subs: []Sub{
				isiFullPackage,
			},
		},
		{
			name:     "Additional Trayport user (Trading system)",
			priceEur: 1050,
			subs: []Sub{
				isiFullPackage,
			},
		},
	}

	for _, sp := range subPacks {
		if err := InsertSubscriptionPackage(ctx, db, sp.name, sp.priceEur, sp.subs); err != nil {
			return err
		}
	}

	return nil
}

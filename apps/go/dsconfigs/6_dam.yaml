name: DAM_IC_Flows
apiVersions:
  - v1
database: HUPX_BOSS_DAM
batchsize: 1000
frequencyCron: "30 12 * * *"  # The hour is in UTC
timeoutSeconds: 600
dataViewUrlName: MC flows
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2024-01-01'''
  - name: Public
    filter:
      - '"DeliveryDay" > now() - interval ''1 months'''
    limit: 10
defaultOrder:
  - key: DeliveryDay
    order: desc
  - key: Hours
    order: desc
query: >
  USE [HUPX_BOSS_DAM]
  DECLARE @Results TABLE (
    ID int,
    Date_Code datetime,
    Border_Code int,
    Instrument_code varchar(5),
    H1 decimal(38,18),
    H2 decimal(38,18),
    H3 decimal(38,18),
    H3B decimal(38,18),
    H4 decimal(38,18),
    H5 decimal(38,18),
    H6 decimal(38,18),
    H7 decimal(38,18),
    H8 decimal(38,18),
    H9 decimal(38,18),
    H10 decimal(38,18),
    H11 decimal(38,18),
    H12 decimal(38,18),
    H13 decimal(38,18),
    H14 decimal(38,18),
    H15 decimal(38,18),
    H16 decimal(38,18),
    H17 decimal(38,18),
    H18 decimal(38,18),
    H19 decimal(38,18),
    H20 decimal(38,18),
    H21 decimal(38,18),
    H22 decimal(38,18),
    H23 decimal(38,18),
    H24 decimal(38,18)
  )
  INSERT INTO @Results
  EXEC [dbo].[spHPXDamReportEpexIcFlow] 2025
  SELECT CAST(Date_Code as date) as "DeliveryDay",
         SUBSTRING(Hours, 2, LEN(Hours)) as Hours,
         MAX(CASE WHEN [Instrument_code] = 'HU-AT' THEN value END) as "HU_AT",
         MAX(CASE WHEN [Instrument_code] = 'AT-HU' THEN value END) as "AT_HU",
         MAX(CASE WHEN [Instrument_code] = 'HU-HR' THEN value END) as "HU_HR",
         MAX(CASE WHEN [Instrument_code] = 'HR-HU' THEN value END) as "HR_HU",
         MAX(CASE WHEN [Instrument_code] = 'HU-RO' THEN value END) as "HU_RO",
         MAX(CASE WHEN [Instrument_code] = 'RO-HU' THEN value END) as "RO_HU",
         MAX(CASE WHEN [Instrument_code] = 'HU-SI' THEN value END) as "HU_SI",
         MAX(CASE WHEN [Instrument_code] = 'SI-HU' THEN value END) as "SI_HU",
         MAX(CASE WHEN [Instrument_code] = 'HU-SK' THEN value END) as "HU_SK",
         MAX(CASE WHEN [Instrument_code] = 'SK-HU' THEN value END) as "SK_HU"
  FROM (
      SELECT "Date_Code", [Instrument_code], H1, H2, H3, H4, H5, H6, H7, H8, H9, H10, H11, H12, H13, H14, H15, H16, H17, H18, H19, H20, H21, H22, H23, H24
      FROM @Results
      WHERE "Date_Code" > '%v'
  ) src
  UNPIVOT (
      value FOR Hours IN (H1, H2, H3, H4, H5, H6, H7, H8, H9, H10, H11, H12, H13, H14, H15, H16, H17, H18, H19, H20, H21, H22, H23, H24)
  ) as unpvt
  GROUP BY "Date_Code", Hours
  ORDER BY "Date_Code" DESC, CAST(SUBSTRING(Hours, 2, LEN(Hours)) as int) ASC
queryFilter:
  defaultValue: "2024-01-01"
  query: 'SELECT "DeliveryDay" FROM hupx_boss_dam_dam_ic_flows ORDER BY "DeliveryDay" DESC LIMIT 1'
  key: DeliveryDay
tags:
  - Day-Ahead Market
summary: "DAM IC Flows- Summary"
description: >
  The Market Coupling (MC) Flow dataset provides details on cross-border flows
  scheduled for a specific delivery day in the HUPX Day-Ahead market.
  It includes the cross-zonal flows, categorized by all available borders in
  both directions.
  
  The dataset is updated daily between 12:00 and 13:00, following the
  conclusion of the Day-Ahead auction for the corresponding delivery day.
plot:
  x: DeliveryDay
  y: HU_AT
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Hours
    title: "Hour"
    type: int
    unit: CET
    description: >
      Indicates the relevant delivery hour of the contracts for a specific day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: HU_AT
    title: "HU>AT"
    type: float
    unit: "MW"
    description: HU > AT direction flow.
  - name: AT_HU
    title: "AT>HU"
    type: float
    unit: "MW"
    description: AT > HU direction flow.
  - name: HU_HR
    title: "HU>HR"
    type: float
    unit: "MW"
    description: HU > HR direction flow.
  - name: HR_HU
    title: "HR>HU"
    type: float
    unit: "MW"
    description: HR > HU direction flow.
  - name: HU_RO
    title: "HU>RO"
    type: float
    unit: "MW"
    description: HU > RO direction flow.
  - name: RO_HU
    title: "RO>HU"
    type: float
    unit: "MW"
    description: RO > HU direction flow.
  - name: HU_SI
    title: "HU>SI"
    type: float
    unit: "MW"
    description: HU > SI direction flow.
  - name: SI_HU
    title: "SI>HU"
    type: float
    unit: "MW"
    description: SI > HU direction flow.
  - name: HU_SK
    title: "HU>SK"
    type: float
    unit: "MW"
    description: HU > SK direction flow.
  - name: SK_HU
    title: "SK>HU"
    type: float
    unit: "MW"
    description: SK > HU direction flow.

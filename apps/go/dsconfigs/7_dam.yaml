name: DAM_Aggregated_curve
apiVersions:
  - v1
database: HUPX_BOSS_DAM
batchsize: 1000
#frequencyCron: "30 13 * * *"
frequencyCron: "*/10 * * * *"
timeoutSeconds: 600
dataViewUrlName: Aggregated curves
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2024-01-01'''
  - name: Public
    filter:
      - '"DeliveryDay" > now() - interval ''1 months'''
    limit: 10
defaultOrder:
  - key: DeliveryDay
    order: desc
  - key: Hour
    order: desc
  - key: Price
    order: asc
query: >
  USE [HUPX_BOSS_DAM]
  DECLARE @Results TABLE (
    DeliveryDay date,
    [Hour] varchar(3),
    Region varchar(2),
    Price money,
    BuyVolume money,
    SellVolume money
  )
  INSERT INTO @Results
  EXEC [dbo].[spHPXDamReportEpexAggregatedCurve] '%v'
  SELECT DeliveryDay,
    CAST(SUBSTRING([Hour], 2, LEN([Hour])) as int) as [Hour],
    Region,
    Price,
    BuyVolume,
    SellVolume
  FROM @Results
  ORDER BY "DeliveryDay" DESC
queryFilter:
  defaultValue: "2024-10-29"
  query: SELECT DISTINCT "DeliveryDay" + interval '1 day' as "NextDay" FROM hupx_boss_dam_dam_aggregated_curve ORDER BY "NextDay" DESC LIMIT 1
  key: DeliveryDay
tags:
  - Day-Ahead Market
summary: "DAM Aggregated Curve"
description: >
  The DAM aggregated curve shows the supply and demand curve for each hour of
  the different delivery days. The aggregated demand and supply curves show
  the linear (single hours) orders aggregated, complemented with the executed
  block volumes, the net position and the physical delivery orders from HUDEX.
  The market clearing price is defined where the demand curve and supply curve
  meet. The market clearing volume shall be determined by linear interpolation.
  
  The dataset is updated daily between 12:00 and 13:00, following the conclusion
  of the Day-Ahead auction for the corresponding delivery day.
extra:
  singleDateFilter: true
plot:
  x: DeliveryDay
  y: Price
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Hour
    title: "Hour"
    type: int
    unit: CET
    description: >
      Indicates the relevant delivery hour of the contracts for a specific day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Region
    title: "Region"
    type: string
    unit: "-"
    description: The name of the market area of the auction.
  - name: Price
    title: "Price"
    type: float
    unit: €/MWh
    description: The price level where the order was placed.
  - name: BuyVolume
    title: "Buy volume"
    type: float
    unit: MWh
    description: Buy side merit orders from the aggregated demand curve.
  - name: SellVolume
    title: "Sell volume"
    type: float
    unit: MWh
    description: Sell side merit orders from the aggregated supply curve.

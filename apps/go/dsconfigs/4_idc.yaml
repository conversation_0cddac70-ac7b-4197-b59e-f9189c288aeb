name: IDC
apiVersions:
  - v1
database: HUPX_BOSS_ID
batchsize: 1000
frequencyCron: "*/2 * * * *"
timeoutSeconds: 280
dataViewUrlName: Trades
subscriptions:
  - name: Full
    filter:
      - '"ExecutionTime" > ''2024-01-01'''
defaultOrder:
  - key: TradeID
    order: desc
query: >
  USE [HUPX_BOSS_ID]
  SELECT vot.TradeID, vot.Quantity, 'MW' as QuantityUnit, vot.Price, 'EUR' as Currency, ExecutionTime, ProductName,
  Name, IsPredefined, DeliveryStart as DeliveryStartDay, DeliveryEnd as DeliveryEndDay,
  SUBSTRING(Name, CHARINDEX(' ', Name) + 1, 5) AS DeliveryStartTime, RIGHT(Name, 5) AS DeliveryEndTime, Duration,
  BuyDeliveryAreaID, Volume, HourOfExecution, HourOfDelivery, Local, vot.RemoteTradeId,
  vot.BuyRemoteOrdrId, vot.SellRemoteOrdrId, SellDeliveryAreaID, BuyOrderID, SellOrderID
  FROM vOlapTrades vot
  JOIN Trade t on vot.TradeID = t.TradeID
  WHERE vot.TradeID > %v AND t.TradeID > %v
  ORDER BY TradeID ASC;
queryFilter:
  defaultValue: 500000000
  query: 'SELECT "TradeID" FROM hupx_boss_id_idc ORDER BY "TradeID" DESC LIMIT 1'
  key: TradeID
tags:
  - Intraday Continuous Market
summary: "IDC - Trades data"
description: >
  Intraday trades contains all transactions executed on HUPX Intraday
  Continuous Market, offering detailed insights into each trade's key
  attributes. The report includes trade identifiers formatted in the same way
  as those used in XBID, allowing users to connect the provided information
  with other markets within SIDC.

  The dataset is updated every 2 minutes, providing users with a real-time
  snapshot of the HUPX Intraday market dynamics.

  The candles illustrate the price volatility of each hourly and quarter-hourly
  product traded on the HUPX Intraday Continuous Market. The average price of
  a specific product is positioned at the center of the box, with the standard
  deviation subtracted and added to determine the height of the box.
  The bottom and top of the candles represent the minimum and maximum prices
  at which a specific product was traded.
plot:
  x: ExecutionTime
  y: Price
  group: side
  type: scatter
fields:
  - name: TradeID
    title: "TradeID"
    type: bigint
    unit: "-"
    description: Unique identifier of the trade.
  - name: Quantity
    title: "Quantity"
    type: float
    unit: MW
    description: The quantity of the traded volume.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: QuantityUnit
    title: "QuantityUnit"
    type: string
    unit: "-"
    description: >
      The unit in which the quantity is given. The value of this filed is MW.
  - name: Price
    title: "Price"
    type: float
    unit: EUR
    description: The price the trade was realised.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Currency
    title: "Currency"
    type: string
    unit: "-"
    description: >
      The currency of the price. The currency of the price is given in EUR.
  - name: ExecutionTime
    title: "Execution time"
    type: datetimeoffset
    unit: UTC
    description: The TimeStamp when the trade was realised.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: ProductName
    title: "Product name"
    type: string
    unit: "-"
    description: The name of the traded product.
    filters:
      - eq
  - name: Name
    title: "Name"
    type: string
    unit: "-"
    description: >
      The extended name of the product, that contains the delivery end and
      delivery start information.
  - name: IsPredefined
    title: "Predefined"
    type: bit
    unit: "-"
    description: >
      This field has two possible values: TRUE or FALSE.
      TRUE: Predefined contract FALSE: User defined block contract.
  - name: DeliveryStartDay
    title: "Start day of delivery"
    type: date
    unit: CET
    description: >
      The starting day of the delivery for the underlying contract.
      Format of this filed: yyyy.MM.dd
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: DeliveryEndDay
    title: "End day of delivery"
    type: date
    unit: CET
    description: >
      The ending day of the delivery for the underlying contract.
      Format of this filed: yyyy.MM.dd
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: DeliveryStartTime
    title: "Start time of delivery"
    type: string
    unit: CET
    description: >
      The starting hour and minute of the delivery for the underlying contract.
      Format of this filed: hh:mm:ss
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: DeliveryEndTime
    title: "End time of delivery"
    type: string
    unit: CET
    description: >
      The ending hour and minute of the delivery for the underlying contract.
      Format of this filed: hh:mm:ss
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Duration
    title: "Duration"
    type: float
    unit: "-"
    description: >
      The duration of the product. If Predefined = True it can have
      0.25 or 1 as possible values. If Predefined = False the duration can
      be different, depending on the underlying block contract.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: BuyDeliveryAreaID
    title: "Buy delivery areaID"
    type: string
    unit: "-"
    description: The ID of the BUY side Delivery area.
    filters:
      - eq
  - name: Volume
    title: "Volume"
    type: float
    unit: MWh
    description: Traded volume given in MWh.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: HourOfExecution
    title: "HourOfExecution"
    type: int
    unit: CET
    description: The exact hour when the trade was realised.
  - name: HourOfDelivery
    title: "HourOfDelivery"
    type: int
    unit: CET
    description: The hour when the traded product is delivered.
  - name: Local
    title: "Local"
    type: string
    unit: "-"
    description: >
      This field has two possible values: TRUE or FALSE. TRUE: Local trade
      FALSE: Cross-border trade.
    valueMappings:
      - from: HAMIS
        to: "FALSE"
      - from: IGAZ
        to: "TRUE"
  - name: RemoteTradeId
    title: "Remote tradeID"
    type: bigint
    unit: "-"
    description: Unique identifier of the trade in XBID.
  - name: BuyRemoteOrdrId
    title: "Buy remote orderID"
    type: bigint
    unit: "-"
    description: The OrderID of the BUY side from XBID.
  - name: SellRemoteOrdrId
    title: "Sell remote orderID"
    type: bigint
    unit: "-"
    description: The OrderID of the SELL side from XBID.
  - name: SellDeliveryAreaID
    title: "Sell delivery areaID"
    type: string
    unit: "-"
    description: The ID of the SELL side Delivery area.
    filters:
      - eq
  - name: BuyOrderID
    title: "Buy orderID"
    type: bigint
    unit: "-"
    description: >
      The OrderID of the BUY side from the OrderBook. If the BUY order is from
      across border this field is empty. The identifier is equal with the
      OrderID element of the Orderbook report.
  - name: SellOrderID
    title: "Sell orderID"
    type: bigint
    unit: "-"
    description: >
      The OrderID of the SELL side from the OrderBook. If the SELL order is
      from across border this field is empty. The identifier is equal with
      the OrderID element of the Orderbook report.

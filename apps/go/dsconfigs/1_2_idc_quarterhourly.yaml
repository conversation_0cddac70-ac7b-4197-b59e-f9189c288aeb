name: IDC_QuarterHourly
apiVersions:
  - v1
database: HUPX_BOSS_ID
batchsize: 1000
frequencyCron: "*/2 * * * *"
timeoutSeconds: 600
dataViewUrlName: Aggregated trading data - QH
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDate" > ''2020-01-01'''
  - name: Public
    filter:
      - '"DeliveryDate" > now() - interval ''5 years'''
    limit: 10
defaultOrder:
  - key: DeliveryDate
    order: desc
  - key: Product
    order: desc
query: >
  USE [HUPX_BOSS_ID]
  
  DECLARE @date_filter datetime = '%v';
  
  WITH HourlyData AS (
      SELECT 
          TradeDate as DeliveryDate,
          Last3HVWAP as PriceVWAPLast3Hour,
          Last3HVol as VolumeLast3Hour
      FROM dbo.RD_AggregatedContractData
      WHERE Product = 'H'
      -- -2 hours is important, otherwise not all rows from the QH select will have ID3
      -- And it caused a nice subtle bug
      AND TradeDate > DATEADD(HOUR, -2, @date_filter)
      AND TradeDate < GETDATE()
  ),
  QuarterlyExpanded AS (
      SELECT 
          DATEADD(MINUTE, v.minutes, h.DeliveryDate) as DeliveryDate,
          h.<PERSON>VWAPLast3Hour,
          h.VolumeLast3Hour
      FROM HourlyData h
      CROSS JOIN (VALUES 
          (0),
          (15),
          (30),
          (45)
      ) as v(minutes)
  )

  SELECT
    TradeDate as DeliveryDate,
    Instrument as Product,
    BestBid,
    BestAsk,
    VolumeWeightedAveragePriceEUR as PriceVWAPLast,
    LastTradePrice as PriceVWAPLastHour,
    Last3HVWAP as PriceVWAPLast3Hour,
    Last3HVol as VolumeLast3Hour,
    Buy_volume_MW as VolumeBuyMW,
    Sell_volume_MW as VolumeSellMW,
    Buy_volume_MWh as VolumeBuyMWh,
    Sell_volume_MWh as VolumeSellMWh,
    Import_volume as VolumeImport,
    Export_volume as VolumeExport,
    Net_position as NetPosition,
    Buy_volume_MW + Export_volume as VolumeTotalTraded,
    (hh.PriceVWAPLast3Hour * hh.VolumeLast3Hour + Last3HVWAP * Last3HVol) / (hh.VolumeLast3Hour + Last3HVol) as ID3Index,
    LastUpdateTimeStamp
  FROM dbo.RD_AggregatedContractData
  JOIN QuarterlyExpanded hh ON hh.DeliveryDate = TradeDate
  WHERE Product = 'QH' and TradeDate > @date_filter and TradeDate < GETDATE()
  ORDER BY DeliveryDate DESC
queryFilter:
  defaultValue: 2024-01-01
  query: 'SELECT "DeliveryDate"::timestamp FROM hupx_boss_id_idc_quarterhourly ORDER BY "DeliveryDate" DESC LIMIT 1'
  key: DeliveryDate
deleteBeforeUpdateQuery: 'DELETE FROM hupx_boss_id_idc_quarterhourly WHERE "DeliveryDate" > (NOW() - INTERVAL ''36 hours'')'
tags:
  - Intraday Continuous Market
summary: "IDC QuarterHourly - Summary"
description: >
  The aggregated trading data from the HUPX Intraday Continuous Market (IDC)
  provides market results for all the available products that can be traded on
  the IDC market of HUPX. The dataset includes various dimensions of trading
  activity for a product, beyond the market price and the total traded volume.
  
  Currently, the dataset includes market data for products where trading has
  closed, with updates provided immediately after trading ends for specific
  products. In the coming months, we plan to increase the update frequency
  and provide data for all products traded within a specific time period.

plot:
  x: "DeliveryDate"
  y: "BestBid"
  group: "Product"
  type: "line"
fields:
  - name: DeliveryDate
    title: "Delivery Date"
    type: datetime
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Product
    title: "Product"
    type: string
    unit: "-"
    description: Name of the product.
    filters:
      - eq
  - name: BestBid
    title: "Best bid"
    type: float
    unit: €/MWh
    description: >
      The purchase offer submitted at the highest price for the given
      contract for the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: BestAsk
    title: "Best ask"
    type: float
    unit: €/MWh
    description: >
      The sell offer submitted at the lowest price for the given contract for
      the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: PriceVWAPLast
    title: "Volume Weighted Average Price"
    type: float
    unit: €/MWh
    description: >
      The volume-weighted average price of all deals concluded for the given
      contract during the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: PriceVWAPLastHour
    title: "VWAP of the last trading hour"
    type: float
    unit: €/MWh
    description: >
      The volume-weighted average price of the transactions concluded for the
      given contract. During the calculation, only deals concluded in the
      last hour before the expiration of the contract's tradability
      are considered.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: PriceVWAPLast3Hour
    title: "VWAP of the last 3 trading hours"
    type: float
    unit: €/MWh
    description: >
      Volume weighted average price - Considering the last 3 trading hours,
      calculated only from the trades of hourly products for the hourly data
      and from the trades of the quarterly hour products for the quarterly
      hour data (based on trade execution date time).
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeLast3Hour
    title: "Traded volume of the last 3 trading hours"
    type: float
    unit: MW
    description: >
      The amount of hourly products traded in the last 3 trading hours
      (displayed in the hourly data) and the amount of quarterly hour products
      traded in the last 3 trading hours (displayed in the quarterly hour data)
      - (based on trade execution date time).
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeBuyMW
    title: "Buy volume"
    type: float
    unit: MW
    description: >
      The amount of electricity purchased by HUPX members on the exchange for
      the given contract during the entire trading period in MW.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeSellMW
    title: "Sell volume"
    type: float
    unit: MW
    description: >
      The amount of electricity sold by HUPX members on the exchange for the
      given contract during the entire trading period in MW.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeBuyMWh
    title: "Buy volume - MWh"
    type: float
    unit: MWh
    description: >
      The amount of electricity purchased by HUPX members on the exchange for
      the given contract during the entire trading period in MWh.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeSellMWh
    title: "Sell volume - MWh"
    type: float
    unit: MWh
    description: >
      The amount of electricity sold by HUPX members on the exchange for the
      given contract during the entire trading period in MWh.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeImport
    title: "Import Volume"
    type: float
    unit: MW
    description: >
      The amount of electricity imported by HUPX members through SIDC borders.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeExport
    title: "Export Volume"
    type: float
    unit: MW
    description: >
      The amount of electricity exported by HUPX members through SIDC borders.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: NetPosition
    title: "Net position"
    type: float
    unit: MW
    description: >
      In relation to the Hungarian bidding zone, the netted amount of
      electricity exports and imports belonging to each contract.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeTotalTraded
    title: "Traded volume"
    type: float
    unit: MW
    description: >
      The amount of electricity traded by HUPX members on the exchange for the
      given contract (buy traded volume + export volume equal to sell traded
      volume + import volume).
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: ID3Index
    title: "ID3 index"
    type: float
    unit: €/MWh
    description: >
      HUPX ID3 index is calculated from the last three hours of trading
      activity preceding GCT, representing a volume-weighted average price of
      the given period. It merges trading activity from both hourly and
      quarter-hourly contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: LastUpdateTimeStamp
    title: "Last update timestamp"
    type: datetime
    unit: CET
    description: The timestamp of the last update for the given row.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte

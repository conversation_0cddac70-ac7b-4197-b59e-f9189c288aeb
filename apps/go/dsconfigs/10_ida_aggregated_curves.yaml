name: IDA_Aggregated_curve
apiVersions:
  - v1
database: HUPX_BOSS_IDA
batchsize: 1000
#frequencyCron: "30 13 * * *"
frequencyCron: "*/10 * * * *"
timeoutSeconds: 600
dataViewUrlName: Aggregated curves
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2020-01-01'''
  - name: Public
    filter:
      - '"DeliveryDay" > now() - interval ''5 years'''
    limit: 10
defaultOrder:
  - key: DeliveryDay
    order: desc
  - key: QuarterHour
    order: desc
  - key: Price
    order: asc
query: >
  USE [HUPX_BOSS_IDA]
  --EXEC [dbo].[spHPXIdaReportAggregatedCurve] '2025-01-20'

  DECLARE @Results TABLE (
    AuctionTypeId int,
    DeliveryDay date,
    Region varchar(2),
    Instrument varchar(5),
    InstrumentOrder int,
    Price decimal(38,18),
    BuyVolume decimal(38,18),
    SellVolume decimal(38,18)
  )
  INSERT INTO @Results
  EXEC [dbo].[spHPXIdaReportAggregatedCurve] '%v'

  SELECT DeliveryDay,
    CAST(SUBSTRING([Instrument], 3, LEN([Instrument])) as int) as QuarterHour,
    Region,
    AuctionTypeId as AuctionID,
    Price,
    BuyVolume,
    SellVolume
  FROM @Results
  ORDER BY DeliveryDay,
    QuarterHour,
    Region,
    AuctionTypeId
queryFilter:
  defaultValue: "2024-10-29"
  query: SELECT DISTINCT "DeliveryDay" + interval '1 day' as "NextDay" FROM hupx_boss_ida_ida_aggregated_curve ORDER BY "NextDay" DESC LIMIT 1
  key: DeliveryDay
tags:
  - Intraday Auction Market
summary: "IDA Aggregated Curve"
description: >
  The IDA aggregated curve shows the supply and demand curve for each quarter 
  hour of the different delivery days. The market clearing price is defined 
  where the demand curve and supply curve meet. The market clearing volume 
  shall be determined by linear interpolation.
  
  Currently, the dataset is updated for all auctions after the completion of 
  the IDA3 auction, between 15:30 and 16:00. In the coming months, we plan to 
  increase the update frequency so that users can access the data immediately 
  after each IDA auction concludes.
extra:
  singleDateFilter: true
plot:
  x: DeliveryDay
  y: Price
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: QuarterHour
    title: "Quarter Hour"
    type: int
    unit: CET
    description: >
      Indicates the relevant delivery hour of the contracts for a specific day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: AuctionID
    title: "Auction ID"
    type: int
    unit: "-"
    description: The number of the IDA auction.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Region
    title: "Region"
    type: string
    unit: "-"
    description: The name of the market area of the auction.
  - name: Price
    title: "Price"
    type: float
    unit: €/MWh
    description: The price level where the order was placed.
  - name: BuyVolume
    title: "Buy volume"
    type: float
    unit: MWh
    description: Buy side merit orders from the aggregated demand curve.
  - name: SellVolume
    title: "Sell volume"
    type: float
    unit: MWh
    description: Sell side merit orders from the aggregated supply curve.

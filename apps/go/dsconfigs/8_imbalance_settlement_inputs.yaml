name: Imbalance_settlement_inputs
apiVersions:
  - v1
database: AGG
batchsize: 1000
frequencyCron: "0 13 * * *" # The hour is in UTC
timeoutSeconds: 600
dataViewUrlName: Imbalance settlement inputs
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2024-01-01'''
defaultOrder:
  - key: DeliveryDay
    order: desc
  - key: QuarterHour
    order: desc
apiQuery: >
  WITH quarters AS (
      SELECT generate_series(1, 4) as quarter_offset
  )
  select
    idaqh."DeliveryDay",
    idaqh."ProductQH" as "QuarterHour",
    damh."Price" as "DAMPrice",
    idch."PriceVWAPLast" as "IDCTHPrice",
    greatest (idch."VolumeSellMW", idch."VolumeBuyMW") / 4 as "IDCTHLiquidity",
    idcqh."PriceVWAPLast" as "IDCTQHPrice",
    greatest (idcqh."VolumeSellMW", idcqh."VolumeBuyMW") / 4 as "IDCTQHLiquidity",
    idch."PriceVWAPLast3Hour" as "IDC3HPrice",
    idch."VolumeLast3Hour" / 4 as "IDC3HVolume",
    idcqh."PriceVWAPLast3Hour" as "IDC3QHPrice",
    idcqh."VolumeLast3Hour" / 4 as "IDC3QHVolume",
    idaqh."PriceIDA1" as "IDA1Price",
    idaqh."VolumeIDA1" / 4 as "IDA1Volume",
    idaqh."PriceIDA2" as "IDA2Price",
    idaqh."VolumeIDA2" / 4 as "IDA2Volume",
    idaqh."PriceIDA3" as "IDA3Price",
    idaqh."VolumeIDA3" / 4 as "IDA3Volume"
  from hupx_boss_ida_ida_quarterhourly idaqh
  CROSS JOIN quarters q
  join hupx_boss_dam_dam_aggregated_trading_data damh on idaqh."DeliveryDay" = damh."DeliveryDay" and idaqh."ProductQH" = (damh."ProductH" - 1) * 4 + q.quarter_offset
  join hupx_boss_id_idc_hourly idch on idaqh."DeliveryDay" = DATE(idch."DeliveryDate") and idaqh."ProductQH" = (extract(hour from idch."DeliveryDate") * 4) + q.quarter_offset
  join hupx_boss_id_idc_quarterhourly idcqh on idaqh."DeliveryDay" = DATE(idcqh."DeliveryDate") and idaqh."ProductQH" = (extract(hour from idcqh."DeliveryDate") * 4) + (extract(minute from idcqh."DeliveryDate") / 15 + 1)
tags:
  - Prices and indices
summary: "Prices and indices - Summary"
description: >
  The imbalance settlement inputs provides an easy collection of the HUPX
  inputs used for the Hungarian Imbalance settlement methodology, effective
  from 1st January 2025. All the prices and volumes, necessary to calculate
  the market incentive price is included in this endpoint.
plot:
  x: DeliveryDay
  y: QuarterHour
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: QuarterHour
    title: "Quarter hour"
    type: int
    unit: "-"
    description: Number of the Imbalance Settlement Period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: DAMPrice
    title: "DAM Price"
    type: float
    unit: €/MWh
    description: The price of the respective DAM product.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDCTHPrice
    title: "IDCTH Price"
    type: float
    unit: €/MWh
    description: >
      The volume-weighted average price of all deals concluded for the
      respective IDC hourly product during the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDCTHLiquidity
    title: "IDCTH Liquidity"
    type: float
    unit: MWh
    description: >
      The quarter of the maximum of buy and sell volumes of all deals as
      defined in MAVIR imbalance settlement methodology concluded for the
      respective IDC hourly product during the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDCTQHPrice
    title: "IDCTQH Price"
    type: float
    unit: €/MWh
    description: >
      The volume-weighted average price of all deals concluded for the
      given IDC quarter-hourly product during the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDCTQHLiquidity
    title: "IDCTQH Liquidity"
    type: float
    unit: MWh
    description: >
      The maximum of buy and sell volumes of all deals as defined in MAVIR
      imbalance settlement methodology concluded for the given IDC
      quarter-hourly product during the entire trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDC3HPrice
    title: "IDC3H Price"
    type: float
    unit: €/MWh
    description: >
      The volume-weighted average price of all deals concluded for the
      respective IDC hourly product during the last 3 hours of the total
      trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDC3HVolume
    title: "IDC3H Volume"
    type: float
    unit: MWh
    description: >
      The quarter of the volume of all deals concluded for the respective
      IDC hourly product during the last 3 hours of the total trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDC3QHPrice
    title: "IDC3QH Price"
    type: float
    unit: €/MWh
    description: >
      The volume-weighted average price of all deals concluded for the given
      IDC quarter-hourly product during the last 3 hours of the total
      trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDC3QHVolume
    title: "IDC3QH Volume"
    type: float
    unit: MWh
    description: >
      The volume of all deals concluded for the given IDC quarter-hourly
      product during the last 3 hours of the total trading period.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDA1Price
    title: "IDA1 price"
    type: float
    unit: €/MWh
    description: Price of the IDA1 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDA1Volume
    title: "IDA1 volume"
    type: float
    unit: MWh
    description: Traded volume of the IDA1 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDA2Price
    title: "IDA2 price"
    type: float
    unit: €/MWh
    description: Price of the IDA2 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDA2Volume
    title: "IDA2 volume"
    type: float
    unit: MWh
    description: Traded volume of the IDA2 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDA3Price
    title: "IDA3 price"
    type: float
    unit: €/MWh
    description: Price of the IDA3 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IDA3Volume
    title: "IDA3 volume"
    type: float
    unit: MWh
    description: Traded volume of the IDA3 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte

name: IIP
apiVersions:
  - v1
database: EXT2_InsiderInfo_Data
batchsize: 1000
frequencyCron: "*/1 * * * *"
timeoutSeconds: 60
dataViewUrlName: Publications
subscriptions:
  - name: Full
    filter:
      - '"IntervalStart" > ''1990-01-01 00:00:00''' # everybody sees everything
  - name: Public
    filter:
      - '"IntervalStart" > ''1990-01-01 00:00:00''' # everybody sees everything
    limit: 10
defaultOrder:
  - key: ID
    order: desc
query: >
  USE [EXT2_InsiderInfo_Data]
  SELECT
    ph.Id as ID,
    ph.ModifiedId as ModifiedID,
    'v' + CAST(DENSE_RANK() OVER (PARTITION BY COALESCE(ph.ModifiedId, ph.Id)
                                    ORDER BY ph.Id ASC) AS VARCHAR(2)) as 'Version',
    ph.CreatedDate,
    MAX(CASE WHEN pv.FieldId = 3 THEN pv.Value END) as 'MarketParticipant',
    MAX(CASE WHEN pv.FieldId = 24 THEN pv.Value END) as 'EventStatus',
    MAX(CASE WHEN pv.FieldId = 38 THEN pv.ValueDateTime END) as 'IntervalStart',
    MAX(CASE WHEN pv.FieldId = 39 THEN pv.ValueDateTime END) as 'IntervalStop',
    MAX(CASE WHEN pv.FieldId = 25 THEN pv.Value END) as 'UnavailabilityType',
    MAX(CASE WHEN pv.FieldId = 9 THEN pv.Value END) as 'EventType',
    MAX(CASE WHEN pv.FieldId = 13 THEN pv.Value END) as 'AffectedAssetUnit',
    MAX(CASE WHEN pv.FieldId = 32 THEN pv.Value END) as 'AffectedEICCode',
    MAX(CASE WHEN pv.FieldId = 27 THEN pv.Value END) as 'MeasurementUnit',
    CAST(MAX(CASE WHEN pv.FieldId = 28 THEN pv.Value END) AS float) as 'UnavailableCapacity',
    CAST(MAX(CASE WHEN pv.FieldId = 15 THEN pv.Value END) AS float) as 'AvailableCapacity',
    CAST(MAX(CASE WHEN pv.FieldId = 33 THEN pv.Value END) AS float) as 'InstalledCapacity',
    MAX(CASE WHEN pv.FieldId = 19 THEN pv.Value END) as 'UnavailabilityReason',
    MAX(CASE WHEN pv.FieldId = 16 THEN pv.ValueDateTime END) as 'EventStart',
    MAX(CASE WHEN pv.FieldId = 17 THEN pv.ValueDateTime END) as 'EventStop',
    MAX(CASE WHEN pv.FieldId = 20 THEN pv.Value END) as 'Remarks',
    MAX(CASE WHEN pv.FieldId = 29 THEN pv.Value END) as 'FuelType',
    MAX(CASE WHEN pv.FieldId = 30 THEN pv.Value END) as 'BiddingZone'
  FROM PublicationHead ph
  LEFT JOIN PublicationValue pv ON ph.Id = pv.HeadId
  WHERE
    LanguageId = 1
    AND ph.Id > %v
  GROUP BY ph.Id, ph.ModifiedId, ph.CreatedDate
  HAVING MAX(CASE WHEN pv.FieldId = 21 THEN pv.Value END) = '1'
  ORDER BY ph.Id ASC;
queryFilter:
  defaultValue: 23000
  query: 'SELECT "ID" FROM ext2_insiderinfo_data_iip ORDER BY "ID" DESC LIMIT 1'
  key: ID
tags:
  - Inside information platform
summary: "IIP - summary"
description: >
  Inside information publications deliver critical announcements and updates
  on energy markets, with a primary focus on the European region. This dataset
  includes all publications from Market Participants registered on the platform
  operated by HUPX.
plot:
  x: "IntervalStart"
  y: "UnavailableCapacity"
  group: "MarketParticipant"
  type: "line"
fields:
  - name: ID
    primary: true
    title: "ID"
    type: int
    unit: "-"
    description: Unique identifier of the message.
  - name: ModifiedID
    title: "Modification ID"
    type: int
    unit: "-"
    description: Unique identifier of a modification for a specific message.
  - name: Version
    title: "Version"
    type: string
    unit: "-"
    description: >
      A comprehensible count of the modifications for a specific message.
  - name: CreatedDate
    title: "Created Date"
    type: datetime
    unit: "CET"
    description: The creation date of the publication.
  - name: MarketParticipant
    title: "Market Participant"
    type: string
    unit: "-"
    description: Name of the market participant.
  - name: EventStatus
    title: "Event Status"
    type: string
    unit: "-"
    description: >
      Status of the event. This field has 3 possible values: Active, dismissed,
      inactive.
    filters:
      - eq
  - name: IntervalStart
    title: "Interval Start"
    type: datetime
    unit: CET
    description: Start time of the interval on which the unavailability occurs.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: IntervalStop
    title: "Interval Stop"
    type: datetime
    unit: CET
    description: End time of the interval on which the unavailability occurs.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: UnavailabilityType
    title: "Type of Unavailability"
    type: string
    unit: "-"
    description: Exact type of the unavaibility.
    filters:
      - eq
  - name: EventType
    title: "Type of Event"
    type: string
    unit: "-"
    description: Exact type of the event.
  - name: AffectedAssetUnit
    title: "Affected Asset or Unit"
    type: string
    unit: "-"
    filters:
      - eq
    description: The name of the affected asset or unit.
  - name: AffectedEICCode
    title: "Affected Asset or Unit EIC Code"
    type: string
    unit: "-"
    description: The EIC code of the affected asset or unit.
  - name: MeasurementUnit
    title: "Unit of measurement"
    type: string
    unit: "-"
    description: >
      The unit in which the outage volume and the capacities are given.
  - name: UnavailableCapacity
    title: "Unavailable Capacity"
    type: float
    unit: "-"
    description: The volume of the outage that is currently unavailable.
  - name: AvailableCapacity
    title: "Available Capacity"
    type: float
    unit: "-"
    description: >
      The currently available capacity. Installed Capacity - Unavailable Capacity.
  - name: InstalledCapacity
    title: "Installed Capacity"
    type: float
    unit: "-"
    description: The total installed capacity that is originally available.
  - name: UnavailabilityReason
    title: "Reason of Unavailability"
    type: string
    unit: "-"
    description: The exact reason of the unavailability.
  - name: EventStart
    title: "Event Start"
    type: datetime
    unit: CET
    description: Start time of the outage specified in the publication.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: EventStop
    title: "Event Stop"
    type: datetime
    unit: CET
    description: End time of the outage specified in the publication.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Remarks
    title: "Remarks"
    type: string
    unit: "-"
    description: Additional remarks of the publication.
  - name: FuelType
    title: "Fuel Type"
    type: string
    unit: "-"
    description: >
      The fuel type used in the power plant for electricity generation.
  - name: BiddingZone
    title: "Bidding Zone"
    type: string
    unit: "-"
    description: Name of the bidding zone for the publication.

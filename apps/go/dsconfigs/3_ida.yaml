name: IDA_QuarterHourly
apiVersions:
  - v1
database: HUPX_BOSS_IDA
batchsize: 1000
frequencyCron: "0 11 * * *"  # The hour is in UTC
timeoutSeconds: 600
dataViewUrlName: Aggregated trading data
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2020-01-01'''
  - name: Public
    filter:
      - '"DeliveryDay" > now() - interval ''5 years'''
    limit: 10
defaultOrder:
  - key: DeliveryDay
    order: desc
  - key: ProductQH
    order: desc
query: >
  USE [HUPX_BOSS_IDA]
  DECLARE @Results TABLE (
    DeliveryDay date,
    AuctionType int,
    [type] varchar(10),
    QH1 decimal(38,18),
    QH2 decimal(38,18),
    QH3 decimal(38,18),
    QH4 decimal(38,18),
    QH5 decimal(38,18),
    QH6 decimal(38,18),
    QH7 decimal(38,18),
    QH8 decimal(38,18),
    QH9 decimal(38,18),
    QH10 decimal(38,18),
    QH11 decimal(38,18),
    QH12 decimal(38,18),
    QH9B decimal(38,18),
    QH10B decimal(38,18),
    QH11B decimal(38,18),
    QH12B decimal(38,18),
    QH13 decimal(38,18),
    QH14 decimal(38,18),
    QH15 decimal(38,18),
    QH16 decimal(38,18),
    QH17 decimal(38,18),
    QH18 decimal(38,18),
    QH19 decimal(38,18),
    QH20 decimal(38,18),
    QH21 decimal(38,18),
    QH22 decimal(38,18),
    QH23 decimal(38,18),
    QH24 decimal(38,18),
    QH25 decimal(38,18),
    QH26 decimal(38,18),
    QH27 decimal(38,18),
    QH28 decimal(38,18),
    QH29 decimal(38,18),
    QH30 decimal(38,18),
    QH31 decimal(38,18),
    QH32 decimal(38,18),
    QH33 decimal(38,18),
    QH34 decimal(38,18),
    QH35 decimal(38,18),
    QH36 decimal(38,18),
    QH37 decimal(38,18),
    QH38 decimal(38,18),
    QH39 decimal(38,18),
    QH40 decimal(38,18),
    QH41 decimal(38,18),
    QH42 decimal(38,18),
    QH43 decimal(38,18),
    QH44 decimal(38,18),
    QH45 decimal(38,18),
    QH46 decimal(38,18),
    QH47 decimal(38,18),
    QH48 decimal(38,18),
    QH49 decimal(38,18),
    QH50 decimal(38,18),
    QH51 decimal(38,18),
    QH52 decimal(38,18),
    QH53 decimal(38,18),
    QH54 decimal(38,18),
    QH55 decimal(38,18),
    QH56 decimal(38,18),
    QH57 decimal(38,18),
    QH58 decimal(38,18),
    QH59 decimal(38,18),
    QH60 decimal(38,18),
    QH61 decimal(38,18),
    QH62 decimal(38,18),
    QH63 decimal(38,18),
    QH64 decimal(38,18),
    QH65 decimal(38,18),
    QH66 decimal(38,18),
    QH67 decimal(38,18),
    QH68 decimal(38,18),
    QH69 decimal(38,18),
    QH70 decimal(38,18),
    QH71 decimal(38,18),
    QH72 decimal(38,18),
    QH73 decimal(38,18),
    QH74 decimal(38,18),
    QH75 decimal(38,18),
    QH76 decimal(38,18),
    QH77 decimal(38,18),
    QH78 decimal(38,18),
    QH79 decimal(38,18),
    QH80 decimal(38,18),
    QH81 decimal(38,18),
    QH82 decimal(38,18),
    QH83 decimal(38,18),
    QH84 decimal(38,18),
    QH85 decimal(38,18),
    QH86 decimal(38,18),
    QH87 decimal(38,18),
    QH88 decimal(38,18),
    QH89 decimal(38,18),
    QH90 decimal(38,18),
    QH91 decimal(38,18),
    QH92 decimal(38,18),
    QH93 decimal(38,18),
    QH94 decimal(38,18),
    QH95 decimal(38,18),
    QH96 decimal(38,18),
    Base decimal(38,18),
    Peak decimal(38,18),
    OffP1 decimal(38,18),
    OffP2 decimal(38,18),
    OffPeak decimal(38,18),
    MNight decimal(38,18),
    Night decimal(38,18),
    EMorning decimal(38,18),
    Morning decimal(38,18),
    LMorning decimal(38,18),
    Business decimal(38,18),
    HNoon decimal(38,18),
    EAftern decimal(38,18),
    Aftern decimal(38,18),
    Rush decimal(38,18),
    Evening decimal(38,18)
  )
  INSERT INTO @Results (DeliveryDay, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH9B, QH10B, QH11B, QH12B, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96)
  EXEC [dbo].[spHPXIdaReportEpexVolumeYearly] 2025, 1
  UPDATE @Results
  SET AuctionType = 1 WHERE AuctionType is NULL
  INSERT INTO @Results (DeliveryDay, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH9B, QH10B, QH11B, QH12B, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96)
  EXEC [dbo].[spHPXIdaReportEpexVolumeYearly] 2025, 2
  UPDATE @Results
  SET AuctionType = 2 WHERE AuctionType is NULL
  INSERT INTO @Results (DeliveryDay, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH9B, QH10B, QH11B, QH12B, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96)
  EXEC [dbo].[spHPXIdaReportEpexVolumeYearly] 2025, 3
  UPDATE @Results
  SET AuctionType = 3 WHERE AuctionType is NULL
  UPDATE @Results
  SET [type] = 'Volumes' WHERE [type] is NULL
  INSERT INTO @Results (DeliveryDay, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH9B, QH10B, QH11B, QH12B, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96, Base, Peak, OffP1, OffP2, OffPeak, MNight, Night, EMorning, Morning, LMorning, Business, HNoon, EAftern, Aftern, Rush, Evening)
  EXEC [dbo].[spHPXIdaReportEpexPriceYearly] 2025, 1
  UPDATE @Results
  SET AuctionType = 1 WHERE AuctionType is NULL
  INSERT INTO @Results (DeliveryDay, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH9B, QH10B, QH11B, QH12B, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96, Base, Peak, OffP1, OffP2, OffPeak, MNight, Night, EMorning, Morning, LMorning, Business, HNoon, EAftern, Aftern, Rush, Evening)
  EXEC [dbo].[spHPXIdaReportEpexPriceYearly] 2025, 2
  UPDATE @Results
  SET AuctionType = 2 WHERE AuctionType is NULL
  INSERT INTO @Results (DeliveryDay, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH9B, QH10B, QH11B, QH12B, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96, Base, Peak, OffP1, OffP2, OffPeak, MNight, Night, EMorning, Morning, LMorning, Business, HNoon, EAftern, Aftern, Rush, Evening)
  EXEC [dbo].[spHPXIdaReportEpexPriceYearly] 2025, 3
  UPDATE @Results
  SET AuctionType = 3 WHERE AuctionType is NULL
  UPDATE @Results
  SET [type] = 'Prices' WHERE [type] is NULL
  SELECT DeliveryDay,
         SUBSTRING(QH, 3, LEN(QH)) as ProductQH,
         MAX(CASE WHEN [type] = 'Prices' AND AuctionType = 1 THEN value END) as PriceIDA1,
         MAX(CASE WHEN [type] = 'Volumes' AND AuctionType = 1 THEN value END) as VolumeIDA1,
         MAX(CASE WHEN [type] = 'Prices' AND AuctionType = 2 THEN value END) as PriceIDA2,
         MAX(CASE WHEN [type] = 'Volumes' AND AuctionType = 2 THEN value END) as VolumeIDA2,
         MAX(CASE WHEN [type] = 'Prices' AND AuctionType = 3 THEN value END) as PriceIDA3,
         MAX(CASE WHEN [type] = 'Volumes' AND AuctionType = 3 THEN value END) as VolumeIDA3
  FROM (
      SELECT DeliveryDay, [type], AuctionType, QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96
      FROM @Results
      WHERE [type] IN ('Prices', 'Volumes') AND DeliveryDay > '%v' AND DeliveryDay <= CAST(GETDATE() as date) 
  ) src
  UNPIVOT (
      value FOR QH IN (QH1, QH2, QH3, QH4, QH5, QH6, QH7, QH8, QH9, QH10, QH11, QH12, QH13, QH14, QH15, QH16, QH17, QH18, QH19, QH20, QH21, QH22, QH23, QH24, QH25, QH26, QH27, QH28, QH29, QH30, QH31, QH32, QH33, QH34, QH35, QH36, QH37, QH38, QH39, QH40, QH41, QH42, QH43, QH44, QH45, QH46, QH47, QH48, QH49, QH50, QH51, QH52, QH53, QH54, QH55, QH56, QH57, QH58, QH59, QH60, QH61, QH62, QH63, QH64, QH65, QH66, QH67, QH68, QH69, QH70, QH71, QH72, QH73, QH74, QH75, QH76, QH77, QH78, QH79, QH80, QH81, QH82, QH83, QH84, QH85, QH86, QH87, QH88, QH89, QH90, QH91, QH92, QH93, QH94, QH95, QH96)
  ) as unpvt
  GROUP BY DeliveryDay, QH
  ORDER BY DeliveryDay DESC, CAST(SUBSTRING(QH, 3, LEN(QH)) as int) ASC
queryFilter:
  defaultValue: "2024-06-14"
  query: 'SELECT "DeliveryDay"::timestamp FROM hupx_boss_ida_ida_quarterhourly ORDER BY "DeliveryDay" DESC LIMIT 1'
  key: DeliveryDay
tags:
  - Intraday Auction Market
summary: "IDA - Summary"
description: >
  The aggregated trading data from the HUPX Intraday Auction Market (IDA)
  provides market results for all IDA auctions, covering all products for a
  specific delivery day. This dataset focuses on the settlement prices and
  traded volumes for each product across the three auctions.
  
  Currently, the dataset is updated for all auctions after the completion of
  the IDA3 auction, between 15:30 and 16:00. In the coming months, we plan to
  increase the update frequency so that users can access the data immediately
  after each IDA auction concludes.
plot:
  x: DeliveryDay
  y: Price_1
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: ProductQH
    title: "Quarter hour"
    type: int
    unit: CET
    description: >
      Indicates the quarter hour when the product is delivered for the
      specific delivery day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: PriceIDA1
    title: "IDA1 price"
    type: float
    unit: €/MWh
    description: Price of the IDA1 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeIDA1
    title: "IDA1 volume"
    type: float
    unit: MWh
    description: Traded volume of the IDA1 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: PriceIDA2
    title: "IDA2 price"
    type: float
    unit: €/MWh
    description: Price of the IDA2 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeIDA2
    title: "IDA2 volume"
    type: float
    unit: MWh
    description: Traded volume of the IDA2 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: PriceIDA3
    title: "IDA3 price"
    type: float
    unit: €/MWh
    description: Price of the IDA3 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: VolumeIDA3
    title: "IDA3 volume"
    type: float
    unit: MWh
    description: Traded volume of the IDA3 auction for the given quarter hour.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte

name: IDA_MC_Flows
apiVersions:
  - v1
database: HUPX_BOSS_IDA
batchsize: 1000
frequencyCron: "00 11 * * *"  # The hour is in UTC
timeoutSeconds: 600
dataViewUrlName: MC flows
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2020-01-01'''
  - name: Public
    filter:
      - '"DeliveryDay" > now() - interval ''5 years'''
    limit: 10
defaultOrder:
  - key: DeliveryDay
    order: desc
  - key: QuarterHour
    order: desc
  - key: AuctionID
    order: desc
query: >
  USE [HUPX_BOSS_IDA]

  DECLARE @filter_date datetime
  SET @filter_date = '%v'

  DECLARE @Results TABLE (
    AuctionTypeId int,
    CountryCode varchar(5),
    DeliveryDay date,
    Instrument varchar(5),
    InstrumentOrder int,
    Volume decimal(38,18)
  )

  INSERT INTO @Results
  EXEC [dbo].[spHPXIdaReportMcFlowYearly] 2024

  INSERT INTO @Results
  EXEC [dbo].[spHPXIdaReportMcFlowYearly] 2025

  SELECT
  	DeliveryDay,
  	QuarterHour,
  	AuctionTypeId as AuctionID,
  	[HU_AT],
  	[AT_HU],
  	[HU_HR],
  	[HR_HU],
  	[HU_SI],
  	[SI_HU],
  	[HU_SK],
  	[SK_HU]
  FROM (
    SELECT REPLACE(CountryCode, '-', '_') as CountryCode,
      Volume,
      DeliveryDay,
      SUBSTRING(Instrument, 3, LEN(Instrument)) as [QuarterHour],
      AuctionTypeId
    FROM @Results
    WHERE DeliveryDay > @filter_date
  ) AS SourceTable
  PIVOT (
    MAX(Volume) FOR CountryCode IN ([HU_AT], [AT_HU], [HU_HR], [HR_HU], [HU_SI], [SI_HU], [HU_SK], [SK_HU])
  ) AS PivotTable
  ORDER BY DeliveryDay, CAST(REPLACE(SUBSTRING([QuarterHour], 1, PATINDEX('%[B]%', [QuarterHour] + 'B') - 1), 'QH', '') as int), AuctionTypeId

queryFilter:
  defaultValue: "2024-01-01"
  query: 'SELECT "DeliveryDay" FROM hupx_boss_ida_ida_mc_flows ORDER BY "DeliveryDay" DESC LIMIT 1'
  key: DeliveryDay
tags:
  - Intraday Auction Market
summary: "IDA IC Flows - Summary"
description: >
  The Market Coupling (MC) Flow dataset provides details on cross-border flows 
  scheduled for a specific delivery day in the HUPX Intraday Auction market. 
  It includes the cross-zonal flows, categorized by all the currently available 
  borders in both directions.
  
  Currently, the dataset is updated for all auctions after the completion of 
  the IDA3 auction, between 15:30 and 16:00. In the coming months, 
  we plan to increase the update frequency so that users can access 
  the data immediately after each IDA auction concludes.
plot:
  x: DeliveryDay
  y: HU_AT
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: QuarterHour
    title: "Quarter Hour"
    type: string
    unit: CET
    description: >
      Indicates the relevant delivery quarter hour of the contracts for a specific day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: AuctionID
    title: "Auction ID"
    type: int
    unit: "-"
    description: The number of the IDA auction.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: HU_AT
    title: "HU>AT"
    type: float
    unit: "MW"
    description: HU > AT direction flow.
  - name: AT_HU
    title: "AT>HU"
    type: float
    unit: "MW"
    description: AT > HU direction flow.
  - name: HU_HR
    title: "HU>HR"
    type: float
    unit: "MW"
    description: HU > HR direction flow.
  - name: HR_HU
    title: "HR>HU"
    type: float
    unit: "MW"
    description: HR > HU direction flow.
  - name: HU_SI
    title: "HU>SI"
    type: float
    unit: "MW"
    description: HU > SI direction flow.
  - name: SI_HU
    title: "SI>HU"
    type: float
    unit: "MW"
    description: SI > HU direction flow.
  - name: HU_SK
    title: "HU>SK"
    type: float
    unit: "MW"
    description: HU > SK direction flow.
  - name: SK_HU
    title: "SK>HU"
    type: float
    unit: "MW"
    description: SK > HU direction flow.

name: DAM_Aggregated_Trading_Data
apiVersions:
  - v1
database: HUPX_BOSS_DAM
batchsize: 1000
frequencyCron: "0 13 * * *"  # The hour is in UTC
timeoutSeconds: 600
dataViewUrlName: Aggregated trading data
subscriptions:
  - name: Full
    filter:
      - '"DeliveryDay" > ''2024-01-01'''
  - name: Public
    filter:
      - '"DeliveryDay" > now() - interval ''1 months'''
    limit: 10
defaultOrder:
  - key: DeliveryDay
    order: desc
query: USE [HUPX_BOSS_DAM]
  DECLARE @Results TABLE (
  Date_Code date,
  type varchar(10),
  H1 decimal(38,18),
  H2 decimal(38,18),
  H3 decimal(38,18),
  H3B decimal(38,18),
  H4 decimal(38,18),
  H5 decimal(38,18),
  H6 decimal(38,18),
  H7 decimal(38,18),
  H8 decimal(38,18),
  H9 decimal(38,18),
  H10 decimal(38,18),
  H11 decimal(38,18),
  H12 decimal(38,18),
  H13 decimal(38,18),
  H14 decimal(38,18),
  H15 decimal(38,18),
  H16 decimal(38,18),
  H17 decimal(38,18),
  H18 decimal(38,18),
  H19 decimal(38,18),
  H20 decimal(38,18),
  H21 decimal(38,18),
  H22 decimal(38,18),
  H23 decimal(38,18),
  H24 decimal(38,18),
  [01-24] decimal(38,18),
  [09-20] decimal(38,18),
  [01-08] decimal(38,18),
  [21-24] decimal(38,18),
  [01-08-21-24] decimal(38,18),
  [01-04] decimal(38,18),
  [01-06] decimal(38,18),
  [05-08] decimal(38,18),
  [07-10] decimal(38,18),
  [09-12] decimal(38,18),
  [09-16] decimal(38,18),
  [11-14] decimal(38,18),
  [13-16] decimal(38,18),
  [15-18] decimal(38,18),
  [17-20] decimal(38,18),
  [19-24] decimal(38,18)
  )
  INSERT INTO @Results
  EXEC [dbo].[spHPXDamReportEpexPriceVolume] 2025
  SELECT "DeliveryDay",
  SUBSTRING(Hours, 2, LEN(Hours)) as "ProductH",
  MAX(CASE WHEN [type] = 'Prices' THEN value END) as "Price",
  MAX(CASE WHEN [type] = 'Volumes' THEN value END) as "Volume",
  MAX(CASE WHEN [type] = 'Prices' THEN [01-24] END) as "BaseloadPrice"
  FROM (
  SELECT Date_Code as "DeliveryDay", [type], H1, H2, H3, H4, H5, H6, H7, H8, H9, H10, H11, H12, H13, H14, H15, H16, H17, H18, H19, H20, H21, H22, H23, H24, [01-24]
  FROM @Results
  WHERE [type] IN ('Prices', 'Volumes')
  AND Date_Code > '%v'
  ) src
  UNPIVOT (
  value FOR Hours IN (H1, H2, H3, H4, H5, H6, H7, H8, H9, H10, H11, H12, H13, H14, H15, H16, H17, H18, H19, H20, H21, H22, H23, H24)
  ) as unpvt
  GROUP BY "DeliveryDay", "Hours"
  ORDER BY "DeliveryDay" DESC, CAST(SUBSTRING(Hours, 2, LEN(Hours)) as int) DESC

queryFilter:
  defaultValue: "2024-01-01"
  query: 'SELECT "DeliveryDay" FROM hupx_boss_dam_dam_aggregated_trading_data ORDER BY "DeliveryDay" DESC LIMIT 1'
  key: DeliveryDay
tags:
  - Day-Ahead Market
summary: "DAM - Summary"
description: >
  The aggregated trading data from the HUPX Day-Ahead Market (DAM) provides
  market results for the Day-Ahead auction, covering all products for a
  specific delivery day. The dataset includes traded volume, settlement prices,
  and the daily baseload price for all available linear products. It is updated
  daily, between 13:00 and 14:00, after the close of each Day-Ahead auction.
plot:
  x: DeliveryDay
  y: Price
  group: side
  type: scatter
fields:
  - name: DeliveryDay
    title: "Delivery day"
    type: date
    unit: CET
    description: Indicates the relevant delivery day of the contracts.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: ProductH
    title: "Hour"
    type: int
    unit: CET
    description: >
      Indicates the relevant delivery hour of the contracts for the specific
      delivery day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Price
    title: "Price"
    type: float
    unit: €/MWh
    description: The price of the product.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: Volume
    title: "Traded volume"
    type: float
    unit: MWh
    description: Traded volume of the product.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte
  - name: BaseloadPrice
    title: "Baseload price"
    type: float
    unit: €/MWh
    description: >
      Baseload price for the given day. This field has the same value for all
      the products traded on the same day.
    filters:
      - eq
      - lt
      - lte
      - gt
      - gte

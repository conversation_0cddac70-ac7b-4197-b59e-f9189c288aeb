name: hupx

services:
  npm_installer:
    restart: "no"
    network_mode: "host"
    image: node:22.13.1-alpine3.20
    working_dir: /src
    command:
      [
        "sh",
        "-cx",
        "npm i -g pnpm@9.6.0 && pnpm i && chmod -R 777 .pnpm-store && find . -name 'node_modules' -type d -prune -exec chmod -R 777 '{}' + && pnpm build",
      ]
    volumes:
      - ./:/src:rw

  website:
    restart: always
    image: node:22.13.1-alpine3.20
    ports:
      - 8000:80
    working_dir: /src
    command: ["sh", "-cx", "cd /src/apps/website && npm run dev-start"]
    environment:
      - PORT=80
      - INTERNAL_API_URL=http://api_server:80
      - POSTGRES_URL=${POSTGRES_URL:?Please provide POSTGRES_URL env variable for website}
      - SESSION_SECRET=${SESSION_SECRET:?Please provide SESSION_SECRET env variable for website}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY:?Please provide INTERNAL_API_KEY env variable for website}
      - MS_AUTH_REDIRECT_URI=${MS_AUTH_REDIRECT_URI:?Please provide MS_AUTH_REDIRECT_URI env variable for website}
      - MS_AUTH_TENANT_ID=${MS_AUTH_TENANT_ID:?Please provide MS_AUTH_TENANT_ID env variable for website}
      - MS_AUTH_CLIENT_ID=${MS_AUTH_CLIENT_ID:?Please provide MS_AUTH_CLIENT_ID env variable for website}
      - MS_AUTH_CLIENT_SECRET=${MS_AUTH_CLIENT_SECRET:?Please provide MS_AUTH_CLIENT_SECRET env variable for website}
      - MS_AUTH_HUPX_ADMIN=${MS_AUTH_HUPX_ADMIN:?Please provide MS_AUTH_HUPX_ADMIN env variable for website}
      - MS_AUTH_HUPX_SUPERADMIN=${MS_AUTH_HUPX_SUPERADMIN:?Please provide MS_AUTH_HUPX_SUPERADMIN env variable for website}
      - MS_AUTH_HUPX_USER=${MS_AUTH_HUPX_USER:?Please provide MS_AUTH_HUPX_USER env variable for website}
      - ZEPTOMAIL_API_KEY=${ZEPTOMAIL_API_KEY:?Please provide ZEPTOMAIL_API_KEY env variable for website}
      - PUBLIC_URL=https://dev-labs.hupx.hu
      - NODE_ENV=development
      - CRM_API_KEY=${CRM_API_KEY:?Please provide CRM_API_KEY env variable for website}
    volumes:
      - ./:/src:rw
    depends_on:
      npm_installer:
        condition: service_completed_successfully
      # postgres:
      #   condition: service_healthy

  go_installer:
    working_dir: /src
    image: mcr.microsoft.com/devcontainers/go:1-1.22-bookworm
    command: ["sh", "-cx", "cd ./apps/go && go mod tidy && go mod vendor"]
    volumes:
      - ./:/src:rw

  api_server:
    restart: always
    ports:
      - 8001:80
    working_dir: /src
    build:
      context: ./apps/go
      dockerfile: Dockerfile.dev
    command: ["sh", "-cx", "cd ./apps/go && air -c ./.air.api_server.toml"]
    volumes:
      - ./:/src:rw
    depends_on:
      go_installer:
        condition: service_completed_successfully
    environment:
      - PORT=80
      - PGDB_HUPX_MIRROR_URL=${PGDB_HUPX_MIRROR_URL:?Please provide PGDB_HUPX_MIRROR_URL env variable for API server}
      - PGDB_WEBSITE_DB_URL=${PGDB_WEBSITE_DB_URL:?Please provide PGDB_WEBSITE_DB_URL env variable for API server}
      - PUBLIC_DOMAIN=dev-labs.hupx.hu
      - INTERNAL_API_KEY=${INTERNAL_API_KEY:?Please provide INTERNAL_API_KEY env variable for API server}

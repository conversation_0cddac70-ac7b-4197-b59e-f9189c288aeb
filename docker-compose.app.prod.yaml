name: hupx

services:
  npm_installer:
    restart: "no"
    network_mode: "host"
    image: node:22.13.1-alpine3.20
    working_dir: /src
    command:
      [
        "sh",
        "-cx",
        "npm i -g pnpm@9.6.0 && pnpm i && chmod -R 777 .pnpm-store && find . -name 'node_modules' -type d -prune -exec chmod -R 777 '{}' + && pnpm build",
      ]
    volumes:
      - ./:/src:rw

  website:
    restart: always
    image: node:22.13.1-alpine3.20
    ports:
      - 8000:80
    working_dir: /src
    command: ["sh", "-cx", "cd /src/apps/website && npm run start"]
    environment:
      - PORT=80
      - INTERNAL_API_URL=http://api_server:80
      - POSTGRES_URL=${POSTGRES_URL:?Please set POSTGRES_URL environment variable for the website DB}
      - SESSION_SECRET=${SESSION_SECRET:?Please set SESSION_SECRET environment variable for the website session}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY:?Please set INTERNAL_API_KEY environment variable for the website API key}
      - MS_AUTH_REDIRECT_URI=${MS_AUTH_REDIRECT_URI:?Please set MS_AUTH_REDIRECT_URI environment variable for the website MS auth redirect URI}
      - MS_AUTH_TENANT_ID=${MS_AUTH_TENANT_ID:?Please set MS_AUTH_TENANT_ID environment variable for the website MS auth tenant ID}
      - MS_AUTH_CLIENT_ID=${MS_AUTH_CLIENT_ID:?Please set MS_AUTH_CLIENT_ID environment variable for the website MS auth client ID}
      - MS_AUTH_CLIENT_SECRET=${MS_AUTH_CLIENT_SECRET:?Please set MS_AUTH_CLIENT_SECRET environment variable for the website MS auth client secret}
      - MS_AUTH_HUPX_ADMIN=${MS_AUTH_HUPX_ADMIN:?Please provide MS_AUTH_HUPX_ADMIN env variable for website}
      - MS_AUTH_HUPX_SUPERADMIN=${MS_AUTH_HUPX_SUPERADMIN:?Please provide MS_AUTH_HUPX_SUPERADMIN env variable for website}
      - MS_AUTH_HUPX_USER=${MS_AUTH_HUPX_USER:?Please provide MS_AUTH_HUPX_USER env variable for website}
      - ZEPTOMAIL_API_KEY=${ZEPTOMAIL_API_KEY:?Please set ZEPTOMAIL_API_KEY environment variable for the website Zeptomail API key}
      - PUBLIC_URL=${PUBLIC_URL:?Please set PUBLIC_URL environment variable for the website public URL}
      - NODE_ENV=production
    volumes:
      - ./:/src:rw
    depends_on:
      npm_installer:
        condition: service_completed_successfully
    healthcheck:
      test:
        ["CMD", "busybox", "wget", "--spider", "http://localhost/healthcheck"]
      interval: 5s
      timeout: 10s
      retries: 3
      start_period: 20s
    logging:
      options:
        max-size: 50m

  go_installer:
    working_dir: /src
    image: mcr.microsoft.com/devcontainers/go:1-1.22-bookworm
    command: ["sh", "-cx", "cd ./apps/go && go mod tidy && go mod vendor"]
    volumes:
      - ./:/src:rw

  api_server:
    restart: always
    ports:
      - 8001:80
    working_dir: /src
    build:
      context: ./apps/go
      dockerfile: Dockerfile.dev
    command: ["sh", "-cx", "cd ./apps/go && air -c ./.air.api_server.toml"]
    volumes:
      - ./:/src:rw
    depends_on:
      go_installer:
        condition: service_completed_successfully
      website:
        condition: service_healthy
    environment:
      - PORT=80
      - PGDB_HUPX_MIRROR_URL=${PGDB_HUPX_MIRROR_URL:?Please set PGDB_HUPX_MIRROR_URL environment variable for the API server}
      - PGDB_WEBSITE_DB_URL=${PGDB_WEBSITE_DB_URL:?Please set PGDB_WEBSITE_DB_URL environment variable for the API server}
      - PUBLIC_DOMAIN=${PUBLIC_DOMAIN:?Please set PUBLIC_DOMAIN environment variable for the API server}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY:?Please set INTERNAL_API_KEY environment variable for the API server}
    logging:
      options:
        max-size: 50m
